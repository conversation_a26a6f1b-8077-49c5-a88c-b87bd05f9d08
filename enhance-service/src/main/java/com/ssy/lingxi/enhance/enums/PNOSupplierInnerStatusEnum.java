package com.ssy.lingxi.enhance.enums;

import com.ssy.lingxi.component.base.language.LanguageHolder;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 生产通知单供应商内部状态 - enum
 * <AUTHOR>
 * @since 2020/10/22
 * @version 2.0.0
 */
public enum PNOSupplierInnerStatusEnum {


    TO_BE_SUBMIT_EXAM(1, "待提交审核"),
    TO_BE_EXAM1(2, "待审核（一级）"),
    TO_BE_EXAM2(3, "待审核（二级）"),
    TO_BE_SUBMIT(4, "待提交"),
    ALREADY_SUBMIT(5, "已提交"),
    NOT_PASSED1(11, "审核不通过（一级）"),
    NOT_PASSED2(12, "审核不通过（二级）"),
    ALREADY_COMPLETE(21, "已完成"),
    TO_BE_ADD_STORAGE(22, "待新增加工入库单"),
    TO_BE_EXAM_STORAGE(23, "待审核加工入库单"),
    TO_BE_CONFIRM_RECEIVE(24, "待确认收货"),
    ALREADY_CONFIRM_RECEIVE(25, "已确认收货"),
    ALREADY_DISCONTINUE(26, "已中止"),
    INVALID_STATE(99, "无效状态");

    private Integer code;
    private String message;

    PNOSupplierInnerStatusEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 根据code获取message
     * */
    public static String getMessage(Integer code) {
        PNOSupplierInnerStatusEnum pnoSupplierInnerStatusEnum = Arrays.stream(PNOSupplierInnerStatusEnum.values()).filter(e -> e.getCode().equals(code)).findFirst().orElse(null);
        return pnoSupplierInnerStatusEnum != null ? pnoSupplierInnerStatusEnum.getMessage() : PNOSupplierInnerStatusEnum.INVALID_STATE.getMessage();
    }

    /**
     * 获取供应商待新增生产通知单列表状态集
     * */
    public static List<Integer> getSupplierToBeAddListStatus() {
        return Stream.of(
                PNOSupplierInnerStatusEnum.TO_BE_SUBMIT_EXAM.getCode(),
                PNOSupplierInnerStatusEnum.ALREADY_SUBMIT.getCode(),
                PNOSupplierInnerStatusEnum.NOT_PASSED1.getCode(),
                PNOSupplierInnerStatusEnum.NOT_PASSED2.getCode()
        ).collect(Collectors.toList());
    }

    /**
     * 获取所有
     * */
    public static List<Map<String, Object>> getAll() {
        return Arrays.stream(PNOSupplierInnerStatusEnum.values()).map(a -> {
            Map<String, Object> map = new HashMap<>();
            map.put("code", a.getCode());
            map.put("message", a.getMessage());
            return map;
        }).collect(Collectors.toList());
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return LanguageHolder.getTranslation(this.getClass(), this.message, this.code);
    }
}
