package com.ssy.lingxi.enhance.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 审核 - DTO
 * <AUTHOR>
 * @since 2020/10/21
 * @version 2.0.0
 */
@Data
public class ExamDTO {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空")
    private Long id;

    /**
     * 状态：0-审核不通过 1-审核通过
     */
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 原因
     */
    private String cause;

    /**
     * 发货信息（加工企业 - 待提交审核生产通知单 - 提交审核时, 不能为空）
     */
    private DeliveryMessage deliveryMessage;

    @Data
    public static class DeliveryMessage {

        /**
         * 发货地址id
         */
        private Long deliveryAddressId;

        /**
         * 发货地址
         */
        private String deliveryAddress;

        /**
         * 发货人姓名
         */
        private String deliveryUserName;

        /**
         * 发货人电话
         */
        private String deliveryUserTel;
    }
}
