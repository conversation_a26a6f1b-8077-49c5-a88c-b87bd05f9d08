package com.ssy.lingxi.enhance.service.process;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.enhance.model.dto.ExamDTO;
import com.ssy.lingxi.enhance.model.req.ProduceNoticeOrderReq;
import com.ssy.lingxi.enhance.model.resp.ProduceNoticeOrderListResp;

/**
 * 加工能力 - 确认生产通知单(加工企业) - 待审核生产通知单(一级) - 业务处理层
 * <AUTHOR>
 * @since 2020/10/20
 * @version 2.0.0
 */
public interface IProcessToBeFirstExamService {

    PageDataResp<ProduceNoticeOrderListResp> list(ProduceNoticeOrderReq qo, UserLoginCacheDTO user);

    void exam(ExamD<PERSON> dto, UserLoginCacheDTO user);

    void batchExamPass(CommonIdListReq dto, UserLoginCacheDTO user);

}
