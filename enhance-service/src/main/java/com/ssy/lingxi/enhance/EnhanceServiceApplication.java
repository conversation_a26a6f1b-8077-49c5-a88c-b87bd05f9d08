package com.ssy.lingxi.enhance;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;


/**
 * 加工服务启动类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/19
 */
@EnableAsync
@EnableAspectJAutoProxy(exposeProxy = true)
@EnableFeignClients(basePackages = {"com.ssy.lingxi.**.api.feign"})
@ComponentScan(basePackages = {
        "com.ssy.lingxi.component",
        "com.ssy.lingxi.**.api.fallback",
        "com.ssy.lingxi." + ServiceModuleConstant.ENHANCE + ".config",
        "com.ssy.lingxi." + ServiceModuleConstant.ENHANCE + ".controller",
        "com.ssy.lingxi." + ServiceModuleConstant.ENHANCE + ".entity",
        "com.ssy.lingxi." + ServiceModuleConstant.ENHANCE + ".handler",
        "com.ssy.lingxi." + ServiceModuleConstant.ENHANCE + ".repository",
        "com.ssy.lingxi." + ServiceModuleConstant.ENHANCE + ".serviceImpl",
})
@EnableDiscoveryClient
@SpringBootApplication
public class EnhanceServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(EnhanceServiceApplication.class, args);
    }

}
