package com.ssy.lingxi.enhance.model.dto;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;

/**
 * 新增生产通知单 - DTO
 * <AUTHOR>
 * @since 2020/10/22
 * @version 2.0.0
 */
@Data
public class AddProduceNoticeOrderDTO {

    /**
     * 外部工作流类型
     */
    @NotNull(message = "外部工作流类型不能为空")
    private Integer outerTaskType;

    /**
     * 通知单摘要
     */
    @NotBlank(message = "通知单摘要不能为空")
    private String summary;

    /**
     * 加工企业会员ID
     */
    @NotNull(message = "加工企业会员ID不能为空")
    private Long processMemberId;

    /**
     * 加工企业会员角色ID
     */
    @NotNull(message = "加工企业会员角色ID不能为空")
    private Long processRoleId;

    /**
     * 加工企业会员名称
     */
    @NotBlank(message = "加工企业会员名称不能为空")
    private String processName;

    /**
     * 通知单来源: 1-订单加工 2-商品加工
     */
    @NotNull(message = "通知单来源不能为空")
    private Integer source;

    /**
     * 交期
     */
    @NotNull(message = "交期不能为空")
    private Long deliveryDate;

    /**
     * 配送方式: 1-物流 2-自提
     */
    @NotNull(message = "配送方式不能为空")
    private Integer deliveryType;

    /**
     * 收货地址id
     */
    private Long receiverAddressId;

    /**
     * 收货地址
     */
    private String receiveAddress;

    /**
     * 收货人姓名
     */
    private String receiveUserName;

    /**
     * 收货人电话
     */
    private String receiveUserTel;

    /**
     * 通知单其他要求(交付说明, 付款说明, 税费说明, 物流说明, 包装说明, 其他说明, 附件名称, 附件地址)
     */
    private HashMap<String, Object> otherAsk;

    /**
     * 生产通知单明细
     */
    @Valid
    @NotEmpty(message = "生产通知单明细不能为空")
    private List<ProduceNoticeOrderDetailDTO> details;

    @Data
    public static class ProduceNoticeOrderDetailDTO {

        /**
         * 订单ID
         */
        private Long orderId;

        /**
         * 订单号
         */
        private String orderNo;

        /**
         * 订单详情ID
         */
        private Long orderDetailId;

        /**
         * 商品ID
         */
        @NotNull(message = "商品ID不能为空")
        private Long productId;

        /**
         * 商品名称
         */
        @NotBlank(message = "商品名称不能为空")
        private String productName;

        /**
         * 品类
         */
        @NotBlank(message = "品类不能为空")
        private String category;

        /**
         * 品牌
         */
        @NotBlank(message = "品牌不能为空")
        private String brand;

        /**
         * 单位
         */
        @NotBlank(message = "单位不能为空")
        private String unit;

        /**
         * 采购数量（订单数量）
         */
        private BigDecimal purchaseCount;

        /**
         * 剩余加工数量（一般来说不能小于0，如果要强行加工的话也可以正常操作）
         */
        private BigDecimal surplusProcessNum;

        /**
         * 加工数量
         */
        @NotNull(message = "加工数量不能为空")
        private BigDecimal processNum;

        /**
         * 是否含税
         */
        @NotNull(message = "是否含税不能为空")
        private Integer isHasTax;

        /**
         * 税率
         */
        @NotNull(message = "税率不能为空")
        private BigDecimal taxRate;

        /**
         * 加工单价
         */
        @NotNull(message = "加工单价不能为空")
        private BigDecimal processPrice;

        /**
         * 加工费
         */
        @NotNull(message = "加工费不能为空")
        private BigDecimal processTotalPrice;

        /**
         * 商品属性
         */
        @NotEmpty(message = "商品属性不能为空")
        private HashMap<String, Object> property;
    }
}
