package com.ssy.lingxi.enhance.repository;


import com.ssy.lingxi.enhance.entity.do_.ProduceNoticeOrderDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * 生产通知单 - 实体仓库
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/10/16
 */
public interface ProduceNoticeOrderRepository extends JpaRepository<ProduceNoticeOrderDO, Long>, JpaSpecificationExecutor<ProduceNoticeOrderDO> {

    List<ProduceNoticeOrderDO> findBySupplierMemberIdAndSupplierRoleIdAndProcessMemberIdAndProcessRoleId(Long supplierMemberId, Long supplierRoleId, Long processMemberId, Long processRoleId);
}
