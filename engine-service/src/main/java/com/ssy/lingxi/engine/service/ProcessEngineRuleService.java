package com.ssy.lingxi.engine.service;

import com.ssy.lingxi.common.model.req.engine.EngineRuleQueryReq;
import com.ssy.lingxi.common.model.resp.engine.ProcessEngineRuleResp;
import com.ssy.lingxi.engine.api.model.dto.ProcessEngineRuleDeleteDTO;
import com.ssy.lingxi.engine.api.model.dto.ProcessEngineRuleSaveDTO;
import com.ssy.lingxi.engine.api.model.dto.ProcessEngineRuleUpdateStateDTO;
import com.ssy.lingxi.engine.api.model.req.ProcessEngineRuleQueryReq;
import com.ssy.lingxi.engine.api.model.req.ProcessEngineRuleReq;

import java.util.List;

/**
 * 流程引擎规则服务
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-05-26
 */
public interface ProcessEngineRuleService {

    /**
     * 保存流程引擎规则
     * @param engineRule 流程引擎规则
     * @return Void
     */
    void saveEngineRule(ProcessEngineRuleSaveDTO engineRule);

    /**
     * 查询流程引擎规则
     * @param engineRuleQuery 流程引擎查询条件
     * @return List<ProcessEngineRuleBO>
     */
    List<ProcessEngineRuleReq> getEngineRule(ProcessEngineRuleQueryReq engineRuleQuery);

    /**
     * 查询流程引擎规则
     * @param engineRuleQueryReq 流程引擎规则查询条件
     * @return List<ProcessEngineRuleBO>
     */
    List<ProcessEngineRuleResp> getEngineRuleList(EngineRuleQueryReq engineRuleQueryReq);

    /**
     * 更新流程引擎规则状态
     * @param engineRuleUpdate 更新状态
     * @return Void
     */
    void updateEngineRuleState(ProcessEngineRuleUpdateStateDTO engineRuleUpdate);

    /**
     * 删除流程引擎规则
     * @param engineRuleDelete 流程引擎规则删除
     * @return Void
     */
    void deleteEngineRule(ProcessEngineRuleDeleteDTO engineRuleDelete);
}
