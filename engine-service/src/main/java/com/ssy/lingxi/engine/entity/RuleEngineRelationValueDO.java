package com.ssy.lingxi.engine.entity;

import com.ssy.lingxi.common.constant.TableNameConstant;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 规则引擎关联值
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-05-13 10:20
 **/
@Getter
@Setter
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_ENGINE_SERVICE + "relation_value")
public class RuleEngineRelationValueDO implements Serializable {

    private static final long serialVersionUID = -1841041416829720294L;

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 规则引擎Id
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long ruleEngineId;

    /**
     * 关联字段
     */
    @Column(columnDefinition = "varchar(30)", nullable = false)
    private String field;

    /**
     * 关联值
     */
    @Column(columnDefinition = "varchar(30)", nullable = false)
    private String value;

    /**
     * 创建时间
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long createTime = System.currentTimeMillis();
}
