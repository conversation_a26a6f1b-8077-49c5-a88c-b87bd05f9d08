package com.ssy.lingxi.engine.api.model.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 更新状态请求
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-05-13 10:20
 **/
@Data
public class DeleteReq implements Serializable {

    private static final long serialVersionUID = -2426944584629138163L;

    /**
     * 规则引擎类型：1-物料管理; 2-采购单流程; 3-合同管理; 4-合同协同; 5-请购单流程; 6-请款单管理;
     * 7-采购订单SRM; 8-售后管理B2B; 9-质量管理; 10-生命周期变更;
     * RuleEngineTypeEnum枚举类
     */
    @NotNull(message = "规则引擎类型不能为空")
    private Integer type;

    /**
     * 流程规则ID
     */
    @NotNull(message = "流程规则ID不能为空")
    private Long ruleId;

}
