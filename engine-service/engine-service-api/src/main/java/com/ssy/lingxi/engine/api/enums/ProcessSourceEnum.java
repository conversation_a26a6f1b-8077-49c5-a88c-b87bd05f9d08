package com.ssy.lingxi.engine.api.enums;

/**
 * 流程引擎来源枚举
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-05-25
 */
public enum ProcessSourceEnum {

    /**
     * 系统
     */
    SYSTEM(0, "系统"),

    /**
     * PAAS
     */
    PAAS(1, "PAAS"),;

    private final Integer code;

    private final String message;

    ProcessSourceEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
