package com.ssy.lingxi.commodity.entity.do_.shop;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ssy.lingxi.commodity.entity.do_.BaseDO;
import com.ssy.lingxi.common.constant.TableNameConstant;
import com.ssy.lingxi.component.base.enums.manage.ShopTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;

import java.io.Serializable;

/**
 * 商城
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldNameConstants
@TableName(schema = TableNameConstant.TABLE_SCHEMA, value = ShopDO.TABLE_NAME, autoResultMap = true)
public class ShopDO extends BaseDO implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 7750384373468840166L;
    /**
     * 表名
     */
    public static final String TABLE_NAME = TableNameConstant.TABLE_PRE_PRODUCT_SERVICE + "shop";

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 商城名称
     */
    private String name;

    /**
     * 商城类型
     * @see ShopTypeEnum
     */
    private Integer type;

    /**
     * 会员id
     */
    private Long memberId;

    /**
     * 会员名称
     */
    private String memberName;

    /**
     * 会员角色id
     */
    private Long memberRoleId;

    /**
     * 商城模型id
     * @see SelfShopModelDO#getId()
     */
    private Long selfShopModelId;

    /**
     * 商城环境:1.web 2.H5 3.小程序 4.APP
     */
    private Integer environment;

    /**
     * 商城属性: 1.B端商城 2.C端商城
     */
    private Integer property;

    /**
     * 是否为自营商城：false.否；true.是；
     */
    private Boolean isSelf;

    /**
     * 是否由会员来运营行情资讯门户：false.否；true.是；
     */
    private Boolean isMemberOperate;

    /**
     * 是否开放商城MRO搜索权限：false.否；true.是；
     */
    private Boolean isOpenMro;

    /**
     * 商城LOGO
     */
    private String logoUrl;

    /**
     * 商城描述
     */
    private String describe;

    /**
     * 启用状态：true启用 false禁用
     */
    private Boolean enabled;

    /**
     * 商城子域名
     */
    private String url;

    /**
     * 是否默认：false:否 true.是
     */
    private Boolean isDefault;

    /**
     * 币种
     */
    private Long currencyId;

    /**
     * 国家
     */
    private Long countryId;

    /**
     * 一对多单向关联语言
     */
    private Long languageId;

    /**
     * 帮助信息的开关
     */
    private Boolean helpInfoEnable;

}
