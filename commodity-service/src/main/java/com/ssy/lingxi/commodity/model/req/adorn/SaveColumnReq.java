package com.ssy.lingxi.commodity.model.req.adorn;

import com.ssy.lingxi.commodity.entity.bo.ColumnBO;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 保存导航栏
 * <AUTHOR>
 */
@Data
public class SaveColumnReq {

    /**
     * 装修ID
     */
    @NotNull(message = "装修ID不能为空")
    private Long adornId;

    /**
     * 导航栏
     */
    @NotEmpty(message = "导航栏不能为空")
    private List<ColumnBO> columnBOList;

}
