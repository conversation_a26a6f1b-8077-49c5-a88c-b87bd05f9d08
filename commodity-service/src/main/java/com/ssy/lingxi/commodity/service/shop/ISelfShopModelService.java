package com.ssy.lingxi.commodity.service.shop;

import com.ssy.lingxi.commodity.model.req.shop.AllocatedSelfShopModelIdListReq;
import com.ssy.lingxi.commodity.model.req.shop.AllocationSelfShopReq;
import com.ssy.lingxi.commodity.model.req.shop.EditSelfShopModelInfoReq;
import com.ssy.lingxi.commodity.model.req.shop.EnvironmentReq;
import com.ssy.lingxi.commodity.model.resp.shop.AllocatedSelfShopModelIdListResp;
import com.ssy.lingxi.commodity.model.resp.shop.SelfShopModelListResp;
import com.ssy.lingxi.common.model.resp.select.SelectLongResp;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ISelfShopModelService {

    /**
     * 自营商城模型列表
     */
    List<SelfShopModelListResp> shopModelList(EnvironmentReq req);

    /**
     * 分配自营商城
     * 分配自营商城给会员，即由自营商城模型派生个自营商城并分配给会员
     */
    Boolean allocationSelfShop(AllocationSelfShopReq allocationSelfShopReq);

    /**
     * 编辑商城信息
     */
    void editSelfShopModelInfo(EditSelfShopModelInfoReq editSelfShopModelInfoReq);

    /**
     * 会员已分配自营商城模型ID列表
     */
    AllocatedSelfShopModelIdListResp allocatedSelfShopModelIdList(AllocatedSelfShopModelIdListReq allocatedSelfShopModelIdListReq);

    /**
     * 自营商城模型列表
     */
    List<SelectLongResp> selfShopModelList();

}
