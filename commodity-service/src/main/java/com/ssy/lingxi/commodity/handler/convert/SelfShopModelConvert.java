package com.ssy.lingxi.commodity.handler.convert;

import com.ssy.lingxi.commodity.entity.do_.shop.SelfShopModelDO;
import com.ssy.lingxi.common.model.resp.select.SelectLongResp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SelfShopModelConvert {

    SelfShopModelConvert INSTANCE = Mappers.getMapper(SelfShopModelConvert.class);

    @Mappings({
            @Mapping(target = "value", source = "id"),
            @Mapping(target = "label", source = "name")
    })
    SelectLongResp toSelectResp(SelfShopModelDO selfShopModelDO);

}
