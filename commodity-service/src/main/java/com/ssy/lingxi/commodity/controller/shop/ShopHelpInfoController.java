package com.ssy.lingxi.commodity.controller.shop;

import com.ssy.lingxi.commodity.model.req.shop.ShopHelpInfoEnableReq;
import com.ssy.lingxi.commodity.model.req.shop.ShopHelpInfoSaveReq;
import com.ssy.lingxi.commodity.model.req.shop.ShopHelpInfoTreeSortReq;
import com.ssy.lingxi.commodity.model.req.shop.ShopHelpInfoUpdateReq;
import com.ssy.lingxi.commodity.model.resp.shop.ShopHelpInfoResp;
import com.ssy.lingxi.commodity.model.resp.shop.ShopHelpInfoTreeResp;
import com.ssy.lingxi.commodity.service.shop.IShopHelpInfoService;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 商城帮助信息
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ShopHelpInfoController.PATH_PREFIX)
public class ShopHelpInfoController extends BaseController {

    /**
     * 路径前缀
     */
    public static final String PATH_PREFIX = ServiceModuleConstant.COMMODITY_PATH_PREFIX + "/shopHelpInfo";

    private final IShopHelpInfoService shopHelpInfoService;

    /**
     * 开启商城帮助信息
     */
    @PostMapping("/helpInfoEnable")
    public WrapperResp<Void> helpInfoEnable(@RequestBody @Valid ShopHelpInfoEnableReq shopHelpInfoEnableReq) {
        UserLoginCacheDTO user = this.getSysUser();
        shopHelpInfoService.helpInfoEnable(user, shopHelpInfoEnableReq);
        return WrapperUtil.success();
    }

    /**
     * 查询是否开启商城帮助信息
     */
    @GetMapping("/helpInfoEnable")
    public WrapperResp<Boolean> helpInfoEnable(@RequestParam Long shopId) {
        return WrapperUtil.success(shopHelpInfoService.helpInfoEnable(shopId));
    }

    /**
     * 保存商城帮助信息
     */
    @PostMapping("/save")
    public WrapperResp<Void> save(@RequestBody @Valid ShopHelpInfoSaveReq shopHelpInfoSaveReq) {
        UserLoginCacheDTO user = this.getSysUser();
        shopHelpInfoService.save(user, shopHelpInfoSaveReq);
        return WrapperUtil.success();
    }

    /**
     * 查询商城信息详情
     *
     * @param id 商城帮助信息ID
     */
    @GetMapping("/detail")
    public WrapperResp<ShopHelpInfoResp> detail(@RequestParam Long id) {
        return WrapperUtil.success(shopHelpInfoService.detail(id));
    }

    /**
     * 删除商城帮助信息
     *
     * @param req 商城帮助信息ID
     */
    @PostMapping("/delete")
    public WrapperResp<Void> delete(@RequestBody @Valid CommonIdReq req) {
        UserLoginCacheDTO user = this.getSysUser();
        shopHelpInfoService.delete(user, req);
        return WrapperUtil.success();
    }

    /**
     * 更新商城帮助信息
     */
    @PostMapping("/update")
    public WrapperResp<Void> update(@RequestBody @Valid ShopHelpInfoUpdateReq shopHelpInfoUpdateReq) {
        UserLoginCacheDTO user = this.getSysUser();
        shopHelpInfoService.update(user, shopHelpInfoUpdateReq);
        return WrapperUtil.success();
    }

    /**
     * 查询商城帮助信息节点数
     *
     * @param shopId 商城ID
     */
    @GetMapping("/tree")
    public WrapperResp<List<ShopHelpInfoTreeResp>> tree(@RequestParam Long shopId) {
        return WrapperUtil.success(shopHelpInfoService.tree(shopId));
    }

    /**
     * 调整排序
     */
    @PostMapping("/sort")
    public WrapperResp<Void> sort(@RequestBody @Valid ShopHelpInfoTreeSortReq shopHelpInfoTreeSortReq) {
        UserLoginCacheDTO user = this.getSysUser();
        shopHelpInfoService.sort(user, shopHelpInfoTreeSortReq);
        return WrapperUtil.success();
    }

}
