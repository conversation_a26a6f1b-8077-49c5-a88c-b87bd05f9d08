package com.ssy.lingxi.commodity.model.req.adorn;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 广告
 * <AUTHOR>
 */
@Data
public class AdvertReq {

    /**
     * 装修ID
     */
    @NotNull(message = "装修ID不能为空")
    private Long adornId;

    /**
     * 广告类型: 1.一号广告 2.二号广告 3.三号广告 4.四号广告
     */
    @NotNull(message = "广告类型不能为空")
    private Integer type;

    /**
     * 品类ID 当广告类型为三号广告时才有ID值
     */
    private Long categoryId;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 角色ID
     */
    private Long roleId;
}
