package com.ssy.lingxi.commodity.controller.feign;

import com.ssy.lingxi.commodity.api.feign.IStoreFeign;
import com.ssy.lingxi.commodity.api.model.dto.*;
import com.ssy.lingxi.commodity.api.model.resp.MerchantLogoInnerResp;
import com.ssy.lingxi.commodity.api.model.resp.StoreInnerResp;
import com.ssy.lingxi.commodity.service.inner.IStoreInnerService;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 会员店铺 - 内部接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/12/09
 * @ignore 不需要提交到Yapi
 */
@RestController
public class StoreFeignController extends BaseController implements IStoreFeign {

    @Resource
    private IStoreInnerService storeInnerService;

    /**
     * 更新店铺（会员名称、注册年数、信用积分、满意度）
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @Override
    public WrapperResp<Void> updateShop(@RequestBody @Valid StoreDTO dto) {
        storeInnerService.updateShop(dto);
        return WrapperUtil.success();
    }

    /**
     * 更新店铺状态（可以是一个角色的禁用与启用，也可以是一个会员下的某个角色的禁用与启用）
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @Override
    public WrapperResp<Void> updateShopStatus(@RequestBody @Valid UpdateShopStatusDTO dto) {
        storeInnerService.updateShopStatus(dto);
        return WrapperUtil.success();
    }

    /**
     * 店铺列表
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @Override
    public WrapperResp<List<StoreInnerResp>> getStoreList(@RequestBody @Valid List<StoreListInnerDTO> dto) {
        return WrapperUtil.success(storeInnerService.storeList(dto));
    }

    /**
     * 根据商城ID获取商家LOGO
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @Override
    public WrapperResp<List<MerchantLogoInnerResp>> findMerchantLogoByShopId(@RequestBody @Valid MerchantLogoInnerDTO dto) {
        return WrapperUtil.success(storeInnerService.findMerchantLogoByShopId(dto));
    }

    /**
     * 根据店铺id列表查询店铺对象
     *
     * @param dto 入参
     * @return 店铺列表
     */
    @Override
    public WrapperResp<List<StoreInnerResp>> getStoreListByIds(@RequestBody @Valid StoreIdListInnerDTO dto) {
        return WrapperUtil.success(storeInnerService.getStoreListByIds(dto.getShopIds()));
    }

    /**
     * 根据店铺id查询店铺对象
     * @param commonIdReq 入参
     * @return 店铺列表
     */
    @Override
    public WrapperResp<StoreInnerResp> getStoreById(@RequestBody @Valid CommonIdReq commonIdReq) {
        return WrapperUtil.success(storeInnerService.getStoreById(commonIdReq.getId()));
    }
}
