package com.ssy.lingxi.commodity.service.adorn;

import com.ssy.lingxi.commodity.entity.bo.WebPlatformBO;
import com.ssy.lingxi.commodity.model.req.adorn.WebPlatformAdornReq;
import com.ssy.lingxi.commodity.model.req.web.RecommendDataReq;
import com.ssy.lingxi.commodity.model.resp.adorn.*;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;

import java.util.List;

/**
 * 装修 - WEB平台首页装修 - 业务处理层
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/04/20
 */
public interface IWebPlatformService {

    void save(WebPlatformAdornReq webPlatformAdornReq, UserLoginCacheDTO sysUser);

    List<WebPlatformBO> find(Long adornId);

    PageDataResp<CommodityResp> findCommodityList(RecommendDataReq dto);

    PageDataResp<BrandResp> findBrandList(RecommendDataReq dto);

    PageDataResp<StoreResp> findStoreList(RecommendDataReq dto);

    PageDataResp<LogisticsResp> findLogisticsList(RecommendDataReq dto);

    PageDataResp<ProcessResp> findProcessList(RecommendDataReq dto);
}
