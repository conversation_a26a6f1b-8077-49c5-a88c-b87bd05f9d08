package com.ssy.lingxi.commodity.api.feign;

import com.ssy.lingxi.commodity.api.fallback.ShopHotSearchTermManageFeignFallback;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
@FeignClient(value = ServiceModuleConstant.COMMODITY_SERVICE, fallback = ShopHotSearchTermManageFeignFallback.class)
public interface IShopHotSearchTermManageFeign {

    /**
     * 路径前缀
     */
    String PATH_PREFIX = ServiceModuleConstant.COMMODITY_FEIGN_PATH_PREFIX + "/shopHotSearchTerm/";

    /**
     * 修改热词搜索次数
     * @param name 热词
     */
    @PostMapping("/updateSearchCount")
    WrapperResp<Void> updateSearchCount(@RequestParam("name") String name);
}
