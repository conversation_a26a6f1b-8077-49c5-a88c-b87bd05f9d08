package com.ssy.lingxi.commodity.api.fallback.factory;

import com.ssy.lingxi.commodity.api.fallback.UnitFeignFallback;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class UnitFeignFallbackFactory implements FallbackFactory<UnitFeignFallback> {

    @Override
    public UnitFeignFallback create(Throwable cause) {
        return new UnitFeignFallback(cause);
    }

}
