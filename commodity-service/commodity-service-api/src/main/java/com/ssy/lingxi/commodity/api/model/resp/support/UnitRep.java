package com.ssy.lingxi.commodity.api.model.resp.support;

import com.ssy.lingxi.component.base.model.resp.TranslateResp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 单位实体类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UnitRep implements Serializable {
    private static final long serialVersionUID = -1548459561973635037L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 名称
     */
    private List<TranslateResp> unitNameList;

    /**
     * 状态
     */
    private Boolean status;
}
