package com.ssy.lingxi.commodity.api.model.resp.support;

import lombok.Data;

import java.io.Serializable;

/**
 * 国家(地区)
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/7/5
 */
@Data
public class CountryAreaResp implements Serializable {
    private static final long serialVersionUID = -7178773959215107277L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 国家(地区)代码
     */
    private String code;

    /**
     * 国家(地区)名称
     */
    private String name;

    /**
     * 国家(地区)英文名称
     */
    private String nameEn;

    /**
     * 电话代码
     */
    private String telCode;

    /**
     * 手机号位数
     */
    private Integer telLength;

    /**
     * 状态: 是否有效
     */
    private Boolean status;

    /**
     * 创建时间
     */
    private Long createTime;
}
