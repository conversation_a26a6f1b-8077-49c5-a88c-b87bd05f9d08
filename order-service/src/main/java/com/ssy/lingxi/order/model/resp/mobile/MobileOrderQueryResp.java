package com.ssy.lingxi.order.model.resp.mobile;

import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.product.CommoditySaleModeEnum;
import com.ssy.lingxi.order.model.dto.MobileOrderQueryDTO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * App - （基础VO）查询订单列表时返回的VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-08-02
 */
@Getter
@Setter
@NoArgsConstructor
public class MobileOrderQueryResp implements Serializable {
    private static final long serialVersionUID = -3665763135608368118L;

    public MobileOrderQueryResp(MobileOrderQueryDTO query) {
        this.orderId = query.getOrderId();
        this.orderNo = query.getOrderNo();
        this.orderMode = query.getOrderMode();
        this.vendorMemberId = query.getVendorMemberId();
        this.vendorRoleId = query.getVendorRoleId();
        this.vendorMemberName = query.getVendorMemberName();
        this.logo = query.getLogo();
        this.totalAmount = NumberUtil.formatAmount(query.getTotalAmount());
        this.quantities = NumberUtil.formatToInteger(query.getQuantities());
        this.innerStatus = query.getInnerStatus();
        this.innerStatusName = query.getInnerStatusName();
        this.outerStatus = query.getOuterStatus();
        this.outerStatusName = query.getOuterStatusName();
        this.shopId = query.getShopId();
        this.shopName = query.getShopName();
        this.storeId = query.getStoreId();
        this.storeName = query.getStoreName();
        this.storeLogo = query.getStoreLogo();
        this.products = query.getProducts().stream().map(MobileOrderProductQueryResp::new).collect(Collectors.toList());
        this.saleMode = query.getSaleMode();
        this.saleModeName = CommoditySaleModeEnum.getNameByCode(this.saleMode);
    }

    /**
     * 订单Id
     */
    private Long orderId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 下单模式，10-积分订单，11-渠道积分订单，其他（说明略）
     */
    private Integer orderMode;

    /**
     * 供应会员Id
     */
    private Long vendorMemberId;

    /**
     * 供应会员角色Id
     */
    private Long vendorRoleId;

    /**
     * 店铺名称
     */
    private String vendorMemberName;

    /**
     * 店铺logo
     */
    private String logo;

    /**
     * 订单总额
     */
    private String totalAmount;

    /**
     * 订单商品总数量
     */
    private String quantities;

    /**
     * 内部状态枚举
     */
    private Integer innerStatus;

    /**
     * 内部状态名称
     */
    private String innerStatusName;

    /**
     * 外部状态枚举
     */
    private Integer outerStatus;

    /**
     * 外部状态名称
     */
    private String outerStatusName;

    /**
     * 商城ID
     */
    private Long shopId;

    /**
     * 商城名称
     */
    private String shopName;

    /**
     * 店铺ID
     */
    private Long storeId;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 店铺logo
     */
    private String storeLogo;

    /**
     * 订单商品列表
     */
    private List<MobileOrderProductQueryResp> products;

    /**
     * 销售方式枚举，1-现货，2-订货
     * @see CommoditySaleModeEnum
     */
    private Integer saleMode;

    /**
     * 销售方式名称
     */
    private String saleModeName;
}
