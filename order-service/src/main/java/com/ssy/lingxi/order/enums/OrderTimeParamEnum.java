package com.ssy.lingxi.order.enums;

/**
 * 订单时间参数枚举
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-10-13
 */
public enum OrderTimeParamEnum {
    /**
     * 自动确认收货 - 1
     */
    AUTO_RECEIVE_DAY(1, "自动确认收货"),

    /**
     * 送货预约时长 - 2
     */
    DELIVERY_APPOINTMENT_DAY(2, "送货预约时长"),

    /**
     * 配送时间段 - 3
     */
    DELIVERY_TIME(3, "配送时间段");

    OrderTimeParamEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    private final Integer code;
    private final String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
