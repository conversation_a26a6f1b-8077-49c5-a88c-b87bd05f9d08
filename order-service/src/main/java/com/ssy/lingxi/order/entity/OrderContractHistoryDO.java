//package com.ssy.lingxi.order.entity;
//
//import com.fasterxml.jackson.annotation.JsonBackReference;
//import com.ssy.lingxi.common.constant.TableNameConstant;
//import com.ssy.lingxi.component.base.enums.contract.ContractSourceTypeEnum;
//import lombok.Getter;
//import lombok.NoArgsConstructor;
//import lombok.Setter;
//
//import javax.persistence.*;
//import java.io.Serializable;
//import java.math.BigDecimal;
//import java.time.LocalDateTime;
//
///**
// * 历史订单合同
// * <AUTHOR>
// * @version 2.0.0
// * @since 2021-07-16
// */
//@Getter
//@Setter
//@NoArgsConstructor
//@Entity
//@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_ORDER_SERVICE + "contract_history")
//public class OrderContractHistoryDO implements Serializable {
//    private static final long serialVersionUID = 2174681397836652035L;
//
//    /**
//     * 主键Id
//     */
//    @Id
//    @GeneratedValue(strategy = GenerationType.IDENTITY)
//    private Long id;
//
//    /**
//     * 一对一双向关联订单
//     */
//    @JsonBackReference
//    @OneToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
//    @JoinColumn(name="order_id", referencedColumnName="id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
//    private OrderHistoryDO order;
//
//    /**
//     * 历史Id
//     */
//    @Column
//    private Long historyId;
//
//    /**
//     * 合同Id
//     */
//    @Column
//    private Long contractId;
//
//    /**
//     * 合同编号
//     */
//    @Column(columnDefinition = "varchar(30)")
//    private String contractNo;
//
//    /**
//     * 合同摘要
//     */
//    @Column(columnDefinition = "varchar(50)")
//    private String digest;
//
//    /**
//     * 合同生效日期
//     */
//    @Column(columnDefinition = "timestamp")
//    private LocalDateTime effectDate;
//
//    /**
//     * 合同失效日期
//     */
//    @Column(columnDefinition = "timestamp")
//    private LocalDateTime expireDate;
//
//    /**
//     * 合同乙方
//     */
//    @Column(columnDefinition = "varchar(200)")
//    private String partB;
//
//    /**
//     * 合同寻源类型
//     * @see ContractSourceTypeEnum
//     */
//    @Column
//    private Integer contractType;
//
//    /**
//     * 合同剩余金额
//     */
//    @Column(columnDefinition = "numeric")
//    private BigDecimal leftAmount;
//
//    /**
//     * 对应单据
//     */
//    @Column(columnDefinition = "varchar(30)")
//    private String receiptNo;
//
//    /**
//     * 合同文件名称
//     */
//    @Column(columnDefinition = "varchar(255)")
//    private String fileName;
//
//    /**
//     * 合同文件Url
//     */
//    @Column(columnDefinition = "varchar(400)")
//    private String url;
//}
