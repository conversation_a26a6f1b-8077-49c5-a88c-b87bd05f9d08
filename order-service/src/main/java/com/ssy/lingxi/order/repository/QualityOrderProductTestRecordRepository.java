package com.ssy.lingxi.order.repository;

import com.ssy.lingxi.order.entity.QualityOrderProductTestRecordDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 质检单物料检验记录Jpa仓库
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/5/27
 */
@Repository
public interface QualityOrderProductTestRecordRepository extends JpaRepository<QualityOrderProductTestRecordDO, Long>, JpaSpecificationExecutor<QualityOrderProductTestRecordDO> {

    void deleteByIdIn(List<Long> ids);
}
