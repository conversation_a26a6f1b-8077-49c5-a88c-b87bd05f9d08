package com.ssy.lingxi.order.model.resp.buyer;


import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
*
 *
 * 8D整改单外部流转记录
*/
@Getter
@Setter
@NoArgsConstructor
public class ExternalEightLogResp {

    /**
     *  操作角色名字
     */
    private String roleName;

    /**
     * 外部流转状态
     */
    private Integer state;

    /**
     * 外部流转状态名称
     */
    private String stateName;

    /**
     * 操作
     */
    private Integer operation;

    /**
     * 操作名称
     */
    private String operationName;

    /**
     *审核意见
     */
    private String auditOpinion;

    /**
     * 创建时间&操作时间 时间戳
     */
    private Long createTime;
}
