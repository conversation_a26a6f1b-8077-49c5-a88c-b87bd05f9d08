package com.ssy.lingxi.order.handler.validator;

import com.ssy.lingxi.component.base.enums.manage.ShopTypeEnum;
import com.ssy.lingxi.order.handler.annotation.BuyerOrderShopTypeAnnotation;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.stream.Stream;

/**
 * 商城类型类型校验注解验证类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-17
 */
public class BuyerOrderShopTypeValidator implements ConstraintValidator<BuyerOrderShopTypeAnnotation, Integer> {
    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext context) {
        if(value == null) {
            return false;
        }

        return Stream.of(ShopTypeEnum.ENTERPRISE).anyMatch(e -> e.getCode().equals(value));
    }
}
