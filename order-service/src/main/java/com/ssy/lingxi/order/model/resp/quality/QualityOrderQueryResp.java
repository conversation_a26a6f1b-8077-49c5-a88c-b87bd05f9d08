package com.ssy.lingxi.order.model.resp.quality;

import com.ssy.lingxi.common.util.DateTimeUtil;
import com.ssy.lingxi.order.constant.QualityConstant;
import com.ssy.lingxi.order.entity.QualityOrderDO;
import com.ssy.lingxi.order.enums.QualityOrderResourceEnum;
import com.ssy.lingxi.order.enums.QualityOrderTypeEnum;
import com.ssy.lingxi.order.enums.QualityStatusEnum;
import com.ssy.lingxi.order.enums.QualityTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 质检单分页列表响应实体类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/5/26
 */
@Setter
@Getter
public class QualityOrderQueryResp implements Serializable {

    private static final long serialVersionUID = -4501590341894889503L;

    /**
     * 主键Id
     */
    private Long id;

    /**
     * 质检单类型：1-B2B  2-SRM
     */
    private Integer type;

    /**
     * 质检单生成类型：1-新增 2-收货单生成
     */
    private Integer orderResource;

    /**
     * 质检单编号
     */
    private String qualityNo;

    /**
     * 质检类型 1-来料质检,2-样品质检,3-试制品质检
     */
    private Integer qualityType;

    /**
     * 质检类型name 1-来料质检,2-样品质检,3-试制品质检
     */
    private String qualityTypeName;

    /**
     * 来源收货单编号
     */
    private String receiveNo;

    /**
     * 质检单摘要
     */
    private String digest;

    /**
     * 供应商会员名称
     */
    private String vendorMemberName;

    /**
     * 质检开始时间 yyyy-MM-dd
     */
    private String startTime;

    /**
     * 质检结束时间 yyyy-MM-dd
     */
    private String endTime;

    /**
     * 外部状态 1-质检中 2-质检完成 3-已生成售后单
     */
    private Integer outerStatus;

    /**
     * 外部状态name 1-质检中 2-质检完成 3-已生成售后单
     */
    private String outerStatusName;

    /**
     * 单据时间 yyyy-MM-dd HH:mm
     */
    private String createTime;

    /**
     * 是否展示生成售后单按钮
     */
    private Boolean generate = false;

    public QualityOrderQueryResp(QualityOrderDO qualityOrderDO) {
        this.id = qualityOrderDO.getId();
        this.type = qualityOrderDO.getType();
        this.orderResource = qualityOrderDO.getOrderResource();
        this.qualityNo = qualityOrderDO.getQualityNo();
        this.qualityType = qualityOrderDO.getQualityType();
        this.qualityTypeName = QualityTypeEnum.getMessage(qualityOrderDO.getQualityType());
        this.receiveNo = qualityOrderDO.getReceiveNo();
        this.digest = qualityOrderDO.getDigest();
        this.vendorMemberName = qualityOrderDO.getVendorMemberName();
        this.startTime = qualityOrderDO.getStartTime() == null ? "" : qualityOrderDO.getStartTime().format(QualityConstant.DEFAULT_DATE_FORMATTER);
        this.endTime = qualityOrderDO.getEndTime() == null ? "" : qualityOrderDO.getEndTime().format(QualityConstant.DEFAULT_DATE_FORMATTER);
        this.outerStatus = qualityOrderDO.getOuterStatus();
        this.outerStatusName = QualityStatusEnum.getMessage(qualityOrderDO.getOuterStatus());
        this.createTime = qualityOrderDO.getCreateTime() == null ? "" : DateTimeUtil.timeStamp2Date(qualityOrderDO.getCreateTime(), QualityConstant.DEFAULT_MINUTE_TIME_FORMATTER_STRING);
        //只有 收货单生成的质检单&& B2B类型 && 外部状态是质检完成
        if (QualityOrderResourceEnum.RECEIPT_GENERATION.getCode().equals(qualityOrderDO.getOrderResource()) && QualityOrderTypeEnum.B2B.getCode().equals(qualityOrderDO.getType()) && QualityStatusEnum.QUALITY_INSPECTION_COMPLETED.getCode().equals(qualityOrderDO.getOuterStatus())) {
            this.generate = Boolean.TRUE;
        }
    }

    public QualityOrderQueryResp() {

    }
}
