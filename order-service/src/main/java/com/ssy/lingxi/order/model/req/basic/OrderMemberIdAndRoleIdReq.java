package com.ssy.lingxi.order.model.req.basic;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;
import java.util.Objects;

/**
 * 会员Id和会员角色Id作为接口参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-26
 */
@Getter
@Setter
@NoArgsConstructor
public class OrderMemberIdAndRoleIdReq implements Serializable {
    private static final long serialVersionUID = 1342166139144350033L;

    /**
     * 会员Id
     */
    @NotNull(message = "会员Id要大于0")
    @Positive(message = "会员Id要大于0")
    private Long memberId;

    /**
     * 会员角色Id
     */
    @NotNull(message = "会员Id要大于0")
    @Positive(message = "会员Id要大于0")
    private Long roleId;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }

        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        OrderMemberIdAndRoleIdReq that = (OrderMemberIdAndRoleIdReq) o;
        return memberId.equals(that.memberId) &&
                roleId.equals(that.roleId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(memberId, roleId);
    }
}
