package com.ssy.lingxi.order.serviceImpl.web;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.ssy.lingxi.common.constant.RedisConstant;
import com.ssy.lingxi.common.enums.order.LogisticsStatusEnum;
import com.ssy.lingxi.common.enums.order.OrderDeliveryTypeEnum;
import com.ssy.lingxi.common.enums.product.InsuranceCompanyEnum;
import com.ssy.lingxi.common.model.dto.MemberAndRoleIdDTO;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.api.order.CommodityReq;
import com.ssy.lingxi.common.model.req.api.order.OrderProducerReq;
import com.ssy.lingxi.common.model.req.api.order.OrderSettleReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.BigDecimalUtil;
import com.ssy.lingxi.common.util.DateTimeUtil;
import com.ssy.lingxi.common.util.RandomNumberUtil;
import com.ssy.lingxi.component.base.config.BaiTaiMemberProperties;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.SfPaymentTypeEnum;
import com.ssy.lingxi.component.base.enums.order.OrderPayChannelEnum;
import com.ssy.lingxi.component.base.enums.order.OrderPayNodeEnum;
import com.ssy.lingxi.component.base.enums.order.OrderPayTypeEnum;
import com.ssy.lingxi.component.base.enums.order.SubPaymentOrderStatusEnum;
import com.ssy.lingxi.component.base.enums.product.CommoditySaleModeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.component.redis.service.IRedisUtils;
import com.ssy.lingxi.component.rest.config.ThirdPartyConfig;
import com.ssy.lingxi.component.rest.constant.EosApiParamConstant;
import com.ssy.lingxi.component.rest.model.req.eos.*;
import com.ssy.lingxi.component.rest.model.req.sf.CargoDetail;
import com.ssy.lingxi.component.rest.model.req.sf.ContactInfo;
import com.ssy.lingxi.component.rest.model.req.sf.SfExpressReq;
import com.ssy.lingxi.component.rest.model.req.sf.SfServiceReq;
import com.ssy.lingxi.component.rest.model.resp.eos.GoldPriceResp;
import com.ssy.lingxi.component.rest.service.EosApiService;
import com.ssy.lingxi.component.rest.service.SfService;
import com.ssy.lingxi.member.api.feign.IMemberCustomerProcessFeeDiscountFeign;
import com.ssy.lingxi.member.api.feign.IMemberFeign;
import com.ssy.lingxi.member.api.model.req.CalDiscountSkuReq;
import com.ssy.lingxi.member.api.model.req.CustomerCalReq;
import com.ssy.lingxi.member.api.model.req.GetMemberByIdReq;
import com.ssy.lingxi.member.api.model.resp.MemberFeignCodeRes;
import com.ssy.lingxi.member.api.model.resp.MobileCustomerFeeDiscountResp;
import com.ssy.lingxi.order.api.model.resp.OrderDepositResp;
import com.ssy.lingxi.order.api.model.resp.OrderFreeExpressConfigResp;
import com.ssy.lingxi.order.constant.OrderConstant;
import com.ssy.lingxi.order.controller.baitai.OrderHistoryReq;
import com.ssy.lingxi.order.entity.*;
import com.ssy.lingxi.order.enums.*;
import com.ssy.lingxi.order.model.bo.LogisticsProductDetailBO;
import com.ssy.lingxi.order.model.dto.FreightSpaceSingleProductExtendDTO;
import com.ssy.lingxi.order.model.req.baitai.*;
import com.ssy.lingxi.order.model.req.basic.MobileOrderProductReq;
import com.ssy.lingxi.order.model.req.basic.OrderProductFreeFreightReq;
import com.ssy.lingxi.order.model.req.basic.ProductFreeFreightReq;
import com.ssy.lingxi.order.model.req.vendor.VendorTransferOrderNewReq;
import com.ssy.lingxi.order.model.req.vendor.VendorVerifyOrderReq;
import com.ssy.lingxi.order.model.resp.baitai.*;
import com.ssy.lingxi.order.repository.*;
import com.ssy.lingxi.order.service.baitai.LogisticsInsuranceFeeSettingService;
import com.ssy.lingxi.order.service.base.*;
import com.ssy.lingxi.order.service.feign.ILogisticsFeignService;
import com.ssy.lingxi.order.service.web.IOrderCreationService;
import com.ssy.lingxi.order.service.web.IOrderParamConfigService;
import com.ssy.lingxi.order.service.web.OrderService;
import com.ssy.lingxi.pay.api.feign.IAssetAccountFeign;
import com.ssy.lingxi.pay.api.model.req.assetAccount.PayFinishAndUnLockReq;
import com.ssy.lingxi.product.api.enums.FreightSpaceSingleProductStatusEnum;
import com.ssy.lingxi.product.api.feign.ICommodityFeign;
import com.ssy.lingxi.product.api.feign.IWarehouseFeign;
import com.ssy.lingxi.product.api.model.req.baitai.CommoditySkuIdsReq;
import com.ssy.lingxi.product.api.model.req.baitai.SpaceSingleProductStatusReq;
import com.ssy.lingxi.product.api.model.resp.commodity.CommoditySkuDetailsResp;
import com.ssy.lingxi.product.api.model.resp.commodity.FreightSpaceSingleProductResp;
import com.ssy.lingxi.product.api.model.resp.warehouse.FreightSpaceSingleProductExtendResp;
import com.ssy.lingxi.product.api.model.resp.warehouse.WarehouseResp;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/27
 */
@Slf4j
@Service
public class OrderServiceImpl implements OrderService {

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private IRedisUtils redisUtils;

    @Resource
    private IBaseOrderTaskService baseOrderTaskService;

    @Resource
    private IBaseOrderHistoryService baseOrderHistoryService;

    @Resource
    private OrderProductRepository orderProductRepository;

    @Resource
    private SfService sfService;

    @Resource
    private ThirdPartyConfig thirdPartyConfig;

    // Redis key前缀验证码
    private static final String authUrl = "order:pickupCode:";

    @Resource
    private JPAQueryFactory jPAQueryFactory;

    @Resource
    private ICommodityFeign commodityFeign;

    @Resource
    private OrderRelationRepository orderRelationRepository;

    @Resource
    private IOrderParamConfigService orderParamConfigService;

    @Resource
    private EosApiService eosApiService;

    @Resource
    private OrderLogisticsRepository orderLogisticsDORepository;

    @Resource
    private ILogisticsFeignService logisticsFeignService;

    @Resource
    private IWarehouseFeign warehouseFeign;

    @Resource
    private BaiTaiMemberProperties baiTaiMemberProperties;

    @Resource
    private OrderPaymentRepository orderPaymentRepository;

    @Resource
    private IOrderCreationService orderCreationServiceProxy;

    @Resource
    private IMemberFeign memberInnerControllerFeign;

    @Resource
    private IAssetAccountFeign assetAccountFeign;

    @Resource
    private SubOrderPaymentRepository subOrderPaymentRepository;

    @Resource
    private LogisticsInsuranceFeeSettingService logisticsInsuranceFeeSettingService;

    @Resource
    private IBaseOrderInvoiceService baseOrderInvoiceService;

    @Resource
    private IBaseOrderConsigneeService baseOrderConsigneeService;

    @Resource
    private IBaseOrderDeliverService orderDeliverService;

    @Resource
    private OrderPickupRepository orderPickupRepository;

    @Resource
    private IMemberCustomerProcessFeeDiscountFeign memberCustomerProcessFeeDiscountFeign;


    /**
     * 获取订单自提验证码
     *
     * @param loginUser 登录用户信息
     * @param orderId   请求参数
     * @return 订单自提验证码
     */
    @Override
    public WrapperResp<OrderPickupCodeResp> getOrderPickupCode(UserLoginCacheDTO loginUser, Long orderId) {
        String authCode = redisUtils.stringGet(authUrl + orderId, RedisConstant.REDIS_USER_INDEX);
        OrderDO orderDO = orderRepository.findById(orderId).orElseThrow(() -> new BusinessException("订单不存在"));
        // 校验订单采购会员和登录会员是否一致
        if (!NumberUtil.equals(orderDO.getBuyerMemberId(), loginUser.getMemberId())) {
            throw new BusinessException("订单采购会员和登录会员不一致");
        }

        OrderPickupCodeResp orderPickupCodeResp = new OrderPickupCodeResp();

        if (StringUtil.isNotBlank(authCode)) {
            orderPickupCodeResp.setDynamicCode(authCode);
            orderPickupCodeResp.setExpireTime(orderDO.getPickupCodeExpireTime());
            return WrapperUtil.success(orderPickupCodeResp);
        }
        // 如果缓存中不存在验证码，生成新的验证码
        String random = String.valueOf(RandomUtil.randomInt(1000, 9999));
        redisUtils.stringSet(authUrl + orderId, random, 5 * 60L, RedisConstant.REDIS_USER_INDEX);
        // 保存超时时间，当前时间加上5分钟
        LocalDateTime localDateTime = LocalDateTimeUtil.now().plusMinutes(5);
        orderDO.setPickupCodeExpireTime(localDateTime);
        orderRepository.save(orderDO);

        orderPickupCodeResp.setDynamicCode(random);
        orderPickupCodeResp.setExpireTime(orderDO.getPickupCodeExpireTime());
        return WrapperUtil.success(orderPickupCodeResp);
    }

    /**
     * 查询订单取货码信息
     *
     * @param orderId 订单ID
     * @return 订单取货码信息
     */
    @Override
    public WrapperResp<OrderPickupCodeInfoResp> getOrderPickupCodeInfo(UserLoginCacheDTO loginUser, Long orderId) {
        OrderDO orderDO = orderRepository.findById(orderId).orElseThrow(() -> new BusinessException("订单不存在"));
        if (StringUtil.isEmpty(orderDO.getPickupCode())) {
            orderDO.setPickupCode(RandomNumberUtil.randomUniqueNumber(9));
            orderRepository.saveAndFlush(orderDO);
        }
        OrderPickupCodeInfoResp orderPickupCodeInfoResp = new OrderPickupCodeInfoResp();
        orderPickupCodeInfoResp.setPickupCode(orderDO.getPickupCode());
        if (ObjectUtil.isNotEmpty(orderDO.getOrderPickup())) {
            orderPickupCodeInfoResp.setSelfAddress(orderDO.getOrderPickup().getSelfAddress());
            orderPickupCodeInfoResp.setPickupTime(orderDO.getOrderPickup().getPickupTime());
            orderPickupCodeInfoResp.setPickupName(orderDO.getOrderPickup().getPickupName());
            orderPickupCodeInfoResp.setPickupPhone(orderDO.getOrderPickup().getPickupPhone());
        }
        return WrapperUtil.success(orderPickupCodeInfoResp);
    }

    /**
     * 查询生产复核订单商品信息
     *
     * @param orderId 订单ID
     * @return 订单商品信息
     */
    @Override
    public WrapperResp<List<OrderProductInfoResp>> getOrderProductInfo(UserLoginCacheDTO loginUser, Long orderId) {
        OrderDO orderDO = orderRepository.findById(orderId).orElseThrow(() -> new BusinessException("订单不存在"));
        List<OrderProductInfoResp> productInfoRespList = orderDO.getProducts().stream().map(s -> {
            OrderProductInfoResp orderProductInfoResp = new OrderProductInfoResp();
            orderProductInfoResp.setCommoditySingleId(s.getCommoditySingleId());
            orderProductInfoResp.setSingleCode(s.getSingleCode());
            orderProductInfoResp.setName(s.getName());
            orderProductInfoResp.setVendorMemberName(orderDO.getVendorMemberName());
            orderProductInfoResp.setCategory(s.getCategory());
            orderProductInfoResp.setSpec(s.getSpec());
            orderProductInfoResp.setNetWeight(s.getNetWeight());
            orderProductInfoResp.setGoodsWeight(s.getGoodsWeight());
            orderProductInfoResp.setQuantity(s.getQuantity());
            return orderProductInfoResp;
        }).collect(Collectors.toList());
        return WrapperUtil.success(productInfoRespList);
    }

    /**
     * 生产复核确认
     *
     * @param confirmOrderReq 订单ID
     * @return 确认结果
     */
    @Override
    @Transactional
    public WrapperResp<Void> confirmProductionReview(UserLoginCacheDTO loginUser, ConfirmOrderReq confirmOrderReq) {
        OrderDO order = orderRepository.findById(confirmOrderReq.getOrderId()).orElseThrow(() -> new BusinessException("订单不存在"));

        //修改商品金重和货重
        if (ObjectUtil.isNotEmpty(confirmOrderReq.getConfirmOrderProductReqList())) {
            confirmOrderReq.getConfirmOrderProductReqList().forEach(s -> {
                OrderProductDO orderProduct = order.getProducts().stream()
                        .filter(product -> product.getSingleCode().equals(s.getSingleCode()))
                        .findFirst()
                        .orElseThrow(() -> new BusinessException("订单商品不存在"));
                // 设置金重和货重
                orderProduct.setNetWeight(s.getNetWeight());
                orderProduct.setGoodsWeight(s.getGoodsWeight());
            });
        }

        OrderLogisticsDO orderLogistics = new OrderLogisticsDO();
        orderLogistics.setOrder(order);
        orderLogistics.setLogisticsStatus(LogisticsStatusEnum.PRODUCTION_COMPLETED.getCode());
        orderLogistics.setCreateTime(LocalDateTime.now());
        orderLogisticsDORepository.saveAndFlush(orderLogistics);

        // 执行订单流程
        baseOrderTaskService.execOrderProcess(order, OrderConstant.DIRECT_EXEC_TASK);

        orderRepository.saveAndFlush(order);
        orderProductRepository.saveAll(order.getProducts());

        // 订单内、外流转记录
        baseOrderHistoryService.saveBuyerInnerHistory(order.getBuyerMemberId(), order.getBuyerRoleId(), loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), order.getId(), order.getOrderNo(), OrderOperationEnum.REVIEW_CONFIRM, order.getBuyerInnerStatus(), confirmOrderReq.getReviewRemark());
        baseOrderHistoryService.saveBuyerOrderOuterHistory(order.getId(), order.getOrderNo(), order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerMemberName(), loginUser.getMemberRoleName(), OrderOperationEnum.REVIEW_CONFIRM, order.getOuterStatus(), OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()), confirmOrderReq.getReviewRemark());

        return WrapperUtil.success();
    }

    /**
     * 订单打包
     *
     * @param packOrderReq 订单ID
     * @return 打包结果
     */
    @Override
    public WrapperResp<Void> packOrder(UserLoginCacheDTO loginUser, PackOrderReq packOrderReq) {
        OrderDO order = orderRepository.findById(packOrderReq.getOrderId()).orElseThrow(() -> new BusinessException("订单不存在"));
        try {
            UpdateSalesOrderGoodsStatus updateSalesOrderGoodsStatus = new UpdateSalesOrderGoodsStatus();
            updateSalesOrderGoodsStatus.setDdzt(VendorInnerStatusEnum.TO_SHIP.getName());
            updateSalesOrderGoodsStatus.setGxsj(DateTimeUtil.getCurrentDateTime());
            updateSalesOrderGoodsStatus.setXsddh(order.getOrderNo());
            eosApiService.updateSalesGoodsOrderStatus(updateSalesOrderGoodsStatus);
        }catch (Exception e){

        }

        // 执行订单流程
        if (OrderDeliveryTypeEnum.SELF_PICKUP.getCode().equals(order.getDeliveryType())) {
            baseOrderTaskService.execOrderProcess(order, OrderConstant.DISAGREE);
        } else {
            baseOrderTaskService.execOrderProcess(order, OrderConstant.AGREE);
        }

        orderRepository.saveAndFlush(order);

        // 订单内、外流转记录
        baseOrderHistoryService.saveBuyerInnerHistory(order.getBuyerMemberId(), order.getBuyerRoleId(), loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), order.getId(), order.getOrderNo(), OrderOperationEnum.PACK, order.getBuyerInnerStatus(), packOrderReq.getReviewRemark());
        baseOrderHistoryService.saveBuyerOrderOuterHistory(order.getId(), order.getOrderNo(), order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerMemberName(), loginUser.getMemberRoleName(), OrderOperationEnum.PACK, order.getOuterStatus(), OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()), packOrderReq.getReviewRemark());

        OrderLogisticsDO orderLogistics = new OrderLogisticsDO();
        orderLogistics.setOrder(order);
        orderLogistics.setLogisticsStatus(LogisticsStatusEnum.PACKAGING_COMPLETED.getCode());
        orderLogistics.setCreateTime(LocalDateTime.now());
        orderLogisticsDORepository.saveAndFlush(orderLogistics);
        return WrapperUtil.success();
    }

    /**
     * 获取单号
     *
     * @param sfExpressNewReq 订单信息
     * @return 顺丰单号
     */
    @Override
    public WrapperResp<String> createOrder(UserLoginCacheDTO loginUser, SfExpressNewReq sfExpressNewReq) throws UnsupportedEncodingException {
        OrderDO order = orderRepository.findById(sfExpressNewReq.getOrderId()).orElseThrow(() -> new BusinessException("订单不存在"));
        // 获取商品总货重
        BigDecimal totalWeight = order.getProducts().stream()
                .map(OrderProductDO::getGoodsWeight)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        SfExpressReq sfExpressReq = new SfExpressReq();
        sfExpressReq.setExpressTypeId(1);
        sfExpressReq.setOrderId(String.valueOf(sfExpressNewReq.getOrderId()));
        sfExpressReq.setPayMethod(order.getSfPaymentType());
        // 总货重（克），转换成千克， Integer类型
        sfExpressReq.setTotalWeight(totalWeight.multiply(BigDecimal.valueOf(0.001)).intValue());
        sfExpressReq.setMonthlyCard(thirdPartyConfig.getSfMonthlyCard());
        // 获取订单商品信息
        List<CargoDetail> cargoDetails = order.getProducts().stream().map(product -> {
            CargoDetail cargoDetail = new CargoDetail();
            cargoDetail.setName(product.getName());
            return cargoDetail;
        }).collect(Collectors.toList());
        sfExpressReq.setCargoDetails(cargoDetails);
        // 设置寄件人信息
        List<ContactInfo> contactInfoList = new ArrayList<>();
        if (OrderDeliveryTypeEnum.EXPRESS_AGENCY.getCode().equals(order.getDeliveryType())) {
            if (ObjectUtil.isEmpty(sfExpressNewReq.getOrderDeliverReq())) {
                sfExpressNewReq.setOrderDeliverReq(new OrderDeliverSfReq());
            }
            BeanUtil.copyProperties(order.getDeliver(), sfExpressNewReq.getOrderDeliverReq());
        } else {
            OrderDeliverSfReq orderDeliverReq = new OrderDeliverSfReq();
            orderDeliverReq.setCountryCode("CN");
            orderDeliverReq.setPhone(sfExpressNewReq.getContactInfoList().getPhone());
            orderDeliverReq.setAddress(sfExpressNewReq.getContactInfoList().getAddress());
            orderDeliverReq.setDeliver(sfExpressNewReq.getContactInfoList().getShipperName());
            orderDeliverReq.setCityName(sfExpressNewReq.getContactInfoList().getCityName());
            orderDeliverReq.setDistrictName(sfExpressNewReq.getContactInfoList().getDistrictName());
            sfExpressNewReq.setOrderDeliverReq(orderDeliverReq);
        }
        ContactInfo senderInfo = new ContactInfo();
        senderInfo.setContactType(1);
        senderInfo.setTel(sfExpressNewReq.getOrderDeliverReq().getPhone());
        senderInfo.setAddress(sfExpressNewReq.getOrderDeliverReq().getAddress());
        senderInfo.setContact(sfExpressNewReq.getOrderDeliverReq().getDeliver());
        senderInfo.setProvince(sfExpressNewReq.getOrderDeliverReq().getProvinceName());
        senderInfo.setCity(sfExpressNewReq.getOrderDeliverReq().getCityName());
        senderInfo.setCounty(sfExpressNewReq.getOrderDeliverReq().getDistrictName());
        contactInfoList.add(senderInfo);
        // 收件人信息
        ContactInfo receiverInfo = new ContactInfo();
        receiverInfo.setContactType(2);
        receiverInfo.setTel(order.getConsignee().getPhone());
        receiverInfo.setAddress(order.getConsignee().getAddress());
        receiverInfo.setContact(order.getConsignee().getConsignee());
        receiverInfo.setProvince(order.getConsignee().getProvinceName());
        receiverInfo.setCity(order.getConsignee().getCityName());
        receiverInfo.setCounty(order.getConsignee().getDistrictName());
        contactInfoList.add(receiverInfo);
        sfExpressReq.setContactInfoList(contactInfoList);

        if (order.getSfInsuranceSelected()) {
            SfServiceReq sfServiceReq = new SfServiceReq();
            sfServiceReq.setName("INSURE");
            sfServiceReq.setValue(String.valueOf(order.getSfInsuranceFee()));
            sfExpressReq.getServiceList().add(sfServiceReq);
        }
        WrapperResp<String> serviceOrder = sfService.createOrder(sfExpressReq);

        order.setSfLogisticsNo(serviceOrder.getData());
        orderRepository.save(order);
        return serviceOrder;
    }

    /**
     * 核销自提订单
     *
     * @param vendorVerifyOrderReq 订单ID请求参数
     * @return 核销结果
     */
    @Override
    public WrapperResp<Void> vendorVerifyOrder(UserLoginCacheDTO loginUser, VendorVerifyOrderReq vendorVerifyOrderReq) {
        OrderDO order = orderRepository.findById(vendorVerifyOrderReq.getOrderId()).orElseThrow(() -> new BusinessException("订单不存在"));
        // 校验订单状态
        if (!OrderOuterStatusEnum.TO_PICK_UP.getCode().equals(order.getOuterStatus())) {
            throw new BusinessException("订单状态不正确，无法核销");
        }
        // 校验自提验证码
        if (!vendorVerifyOrderReq.getSelfCode().equals(order.getPickupCode())) {
            throw new BusinessException("自提验证码不正确");
        }
        // 验证动态验证码是否一样
        String authCode = redisUtils.stringGet(authUrl + order.getId(), RedisConstant.REDIS_USER_INDEX);
        if (StringUtil.isBlank(authCode) || !authCode.equals(vendorVerifyOrderReq.getRandom())) {
            throw new BusinessException("动态验证码不正确或已过期");
        }

        // 执行订单流程
        baseOrderTaskService.execOrderProcess(order, OrderConstant.DIRECT_EXEC_TASK);
        orderRepository.saveAndFlush(order);

        // 订单内、外流转记录
        baseOrderHistoryService.saveBuyerInnerHistory(order.getBuyerMemberId(), order.getBuyerRoleId(), loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), order.getId(), order.getOrderNo(), OrderOperationEnum.PICKUP_VERIFY, order.getBuyerInnerStatus(), vendorVerifyOrderReq.getRemark());
        baseOrderHistoryService.saveBuyerOrderOuterHistory(order.getId(), order.getOrderNo(), order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerMemberName(), loginUser.getMemberRoleName(), OrderOperationEnum.PICKUP_VERIFY, order.getOuterStatus(), OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()), vendorVerifyOrderReq.getRemark());

        return WrapperUtil.success();
    }

    /**
     * 订单走一个流程
     * @param orderIdReq 订单ID请求参数
     * @return 订单流程结果
     */
    @Override
    public WrapperResp<Void> orderProcess(UserLoginCacheDTO loginUser, OrderIdReq orderIdReq) {
        OrderDO order = orderRepository.findById(orderIdReq.getOrderId()).orElseThrow(() -> new BusinessException("订单不存在"));

        // 执行订单流程
        baseOrderTaskService.execOrderProcess(order, OrderConstant.DIRECT_EXEC_TASK);
        orderRepository.saveAndFlush(order);

        // 订单内、外流转记录
        baseOrderHistoryService.saveBuyerInnerHistory(order.getBuyerMemberId(), order.getBuyerRoleId(), loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), order.getId(), order.getOrderNo(), OrderOperationEnum.PICKUP_VERIFY, order.getBuyerInnerStatus(), "");
        baseOrderHistoryService.saveBuyerOrderOuterHistory(order.getId(), order.getOrderNo(), order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerMemberName(), loginUser.getMemberRoleName(), OrderOperationEnum.PICKUP_VERIFY, order.getOuterStatus(), OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()), "");

        return WrapperUtil.success();
    }

    /**
     * 查询该会员门店下是否存在未支付得订单
     *
     * @param branchId 门店ID
     * @return 是否存在未支付订单
     */
    @Override
    public WrapperResp<Boolean> hasUnpaidOrder(UserLoginCacheDTO loginUser, Long branchId) {
        QOrderDO qOrderDO = QOrderDO.orderDO;
        long fetchCount = jPAQueryFactory.select(qOrderDO)
                .from(qOrderDO)
                .where(qOrderDO.branchId.eq(branchId)
                        .and(qOrderDO.buyerMemberId.eq(loginUser.getMemberId()))
                        .and(qOrderDO.outerStatus.eq(OrderOuterStatusEnum.TO_PAY.getCode()))).fetchCount();

        return WrapperUtil.success(fetchCount > 0);
    }

    /**
     * 分页查询历史订单数据
     *
     * @param pageVO 分页查询请求参数
     * @return 分页查询结果
     */
    @Override
    public WrapperResp<PageDataResp<OrderHistoryResp>> pageHistoryOrders(UserLoginCacheDTO loginUser, OrderHistoryReq pageVO) {
        QOrderDO qOrderDO = QOrderDO.orderDO;

        BooleanBuilder booleanBuilder = new BooleanBuilder();
        // 分页查询条件
        if (ObjectUtil.isNotEmpty(pageVO.getSaleMode())) {
            booleanBuilder.and(qOrderDO.saleMode.eq(pageVO.getSaleMode()));
        }
        if (ObjectUtil.isNotEmpty(loginUser.getBranchId())) {
            booleanBuilder.and(qOrderDO.branchId.eq(pageVO.getBranchId()));
        }

        JPAQuery<OrderDO> jpaQuery = jPAQueryFactory.select(qOrderDO)
                .from(qOrderDO)
                .where(qOrderDO.buyerMemberId.eq(loginUser.getMemberId())
                        .and(qOrderDO.buyerRoleId.eq(loginUser.getMemberRoleId()))
                        .and(booleanBuilder));

        // 总数
        long totalCount = jpaQuery.fetchCount();

        // 分页查询
        List<OrderDO> orderList = jpaQuery.offset(pageVO.getCurrentOffset())
                .limit(pageVO.getPageSize())
                .orderBy(qOrderDO.createTime.desc())
                .fetch();

        // 如果没有查询到订单，直接返回空结果
        if (CollUtil.isEmpty(orderList)) {
            return WrapperUtil.success(new PageDataResp<>());
        }

        // 获取订单商品中的商品skuId集合
        Set<Long> skuIdSet = orderList.stream()
                .flatMap(order -> order.getProducts().stream())
                .map(OrderProductDO::getSkuId)
                .collect(Collectors.toSet());
        WrapperResp<Map<Long, Boolean>> commoditySkuHasStock = commodityFeign.getCommoditySkuHasStock(skuIdSet);

        List<OrderHistoryResp> historyRespList = orderList.stream().map(s -> {
            OrderHistoryResp orderHistoryResp = new OrderHistoryResp();
            orderHistoryResp.setOrderId(s.getId());
            orderHistoryResp.setOrderNo(s.getOrderNo());
            List<OrderHistoryInfoResp> historyInfoRespList = s.getProducts().stream().map(product -> {
                OrderHistoryInfoResp orderHistoryInfoResp = new OrderHistoryInfoResp();
                orderHistoryInfoResp.setSingleCode(product.getSingleCode());
                orderHistoryInfoResp.setSkuId(product.getSkuId());
                orderHistoryInfoResp.setName(product.getName());
                orderHistoryInfoResp.setLogo(product.getLogo());
                orderHistoryInfoResp.setSkuGoldWeight(product.getSkuGoldWeight());
                orderHistoryInfoResp.setSkuCode(product.getSkuCode());
                if (WrapperUtil.isOk(commoditySkuHasStock) && CollUtil.isNotEmpty(commoditySkuHasStock.getData())) {
                    orderHistoryInfoResp.setIsStock(commoditySkuHasStock.getData().getOrDefault(product.getSkuId(), false));
                }
                return orderHistoryInfoResp;
            }).collect(Collectors.toList());
            orderHistoryResp.setProductInfo(historyInfoRespList);
            return orderHistoryResp;
        }).collect(Collectors.toList());
        return WrapperUtil.success(new PageDataResp<>(totalCount, historyRespList));
    }

    /**
     * 订单结算
     * @param orderSettleReq 订单状态同步请求实体类
     * @return 订单结算结果
     */
    @Override
    @Transactional
    public WrapperResp<Void> orderSettlement(OrderSettleReq orderSettleReq) {
        OrderDO orderDO = orderRepository.findFirstByOrderNo(orderSettleReq.getOrderNo());

//        // 执行订单流程
//        baseOrderTaskService.execOrderProcess(orderDO, OrderConstant.DIRECT_EXEC_TASK);
//        orderRepository.saveAndFlush(orderDO);
//
//        // 订单内、外流转记录
//        baseOrderHistoryService.saveBuyerInnerHistory(orderDO.getBuyerMemberId(), orderDO.getBuyerRoleId(), "系统", "系统", "", orderDO.getId(), orderDO.getOrderNo(), OrderOperationEnum.PICKUP_VERIFY, orderDO.getBuyerInnerStatus(), "");
//        baseOrderHistoryService.saveBuyerOrderOuterHistory(orderDO.getId(), orderDO.getOrderNo(), orderDO.getBuyerMemberId(), orderDO.getBuyerRoleId(), orderDO.getBuyerMemberName(), "系统", OrderOperationEnum.PICKUP_VERIFY, orderDO.getOuterStatus(), OrderOuterStatusEnum.getNameByCode(orderDO.getOuterStatus()), "");

        Set<OrderRelationDO> relationDOSet = orderDO.getOrderRelationDOSet();

        List<Long> relationIds = relationDOSet.stream().map(OrderRelationDO::getRelationId).collect(Collectors.toList());
        List<OrderDO> orderDOList = orderRepository.findAllById(relationIds);

        orderDOList.forEach(order -> {
            // 执行订单流程
            baseOrderTaskService.execOrderProcess(order, OrderConstant.DIRECT_EXEC_TASK);
            orderRepository.saveAndFlush(order);

            // 订单内、外流转记录
            baseOrderHistoryService.saveBuyerInnerHistory(order.getBuyerMemberId(), order.getBuyerRoleId(), "系统", "系统", "", order.getId(), order.getOrderNo(), OrderOperationEnum.PICKUP_VERIFY, order.getBuyerInnerStatus(), "");
            baseOrderHistoryService.saveBuyerOrderOuterHistory(order.getId(), order.getOrderNo(), order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerMemberName(), "系统", OrderOperationEnum.PICKUP_VERIFY, order.getOuterStatus(), OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()), "");
        });
        return WrapperUtil.success();
    }

    /**
     * 订单状态变更
     * @param orderProducerReq 订单状态同步请求实体类
     * @return 订单状态变更结果
     */
    @Override
    @Transactional
    public WrapperResp<Void> orderStatusChange(OrderProducerReq orderProducerReq) {
        // 生产完成后，修改订单商品单件信息
        if (CollUtil.isEmpty(orderProducerReq.getCommodityReqList())) {
            throw new BusinessException("订单商品信息不能为空");
        }

        // new获取实时金价
        GoldPriceResp goldPrice = eosApiService.getGoldPrice();
        if (ObjectUtil.isEmpty(goldPrice)) {
            throw new BusinessException("获取实时金价失败");
        }

        OrderDO order = orderRepository.findFirstByOrderNo(orderProducerReq.getOrderNo());

        // 判断订单状态是否是待生产
        if (!BuyerInnerStatusEnum.IN_PRODUCTION.getCode().equals(order.getBuyerInnerStatus())) {
            throw new BusinessException("销售订单状态不是待生产，无法处理!");
        }

        order.getProducts().stream().filter(product -> ObjectUtil.isEmpty(product.getProductionCompleted()) || !product.getProductionCompleted()).forEach(product -> {
            List<CommodityReq> commodityReqList = orderProducerReq.getCommodityReqList().stream()
                    .filter(s -> s.getSkuCode().equals(product.getSkuCode())).collect(Collectors.toList());
            // 获取第一个没有使用过的商品信息
            Optional<CommodityReq> productDO = commodityReqList.stream().filter(s -> !s.getUsed()).findFirst();
            product.setProductionCompleted(false);
            if (productDO.isPresent()) {
                // 更新商品单件信息
                product.setSingleCode(productDO.get().getSingleCode());
                product.setNetWeight(productDO.get().getNetWeight());
                product.setBaseLaborCosts(productDO.get().getBaseLaborCosts());
                product.setAdditionalLaborCosts(productDO.get().getAdditionalLaborCosts());
                product.setPieceLaborCosts(productDO.get().getPieceLaborCosts());
                product.setAmount(productDO.get().getNetWeight().multiply(goldPrice.getJj()));
                BigDecimal refPrice = product.getAmount()
                        .add(ObjectUtil.isNotEmpty(productDO.get().getBaseLaborCosts()) ? productDO.get().getBaseLaborCosts() : BigDecimal.ZERO)
                        .add(ObjectUtil.isNotEmpty(productDO.get().getAdditionalLaborCosts()) ? productDO.get().getAdditionalLaborCosts() : BigDecimal.ZERO)
                        .add(ObjectUtil.isNotEmpty(productDO.get().getPieceLaborCosts()) ? productDO.get().getPieceLaborCosts() : BigDecimal.ZERO);
                product.setRefPrice(refPrice);
                product.setWarehouseCode(productDO.get().getCode());
                product.setProductionCompleted(true);
                productDO.get().setUsed(true);
                orderProductRepository.saveAndFlush(product);
            }
        });

        // 获取商品总金重
        order.setTotalWeight(order.getProducts().stream().map(OrderProductDO::getNetWeight).reduce(BigDecimal.valueOf(0), BigDecimal::add));
        // 获取商品所有工费
        BigDecimal reduce = order.getProducts().stream().map(s -> {
            BigDecimal craftPrice = ObjectUtil.isNotEmpty(s.getBaseLaborCosts()) ? s.getBaseLaborCosts().multiply(s.getNetWeight()) : BigDecimal.ZERO;
            if (ObjectUtil.isNotEmpty(s.getAdditionalLaborCostsPerGram())) {
                craftPrice = craftPrice.add(s.getAdditionalLaborCostsPerGram().multiply(s.getNetWeight()));
            }
            if (ObjectUtil.isNotEmpty(s.getPieceLaborCosts())) {
                craftPrice = craftPrice.add(s.getPieceLaborCosts());
            }
            if (CollUtil.isNotEmpty(s.getFreightSpaceSingleProductExtendRespList())) {
                craftPrice = craftPrice.add(s.getFreightSpaceSingleProductExtendRespList().stream()
                        .filter(Objects::nonNull)
                        .map(freight -> new BigDecimal(freight.getFieldValue())).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            return craftPrice;
        }).reduce(BigDecimal.valueOf(0), BigDecimal::add);
        order.setTotalCraftPrice(reduce);

        // 计算订单运费
        if (!OrderDeliveryTypeEnum.SELF_PICKUP.getCode().equals(order.getDeliveryType())) {
            extracted(order);
        }
        orderRepository.saveAndFlush(order);
        // 判断只有所有订货商品都已经生产完成后，才走后面的逻辑
        if (order.getProducts().stream().anyMatch(product -> !product.getProductionCompleted())) {
            return WrapperUtil.success();
        }

        // 更新挂签价格
        //order.getVendorInnerStatus()

        // 获取工费优惠信息
        List<MobileCustomerFeeDiscountResp> customerFeeDiscountRespList = new ArrayList<>();
        CustomerCalReq customerCalReq = new CustomerCalReq();
        List<CalDiscountSkuReq> discountSkuReqList = order.getProducts().stream().map(s -> {
            CalDiscountSkuReq calDiscountSkuReq = new CalDiscountSkuReq();
            calDiscountSkuReq.setSingleCode(s.getSingleCode());
            return calDiscountSkuReq;
        }).collect(Collectors.toList());
        customerCalReq.setSkuIdList(discountSkuReqList);
        customerCalReq.setCustomerId(order.getBuyerMemberId());
        WrapperResp<List<MobileCustomerFeeDiscountResp>> calculatedDiscount = memberCustomerProcessFeeDiscountFeign.calculateDiscount(customerCalReq);
        if (WrapperUtil.isOk(calculatedDiscount)) {
            customerFeeDiscountRespList = calculatedDiscount.getData();
        }

        // 如果有工费优惠，则减去工费优惠
        if (CollectionUtil.isNotEmpty(customerFeeDiscountRespList)) {
            List<MobileCustomerFeeDiscountResp> finalCustomerFeeDiscountRespList = customerFeeDiscountRespList;
            order.getProducts().forEach(productDetail -> {
                Optional<MobileCustomerFeeDiscountResp> discountRespOptional = finalCustomerFeeDiscountRespList.stream().filter(customerFeeDiscountResp -> customerFeeDiscountResp.getSingleCode().equals(productDetail.getSingleCode())).findFirst();
                if (discountRespOptional.isPresent()) {
                    // 优惠金额
                    BigDecimal discountAmount = discountRespOptional.get().getBaseLaborCostDiscountAmount() != null ? discountRespOptional.get().getBaseLaborCostDiscountAmount() : BigDecimal.ZERO;
                    discountAmount = discountAmount.add(discountRespOptional.get().getGramLaborCostDiscountAmount() != null ? discountRespOptional.get().getGramLaborCostDiscountAmount() : BigDecimal.ZERO);
                    discountAmount = discountAmount.add(discountRespOptional.get().getPieceLaborCostDiscountAmount() != null ? discountRespOptional.get().getPieceLaborCostDiscountAmount() : BigDecimal.ZERO);
                    productDetail.setDiscountedBaseLaborCosts(discountRespOptional.get().getBaseLaborCostDiscountAmount() != null ? discountRespOptional.get().getBaseLaborCostDiscountAmount() : BigDecimal.ZERO);
                    productDetail.setDiscountedAdditionalLaborCosts(discountRespOptional.get().getGramLaborCostDiscountAmount() != null ? discountRespOptional.get().getGramLaborCostDiscountAmount() : BigDecimal.ZERO);
                    productDetail.setDiscountedPieceLaborCosts(discountRespOptional.get().getPieceLaborCostDiscountAmount() != null ? discountRespOptional.get().getPieceLaborCostDiscountAmount() : BigDecimal.ZERO);
                    order.setCraftDiscountAmount(order.getCraftDiscountAmount() != null ? order.getCraftDiscountAmount().add(discountAmount) : discountAmount);
                }
            });
        }
        order.setTotalCraftPrice(order.getTotalCraftPrice().subtract(order.getCraftDiscountAmount()));
        orderRepository.saveAndFlush(order);

        // 销售订单执行订单流程
        List<Long> relationIds = order.getOrderRelationDOSet()
                .stream().map(OrderRelationDO::getRelationId)
                .collect(Collectors.toList());
        List<OrderDO> orderDOList = orderRepository.findAllById(relationIds);

        // 采购单必须都是待结算，才可以处理待结算
        if (orderDOList.stream().anyMatch(o -> !VendorInnerStatusEnum.TO_PRODUCE_1.getCode().equals(o.getVendorInnerStatus()))) {
            throw new BusinessException("采购单状态不是待结算，无法处理!");
        }

        // 更新采购单，商品信息
        orderDOList.forEach(orderDO -> {
            orderDO.getProducts().forEach(product -> {
                Optional<OrderProductDO> productDO = order.getProducts().stream().filter(s -> s.getId().equals(product.getRelationId())).findFirst();
                if (productDO.isPresent()) {
                    product.setCommoditySingleId(productDO.get().getCommoditySingleId());
                    product.setSingleCode(productDO.get().getSingleCode());
                    product.setName(productDO.get().getName());
                    product.setSpec(productDO.get().getSpec());
                    product.setNetWeight(productDO.get().getNetWeight());
                    product.setGoodsWeight(productDO.get().getGoodsWeight());
                    product.setQuantity(productDO.get().getQuantity());
                    product.setAmount(productDO.get().getAmount());
                    BigDecimal refPrice = product.getAmount()
                            .add(ObjectUtil.isNotEmpty(productDO.get().getBaseLaborCosts()) ? productDO.get().getBaseLaborCosts() : BigDecimal.ZERO)
                            .add(ObjectUtil.isNotEmpty(productDO.get().getAdditionalLaborCosts()) ? productDO.get().getAdditionalLaborCosts() : BigDecimal.ZERO)
                            .add(ObjectUtil.isNotEmpty(productDO.get().getPieceLaborCosts()) ? productDO.get().getPieceLaborCosts() : BigDecimal.ZERO);
                    refPrice = refPrice.subtract(ObjectUtil.isNotEmpty(productDO.get().getDiscountedBaseLaborCosts()) ? productDO.get().getDiscountedBaseLaborCosts() : BigDecimal.ZERO)
                                    .subtract(ObjectUtil.isNotEmpty(productDO.get().getDiscountedAdditionalLaborCosts()) ? productDO.get().getDiscountedAdditionalLaborCosts() : BigDecimal.ZERO)
                                    .subtract(ObjectUtil.isNotEmpty(productDO.get().getDiscountedPieceLaborCosts()) ? productDO.get().getDiscountedPieceLaborCosts() : BigDecimal.ZERO);
                    product.setRefPrice(refPrice);
                    orderProductRepository.saveAndFlush(product);
                }
            });

            // 获取商品总金重
            orderDO.setTotalWeight(orderDO.getProducts().stream().map(OrderProductDO::getNetWeight).reduce(BigDecimal.valueOf(0), BigDecimal::add));
            // 获取商品所有工费
            BigDecimal reduceTwo = orderDO.getProducts().stream().map(s -> {
                BigDecimal craftPrice = ObjectUtil.isNotEmpty(s.getBaseLaborCosts()) ? s.getBaseLaborCosts() : BigDecimal.ZERO;
                if (ObjectUtil.isNotEmpty(s.getAdditionalLaborCostsPerGram())) {
                    craftPrice = craftPrice.add(s.getAdditionalLaborCostsPerGram().multiply(s.getNetWeight()));
                }
                if (ObjectUtil.isNotEmpty(s.getPieceLaborCosts())) {
                    craftPrice = craftPrice.add(s.getPieceLaborCosts());
                }
                if (CollUtil.isNotEmpty(s.getFreightSpaceSingleProductExtendRespList())) {
                    craftPrice = craftPrice.add(s.getFreightSpaceSingleProductExtendRespList().stream()
                            .filter(Objects::nonNull)
                            .map(freight -> new BigDecimal(freight.getFieldValue())).reduce(BigDecimal.ZERO, BigDecimal::add));
                }
                return craftPrice;
            }).reduce(BigDecimal.valueOf(0), BigDecimal::add);
            orderDO.setTotalCraftPrice(reduceTwo);
        });
        orderRepository.saveAll(orderDOList);

        // 执行订单流程
        baseOrderTaskService.execOrderProcess(order, OrderConstant.DIRECT_EXEC_TASK);
        orderRepository.saveAndFlush(order);

        // 订单内、外流转记录
        baseOrderHistoryService.saveBuyerInnerHistory(order.getBuyerMemberId(), order.getBuyerRoleId(), "系统", "系统", "", order.getId(), order.getOrderNo(), OrderOperationEnum.PICKUP_VERIFY, order.getBuyerInnerStatus(), "");
        baseOrderHistoryService.saveBuyerOrderOuterHistory(order.getId(), order.getOrderNo(), order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerMemberName(), "系统", OrderOperationEnum.PICKUP_VERIFY, order.getOuterStatus(), OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()), "");

        // 执行订单流程
        orderDOList.forEach(o -> {
            baseOrderTaskService.execOrderProcess(o, OrderConstant.DIRECT_EXEC_TASK);
            orderRepository.saveAndFlush(o);

            // 订单内、外流转记录
            baseOrderHistoryService.saveBuyerInnerHistory(o.getBuyerMemberId(), o.getBuyerRoleId(), "系统", "系统", "", o.getId(), o.getOrderNo(), OrderOperationEnum.PICKUP_VERIFY, o.getBuyerInnerStatus(), "");
            baseOrderHistoryService.saveBuyerOrderOuterHistory(o.getId(), o.getOrderNo(), o.getBuyerMemberId(), o.getBuyerRoleId(), o.getBuyerMemberName(), "系统", OrderOperationEnum.PICKUP_VERIFY, o.getOuterStatus(), OrderOuterStatusEnum.getNameByCode(o.getOuterStatus()), "");
        });

        return WrapperUtil.success();
    }

    /**
     * 计算订单运费
     * @param order
     */
    private void extracted(OrderDO order) {
        // 判断是否有收货地址信息
        if (ObjectUtil.isEmpty(order.getConsignee()) || ObjectUtil.isEmpty(order.getConsignee().getConsigneeId())) {
            return;
        }

        List<String> warehouseCodeList = order.getProducts().stream().map(OrderProductDO::getWarehouseCode).collect(Collectors.toList());
        WrapperResp<Map<String, Long>> templateIdByWarehouseCode = warehouseFeign.findLogisticsTemplateIdByWarehouseCode(warehouseCodeList);
        if (WrapperUtil.isOk(templateIdByWarehouseCode)) {
            List<ProductFreeFreightReq> productFreeFreightReqList = order.getProducts().stream().filter(s-> templateIdByWarehouseCode.getData().containsKey(s.getWarehouseCode())).map(req -> {
                ProductFreeFreightReq productFreeFreightReq = new ProductFreeFreightReq();
                productFreeFreightReq.setMemberId(baiTaiMemberProperties.getSelfMemberId());
                productFreeFreightReq.setRoleId(baiTaiMemberProperties.getSelfRoleId());
                productFreeFreightReq.setTemplateId(templateIdByWarehouseCode.getData().get(req.getWarehouseCode()));
                productFreeFreightReq.setRefPrice(req.getRefPrice());
                productFreeFreightReq.setCount(req.getQuantity());
                productFreeFreightReq.setWeight(req.getWeight());
                return productFreeFreightReq;
            }).collect(Collectors.toList());

            OrderProductFreeFreightReq orderProductFreeFreightReq = new OrderProductFreeFreightReq();
            orderProductFreeFreightReq.setReceiverAddressId(order.getConsignee().getConsigneeId());
            orderProductFreeFreightReq.setProductFreightDetailList(productFreeFreightReqList);
            BigDecimal orderFreeFreight = logisticsFeignService.findOrderFreeFreight(null, orderProductFreeFreightReq);
            order.setFreight(orderFreeFreight);
            order.setTotalServicePrice(order.getTotalCraftPrice().add(orderFreeFreight));
        }
        // 修改订单支付记录的，工费和服务费
        order.getPayments().forEach(s -> {
            s.setOtherFee(order.getTotalServicePrice());
            s.setLaborFee(order.getTotalCraftPrice());
        });
        orderPaymentRepository.saveAll(order.getPayments());
    }

    /**
     * 释放库存
     * @param orderNo 订单实体
     */
    @Override
    @Transactional
    public void releaseStock(String orderNo) {
        OrderDO order = orderRepository.findFirstByOrderNo(orderNo);
        order.setBuyerInnerStatus(BuyerInnerStatusEnum.CANCELLED.getCode());
        order.setVendorInnerStatus(VendorInnerStatusEnum.CANCELLED.getCode());
        order.setOuterStatus(OrderOuterStatusEnum.CANCELLED.getCode());
        orderRepository.saveAndFlush(order);

        Set<OrderRelationDO> relationDOSet = order.getOrderRelationDOSet();
        List<Long> relationIds = relationDOSet.stream().map(OrderRelationDO::getRelationId).collect(Collectors.toList());
        List<OrderDO> orderDOList = orderRepository.findAllById(relationIds);
        orderDOList.forEach(o -> {
            o.setBuyerInnerStatus(BuyerInnerStatusEnum.CANCELLED.getCode());
            o.setVendorInnerStatus(VendorInnerStatusEnum.CANCELLED.getCode());
            o.setOuterStatus(OrderOuterStatusEnum.CANCELLED.getCode());
        });
        orderRepository.saveAll(orderDOList);

        List<Long> commoditySingleIdList = order.getProducts()
                .stream().map(OrderProductDO::getCommoditySingleId)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(commoditySingleIdList)) {
            SpaceSingleProductStatusReq commoditySingleProductStatusReq = new SpaceSingleProductStatusReq();
            commoditySingleProductStatusReq.setSingleProductIds(commoditySingleIdList);
            commoditySingleProductStatusReq.setStatus(FreightSpaceSingleProductStatusEnum.NOT_USE.getCode());
            WrapperResp<Void> voidWrapperResp = commodityFeign.updateFreightSpaceSingleProductStatus(commoditySingleProductStatusReq);
            if (WrapperUtil.isFail(voidWrapperResp)) {
                log.error("更新现货商品单件状态失败: {}", voidWrapperResp.getMessage());
                throw new BusinessException(voidWrapperResp.getCode(), voidWrapperResp.getMessage());
            }
        }
        List<String> singleCodeList = order.getProducts()
                .stream().map(OrderProductDO::getSingleCode)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(singleCodeList)) {
            String status = eosApiService.releaseStock(singleCodeList);
            if (!"Y".equals(status)) {
                log.error("更新现货商品单件状态失败: {}", status);
                throw new BusinessException("更新现货商品单件状态失败");
            }
        }
    }

    /**
     * 扣减库存
     * @param order 订单实体
     */
    @Override
    public void deductStock(OrderDO order) {
        List<Long> commoditySingleIdList = order.getProducts()
                .stream().map(OrderProductDO::getCommoditySingleId)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(commoditySingleIdList)) {
            SpaceSingleProductStatusReq commoditySingleProductStatusReq = new SpaceSingleProductStatusReq();
            commoditySingleProductStatusReq.setSingleProductIds(commoditySingleIdList);
            commoditySingleProductStatusReq.setStatus(FreightSpaceSingleProductStatusEnum.CONSUMED.getCode());
            WrapperResp<Void> voidWrapperResp = commodityFeign.updateFreightSpaceSingleProductStatus(commoditySingleProductStatusReq);
            if (WrapperUtil.isFail(voidWrapperResp)) {
                log.error("更新现货商品单件状态失败: {}", voidWrapperResp.getMessage());
                throw new BusinessException(voidWrapperResp.getCode(), voidWrapperResp.getMessage());
            }
        }
    }

    /**
     * 查询订单，分拣单信息
     * @param orderIdsReq 订单ID请求参数
     * @return 分拣单信息
     */
    @Override
    public List<OrderSortInfoResp> getOrderSortInfo(UserLoginCacheDTO loginUser, CommonIdListReq orderIdsReq) {
        List<OrderDO> orderDOList = orderRepository.findAllById(orderIdsReq.getIdList());
        if (CollUtil.isEmpty(orderDOList)) {
            return Collections.emptyList();
        }
        List<OrderSortInfoResp> orderSortInfoRespList = new ArrayList<>();
        orderDOList.forEach(s -> {
            OrderSortInfoResp orderSortInfoResp = new OrderSortInfoResp();
            orderSortInfoResp.setSortTypeName(CommoditySaleModeEnum.getNameByCode(s.getSaleMode()));
            orderSortInfoResp.setOrderNo(s.getOrderNo());
            orderSortInfoResp.setSaleOrderNo(s.getRelationNo());
            orderSortInfoResp.setSupplierName(s.getVendorMemberName());
            orderSortInfoResp.setPrintUserName(loginUser.getUserName());
            orderSortInfoResp.setPrintTime(LocalDateTimeUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss"));
            List<OrderSortProductInfoResp> orderSortProductInfoRespList = new ArrayList<>();
            s.getProducts().forEach(orderProductDO -> {
                OrderSortProductInfoResp orderSortProductInfoResp = new OrderSortProductInfoResp();
                orderSortProductInfoResp.setProductName(orderProductDO.getName());
                orderSortProductInfoResp.setSpec(orderProductDO.getSpec());
                orderSortProductInfoResp.setTotalCount(orderProductDO.getQuantity());
                orderSortProductInfoResp.setTotalWeight(orderProductDO.getGoodsWeight());
                orderSortProductInfoResp.setSpecWeight(orderProductDO.getWeight());
                orderSortProductInfoResp.setSpecCode(orderProductDO.getSkuCode());
                orderSortProductInfoResp.setProductCode(orderProductDO.getSpuCode());
                orderSortProductInfoResp.setSupplierCode(orderProductDO.getSupplyMemberId());
                orderSortProductInfoResp.setBarCode(orderProductDO.getSingleCode());
                orderSortProductInfoResp.setNetWeight(orderProductDO.getNetWeight());
                orderSortProductInfoRespList.add(orderSortProductInfoResp);
            });
            orderSortInfoResp.setProductInfo(orderSortProductInfoRespList);
            orderSortInfoRespList.add(orderSortInfoResp);
        });
        return orderSortInfoRespList;
    }

    /**
     * 刷新订单实时总金额
     * @param order 订单实体
     */
    @Override
    public void getOrderTotalPrice(OrderDO order) {
        // 判断订单是否需要刷新总金额
        if (order.getTotalAmount().compareTo(order.getPaidAmount()) == 0) {
            return;
        }
        GoldPriceResp goldPrice = eosApiService.getGoldPrice();

        // 计算订单总金额
        BigDecimal multiply = order.getTotalWeight().multiply(goldPrice.getJj()).setScale(2, RoundingMode.HALF_UP);
        // 服务费
        BigDecimal totalAmount = multiply.add(order.getTotalCraftPrice()).add(order.getTotalServicePrice());
        order.setRawMaterialAmount(multiply);
        order.setTotalAmount(totalAmount);
    }

    /**
     * 刷新订单实时总金额
     * @param order 订单ID
     * @param goldPrice 金价
     */
    @Override
    public void getOrderTotalPrice(OrderDO order, BigDecimal goldPrice) {
        // 判断订单是否需要刷新总金额
        if (order.getTotalAmount().compareTo(order.getPaidAmount()) == 0) {
            return;
        }
        // 计算订单总金额
        BigDecimal multiply = order.getTotalWeight().multiply(goldPrice).setScale(2, RoundingMode.HALF_UP);
        // 服务费
        BigDecimal totalAmount = multiply.add(order.getTotalCraftPrice()).add(order.getTotalServicePrice());
        order.setTotalAmount(totalAmount);
    }

    /**
     * 订单支付完成后，需要处理的逻辑
     * @param order 订单实体
     */
    @Override
    @Transactional
    public void afterOrderPaySuccess(OrderDO order, Integer batchNo) {
        // 判断是否是现货订单，如果是自动跳过生产流程
        if (CommoditySaleModeEnum.SPOT.getCode().equals(order.getSaleMode()) && OrderOuterStatusEnum.IN_PRODUCTION.getCode().equals(order.getOuterStatus())) {
            baseOrderTaskService.execOrderPayTasks(order, 4, Stream.of(OrderConstant.DIRECT_EXEC_TASK, OrderConstant.DIRECT_EXEC_TASK, OrderConstant.AGREE, OrderConstant.AGREE).collect(Collectors.toList()));
        }

        if (batchNo.equals(2)) {
            log.info("订货订单支付成功，开始采购执行订单流程，订货订单号：{}", order.getOrderNo());
            // 销售单复核完后，修改采购单状态到结算
            Set<OrderRelationDO> relationDOSet = order.getOrderRelationDOSet();

            List<Long> relationIds = relationDOSet.stream().map(OrderRelationDO::getRelationId).collect(Collectors.toList());
            List<OrderDO> orderDOList = orderRepository.findAllById(relationIds);
            // 执行订单流程
            orderDOList.forEach(o -> {
                baseOrderTaskService.execOrderPayTasks(o, 2, Stream.of(OrderConstant.DIRECT_EXEC_TASK, OrderConstant.DIRECT_EXEC_TASK).collect(Collectors.toList()));
                orderRepository.saveAndFlush(o);

                // 订单内、外流转记录
                baseOrderHistoryService.saveBuyerInnerHistory(o.getBuyerMemberId(), o.getBuyerRoleId(), "系统", "", "", o.getId(), o.getOrderNo(), OrderOperationEnum.REVIEW_CONFIRM, o.getBuyerInnerStatus(), "");
                baseOrderHistoryService.saveBuyerOrderOuterHistory(o.getId(), o.getOrderNo(), o.getBuyerMemberId(), o.getBuyerRoleId(), o.getBuyerMemberName(), "", OrderOperationEnum.REVIEW_CONFIRM, o.getOuterStatus(), OrderOuterStatusEnum.getNameByCode(o.getOuterStatus()), "");
            });
        }

        // 自动转单
        if (OrderOuterStatusEnum.TO_PICKING_AND_MATCH.getCode().equals(order.getOuterStatus()) || OrderOuterStatusEnum.IN_PRODUCTION.getCode().equals(order.getOuterStatus())) {
            //推送销售订单到Eos
            pushSalesOrderToEos(order);
            VendorTransferOrderNewReq transferOrderReq = new VendorTransferOrderNewReq();
            transferOrderReq.setOrderId(order.getId());
            orderCreationServiceProxy.autoTransferOrders(transferOrderReq);
        }
        //尾款支付通知
        if(batchNo == 2){
            balancePymentNotify(order);
        }
    }

    /**
     * 更新订单物流信息
     * @param updateOrderLogisticsReq 更新订单物流请求参数
     * @return 更新结果
     */
    @Override
    @Transactional
    public WrapperResp<Void> updateOrderLogistics(UserLoginCacheDTO loginUser, UpdateOrderLogisticsReq updateOrderLogisticsReq) {
        // Step 1: 校验运费
        List<MobileOrderProductLogisticsReq> freightProductList = updateOrderLogisticsReq.getProductLogisticsReqList().stream().filter(
                p -> p.getDeliveryType().equals(OrderProductDeliverTypeEnum.LOGISTICS.getCode())
                        && p.getFreightType().equals(OrderFreightTypeEnum.BUYER.getCode())
        ).collect(Collectors.toList());
        BigDecimal freightAmount = BigDecimal.ZERO;
        if (updateOrderLogisticsReq.getConsignee() != null && freightProductList != null  && freightProductList.stream().anyMatch(s -> ObjectUtil.isNotEmpty(s.getLogisticsTemplateId()))) {
            OrderProductFreeFreightReq orderProductFreeFreightReq = OrderProductFreeFreightReq.buildBy(updateOrderLogisticsReq, freightProductList);
            freightAmount = logisticsFeignService.findOrderFreeFreight(loginUser, orderProductFreeFreightReq);
        }

        if (SfPaymentTypeEnum.RECEIVER_PAY.getCode().equals(updateOrderLogisticsReq.getSfPaymentType())) {
            freightAmount = BigDecimal.ZERO;
        }

        if (Stream.of(OrderDeliveryTypeEnum.EXPRESS_DELIVERY.getCode(), OrderDeliveryTypeEnum.EXPRESS_AGENCY.getCode()).collect(Collectors.toList()).stream().anyMatch(deliver -> deliver.equals(updateOrderLogisticsReq.getDeliveryType()))) {
            if (updateOrderLogisticsReq.getFreight().setScale(2, RoundingMode.HALF_UP).compareTo(freightAmount) != 0) {
                throw new BusinessException(ResponseCodeEnum.ORDER_FREIGHT_AMOUNT_MISMATCHED);
            }
        }

        // Step 2: 校验保费
        if (updateOrderLogisticsReq.getSfInsuranceSelected() && ObjectUtil.isNotEmpty(updateOrderLogisticsReq.getSfInsuranceFee())) {
            CalculateReq calculateReq = new CalculateReq();
            calculateReq.setInsuranceAmount(updateOrderLogisticsReq.getSfInsuranceAmount());
            calculateReq.setInsuranceCompanyType(InsuranceCompanyEnum.SF_INSURANCE.getCode());
            WrapperResp<CalculateResp> respWrapperResp = logisticsInsuranceFeeSettingService.calculateInsuranceFee(calculateReq);
            if (updateOrderLogisticsReq.getSfInsuranceFee().compareTo(respWrapperResp.getData().getInsuranceFee()) != 0) {
                throw new BusinessException("顺丰保费不正确，请重新下单");
            }
        }

        if (updateOrderLogisticsReq.getPacInsuranceSelected() && ObjectUtil.isNotEmpty(updateOrderLogisticsReq.getPacInsuranceFee())) {
            CalculateReq calculateTwoReq = new CalculateReq();
            calculateTwoReq.setInsuranceAmount(updateOrderLogisticsReq.getPacInsuranceAmount());
            calculateTwoReq.setInsuranceCompanyType(InsuranceCompanyEnum.TAIPING_INSURANCE.getCode());
            WrapperResp<CalculateResp> respWrapperTwoResp = logisticsInsuranceFeeSettingService.calculateInsuranceFee(calculateTwoReq);
            if (updateOrderLogisticsReq.getPacInsuranceFee().compareTo(respWrapperTwoResp.getData().getInsuranceFee()) != 0) {
                throw new BusinessException("平安保费不正确，请重新下单");
            }
        }

        OrderDO orderDO = orderRepository.findById(updateOrderLogisticsReq.getOrderId()).orElseThrow(() -> new BusinessException("订单不存在"));

        // 更新发票信息
        baseOrderInvoiceService.checkOrderInvoice(orderDO, updateOrderLogisticsReq.getInvoice(), false);

        //收货人信息
        baseOrderConsigneeService.checkOrderConsignee(orderDO, updateOrderLogisticsReq.getConsignee(), false);

        //发货人信息
        if (OrderDeliveryTypeEnum.EXPRESS_AGENCY.getCode().equals(updateOrderLogisticsReq.getDeliveryType())) {
            orderDeliverService.checkOrderConsignee(orderDO, updateOrderLogisticsReq.getDeliveryAddress(), false);
        }

        //自提点信息
        if (OrderDeliveryTypeEnum.SELF_PICKUP.getCode().equals(updateOrderLogisticsReq.getDeliveryType())) {
            if (ObjectUtil.isEmpty(updateOrderLogisticsReq.getPickUpAddress())) {
                throw new BusinessException("自提地址不能为空");
            }

            List<OrderPickupDO> consigneeDOS = orderPickupRepository.findByOrder(orderDO);
            consigneeDOS.forEach(orderConsigneeDO -> orderConsigneeDO.setOrder(null));
            orderPickupRepository.deleteAll(consigneeDOS);

            OrderPickupDO orderPickupDO = BeanUtil.copyProperties(updateOrderLogisticsReq.getPickUpAddress(), OrderPickupDO.class);
            orderPickupDO.setOrder(orderDO);
            orderPickupDO.setPickupName(updateOrderLogisticsReq.getPickUpAddress().getConsignee());
            orderPickupDO.setPickupPhone(updateOrderLogisticsReq.getPickUpAddress().getPhone());
            orderDO.setOrderPickup(orderPickupDO);
        }

        // 更新运费价格
        if (ObjectUtil.isNotEmpty(updateOrderLogisticsReq.getFreight())) {
            orderDO.setTotalAmount(orderDO.getTotalAmount().subtract(orderDO.getFreight()).add(updateOrderLogisticsReq.getFreight()));
            orderDO.setTotalServicePrice(orderDO.getTotalServicePrice().subtract(orderDO.getFreight()).add(updateOrderLogisticsReq.getFreight()));
            orderDO.setFreight(updateOrderLogisticsReq.getFreight().setScale(2, RoundingMode.HALF_UP));
        }
        // 更新支付记录的服务费
        orderDO.getPayments().forEach(payment -> {
            payment.setOtherFee(orderDO.getTotalServicePrice());
        });
        orderPaymentRepository.saveAll(orderDO.getPayments());
        // 备注
        orderDO.setOrderRemark(updateOrderLogisticsReq.getOrderRemark());
        orderDO.setPackageRequirement(updateOrderLogisticsReq.getPackageRequirement());
        orderDO.setOtherRequirement(updateOrderLogisticsReq.getOtherRequirement());
        orderDO.setDeliveryType(updateOrderLogisticsReq.getDeliveryType());
        orderDO.setSfInsuranceSelected(updateOrderLogisticsReq.getSfInsuranceSelected());
        orderDO.setPacInsuranceSelected(updateOrderLogisticsReq.getPacInsuranceSelected());
        orderDO.setSfInsuranceAmount(updateOrderLogisticsReq.getSfInsuranceAmount());
        orderDO.setSfInsuranceFee(updateOrderLogisticsReq.getSfInsuranceFee());
        orderDO.setPacInsuranceAmount(updateOrderLogisticsReq.getPacInsuranceAmount());
        orderDO.setPacInsuranceFee(updateOrderLogisticsReq.getPacInsuranceFee());
        // 如果是自提，生成一个9位数的随机自提码
        if (OrderDeliveryTypeEnum.SELF_PICKUP.getCode().equals(updateOrderLogisticsReq.getDeliveryType())) {
            orderDO.setPickupCode(RandomNumberUtil.randomUniqueNumber(9));
        }
        orderRepository.saveAndFlush(orderDO);

        return WrapperUtil.success();
    }


    /**
     * 推送订单到eos
     * @param order
     */
    public void pushSalesOrderToEos(OrderDO order) {
        List<Long> skuIds = order.getProducts().stream().map(OrderProductDO::getSkuId).distinct().collect(Collectors.toList());
        CommoditySkuIdsReq commoditySkuIdsReq = new CommoditySkuIdsReq();
        commoditySkuIdsReq.setIds(skuIds);
        WrapperResp<List<CommoditySkuDetailsResp>> commoditySkuStockRespListWrapperResp = commodityFeign.getCommodityByCommoditySkuIds(commoditySkuIdsReq);
        List<CommoditySkuDetailsResp> commoditySkuStockResps = WrapperUtil.getDataOrThrow(commoditySkuStockRespListWrapperResp);
        for (CommoditySkuDetailsResp commoditySkuStockResp : commoditySkuStockResps) {
            //TODO 自建的商品不存在优时的skuId和spuId，如果不存在，暂时给默认值，后面删除
            if(commoditySkuStockResp.getDataWarehouseSpuId() == null ||  commoditySkuStockResp.getDataWarehouseSpuId() == null){
                commoditySkuStockResp.setDataWarehousePkId("sell_pro_spec:50212");
                commoditySkuStockResp.setDataWarehouseSpuId("sell_pro:665");
            }
        }
        Map<Long, CommoditySkuDetailsResp> downCommoditySkuIdAndCommditySkuMap = commoditySkuStockResps.stream().collect(Collectors.toMap(CommoditySkuDetailsResp::getDownCommoditySkuId, Function.identity()));
        PayFinishAndUnLockReq payFinishAndUnLockReq = new PayFinishAndUnLockReq();
        payFinishAndUnLockReq.setMemberId(order.getBuyerMemberId());
        payFinishAndUnLockReq.setOrderId(order.getId());
        List<OrderPaymentDO> orderPaymentDOS = orderPaymentRepository.findByOrder(order);
        if(CommoditySaleModeEnum.SPOT.getCode().equals(order.getSaleMode())){
            OrderPaymentDO orderPaymentDO = orderPaymentDOS.stream().filter(orderPayment -> orderPayment.getBatchNo().equals(1)).findFirst().get();
            payFinishAndUnLockReq.setPaymentId(orderPaymentDO.getId());
            //推送现货销售订单到eos
            PushSalesSportOrderReq pushSalesSportOrderReq = buildPushSalesSportOrderParam(order, downCommoditySkuIdAndCommditySkuMap, orderPaymentDO);
            eosApiService.pushSalesSportOrder(pushSalesSportOrderReq);
            //解锁冻结资金
            assetAccountFeign.payFinishAndUnLock(payFinishAndUnLockReq);
        }else{
            //推送预售销售订单(定金)到eos
            GetMemberByIdReq getMemberByIdReq = new GetMemberByIdReq();
            getMemberByIdReq.setMemberId(order.getBuyerMemberId());
            WrapperResp<MemberFeignCodeRes> buyerMemberFeignCodeResWrapperResp = memberInnerControllerFeign.findMemberById(getMemberByIdReq);
            MemberFeignCodeRes buyerMemberInfo = WrapperUtil.getDataOrThrow(buyerMemberFeignCodeResWrapperResp, ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
            pushSaleOrder(order, buyerMemberInfo, downCommoditySkuIdAndCommditySkuMap);
        }
    }

    /**
     * 订货订单尾款支付通知
     * @param order
     */
    private void balancePymentNotify(OrderDO order) {
        List<Long> skuIds = order.getProducts().stream().map(OrderProductDO::getSkuId).distinct().collect(Collectors.toList());
        CommoditySkuIdsReq memberIdsReq = new CommoditySkuIdsReq();
        memberIdsReq.setIds(skuIds);
        WrapperResp<List<CommoditySkuDetailsResp>> commoditySkuStockRespListWrapperResp = commodityFeign.getCommodityByCommoditySkuIds(memberIdsReq);
        List<CommoditySkuDetailsResp> commoditySkuStockResps = WrapperUtil.getDataOrThrow(commoditySkuStockRespListWrapperResp);
        for (CommoditySkuDetailsResp commoditySkuStockResp : commoditySkuStockResps) {
            //TODO 自建的商品不存在优时的skuId和spuId，如果不存在，暂时给默认值，后面删除
            if(commoditySkuStockResp.getDataWarehouseSpuId() == null ||  commoditySkuStockResp.getDataWarehouseSpuId() == null){
                commoditySkuStockResp.setDataWarehousePkId("sell_pro_spec:50212");
                commoditySkuStockResp.setDataWarehouseSpuId("sell_pro:665");
            }
        }
        Map<Long, CommoditySkuDetailsResp> downCommoditySkuIdAndCommditySkuMap = commoditySkuStockResps.stream().collect(Collectors.toMap(CommoditySkuDetailsResp::getDownCommoditySkuId, Function.identity()));
        PayFinishAndUnLockReq payFinishAndUnLockReq = new PayFinishAndUnLockReq();
        payFinishAndUnLockReq.setMemberId(order.getBuyerMemberId());
        payFinishAndUnLockReq.setOrderId(order.getId());
        List<OrderPaymentDO> orderPaymentDOS = orderPaymentRepository.findByOrder(order);
        OrderPaymentDO orderPaymentDO = orderPaymentDOS.stream().filter(orderPayment -> orderPayment.getBatchNo().equals(2)).findFirst().get();
        payFinishAndUnLockReq.setPaymentId(orderPaymentDO.getId());
        PushSalesSportOrderReq pushSalesSportOrderReq = buildPushSalesSportOrderParam(order, downCommoditySkuIdAndCommditySkuMap, orderPaymentDO);
        eosApiService.balancePymentNotify(pushSalesSportOrderReq);
        assetAccountFeign.payFinishAndUnLock(payFinishAndUnLockReq);
    }

    /**
     * 推送销售订单(定金)
     * @param orderDO
     * @param buyerMemberInfo
     * @param downCommoditySkuIdAndCommditySkuMap
     */
    private void pushSaleOrder(OrderDO orderDO, MemberFeignCodeRes buyerMemberInfo, Map<Long, CommoditySkuDetailsResp> downCommoditySkuIdAndCommditySkuMap) {
        SalesOrderGoodsPush orderPushReq = new SalesOrderGoodsPush();
        orderPushReq.setDjhm(orderDO.getOrderNo());
        orderPushReq.setDdlx(CommoditySaleModeEnum.getNameByCode(orderDO.getSaleMode()));
        orderPushReq.setXdrqh(DateTimeUtil.formatDate(orderDO.getSubmitTime()));
        Set<OrderProductDO> products = orderDO.getProducts();
        if(products != null && products.iterator().hasNext()) {
            OrderProductDO orderProductDO = orderDO.getProducts().iterator().next();
            orderPushReq.setKhjhrqh(DateTimeUtil.formatDate(orderProductDO.getExpectedDeliveryDate()));
            orderPushReq.setHfjq(DateTimeUtil.formatDate(orderProductDO.getPromisedDeliveryDate()));
            orderPushReq.setCzbm(orderProductDO.getMaterialInfo());
            orderPushReq.setCkmc(orderProductDO.getWarehouseName());
            orderPushReq.setCzmc(orderProductDO.getMaterialInfo());
        }
        orderPushReq.setDdzt(VendorInnerStatusEnum.getNameByCode(orderDO.getVendorInnerStatus()));
        orderPushReq.setKhbm(buyerMemberInfo.getCode());
        orderPushReq.setKhmc(buyerMemberInfo.getName());

        orderPushReq.setZdsj(DateTimeUtil.formatDateTime(orderDO.getCreateTime()));
        orderPushReq.setHjxdjs(orderDO.getProducts().stream().map(OrderProductDO::getQuantity).filter(Objects::isNull).reduce(BigDecimal.ZERO,BigDecimal::add).intValue());
        orderPushReq.setHjxdzjz(orderDO.getProducts().stream().map(OrderProductDO::getSkuGoldWeight).filter(Objects::isNull).reduce(BigDecimal.ZERO,BigDecimal::add));

        if(orderDO.getSealsSet() != null && orderDO.getSealsSet().iterator().hasNext()) {
            OrderSealDO orderSealDO = orderDO.getSealsSet().iterator().next();
            orderPushReq.setZynr(orderSealDO.getSealContent());
        }
        List<SalesOrderGoodsDetailReq> data = new ArrayList<>();
        orderDO.getProducts().stream().forEach(product -> {
            CommoditySkuDetailsResp commoditySkuResp = downCommoditySkuIdAndCommditySkuMap.get(product.getSkuId());
            SalesOrderGoodsDetailReq salesOrderGoodsDetailReq = new SalesOrderGoodsDetailReq();
            salesOrderGoodsDetailReq.setXh(product.getId().intValue());
            salesOrderGoodsDetailReq.setKsbm(product.getProductNo());
            salesOrderGoodsDetailReq.setKsmc(product.getName());
            salesOrderGoodsDetailReq.setGg(product.getSpec());
            salesOrderGoodsDetailReq.setXdjs(product.getQuantity() != null ? product.getQuantity().intValue() : null);
            salesOrderGoodsDetailReq.setDw(product.getUnit());
            salesOrderGoodsDetailReq.setBzjz(product.getNetWeight() != null ? product.getNetWeight().toString() : null);
            salesOrderGoodsDetailReq.setZxjz(commoditySkuResp.getWeightMin());
            salesOrderGoodsDetailReq.setZdjz(commoditySkuResp.getWeightMax());
            salesOrderGoodsDetailReq.setXdzjz(BigDecimalUtil.multiply(product.getSkuGoldWeight(), product.getQuantity()));
            salesOrderGoodsDetailReq.setJbgf(product.getDiscountedBaseLaborCosts() !=null ? product.getDiscountedBaseLaborCosts() : product.getBaseLaborCosts());
            salesOrderGoodsDetailReq.setXsjgf(product.getDiscountedPieceLaborCosts() != null ? product.getDiscountedPieceLaborCosts() : product.getPieceLaborCosts());
            salesOrderGoodsDetailReq.setXskgf(product.getDiscountedAdditionalLaborCosts() != null ? product.getDiscountedAdditionalLaborCosts() : product.getAdditionalLaborCosts());
            if(commoditySkuResp.getCommodityPic() != null){
                salesOrderGoodsDetailReq.setTpsy(commoditySkuResp.getCommodityPic().length > 0 ? commoditySkuResp.getCommodityPic()[0] : null);
                salesOrderGoodsDetailReq.setTpsy1(commoditySkuResp.getCommodityPic().length > 1 ? commoditySkuResp.getCommodityPic()[1] : null);
            }
            salesOrderGoodsDetailReq.setCzbmb(commoditySkuResp.getMaterialCode());
            if(nonSyncData(commoditySkuResp)){
                orderPushReq.setCzbm(EosApiParamConstant.MATERIAL_CODE);
                orderPushReq.setCkmc(EosApiParamConstant.WAREHOUSE_CODE);
                orderPushReq.setCkbm(EosApiParamConstant.WAREHOUSE_NAME);
                orderPushReq.setCzmc(EosApiParamConstant.MATERIAL_NAME);
                salesOrderGoodsDetailReq.setCzbmb(EosApiParamConstant.MATERIAL_CODE);
            }
            salesOrderGoodsDetailReq.setSkunumber(commoditySkuResp.getCode());
            salesOrderGoodsDetailReq.setCzcsb(product.getFineness());
            salesOrderGoodsDetailReq.setJhrqb(product.getPromisedDeliveryDate() != null ? DateTimeUtil.formatDate(product.getPromisedDeliveryDate()) : null);
            salesOrderGoodsDetailReq.setBz(product.getRemark());
            data.add(salesOrderGoodsDetailReq);
        });
        orderPushReq.setData(data);
        List<OrderPaymentDO> orderPaymentDOS = orderPaymentRepository.findByOrder(orderDO);
        //获取定金的支付记录
        OrderPaymentDO orderPaymentDO = orderPaymentDOS.stream().filter(orderPayment -> orderPayment.getBatchNo().equals(1) && orderPayment.getConfirmTime() != null).findFirst().get();
        OrderPayDetailsReq orderPayDetailsReq = new OrderPayDetailsReq();
        orderPayDetailsReq.setXhf(1);
        orderPayDetailsReq.setZfqdf(OrderPayChannelEnum.getNameByCode(orderPaymentDO.getPayChannel()));
        orderPayDetailsReq.setZflxf(OrderPayTypeEnum.getNameByCode(orderPaymentDO.getPayType()));
        orderPayDetailsReq.setZflshf(orderPaymentDO.getChannelTradeNo());
        orderPayDetailsReq.setFs(EosApiParamConstant.DEPOSIT);
        orderPayDetailsReq.setJef(orderPaymentDO.getPayAmount());
        orderPayDetailsReq.setJzf(orderPaymentDO.getTotalWeight());
        orderPayDetailsReq.setBzf(EosApiParamConstant.DEPOSIT);
        orderPushReq.setDataF(orderPayDetailsReq);
        eosApiService.pushSalesGoodsOrder(orderPushReq);
    }

    /**
     * 推送现货销售订单到eos
     * @param orderDO
     */
    public PushSalesSportOrderReq buildPushSalesSportOrderParam(OrderDO orderDO, Map<Long, CommoditySkuDetailsResp> downCommoditySkuIdAndCommditySkuMap, OrderPaymentDO orderPaymentDO) {
        PushSalesSportOrderReq checkPickingNotifyReq = new PushSalesSportOrderReq();
        checkPickingNotifyReq.setDjzt(BuyerInnerStatusEnum.getNameByCode(orderDO.getBuyerInnerStatus()));
        checkPickingNotifyReq.setRq(DateTimeUtil.getCurrentDate());
        checkPickingNotifyReq.setDjh(orderDO.getOrderNo());
        checkPickingNotifyReq.setKhbm(orderDO.getBuyerErpCode());
        checkPickingNotifyReq.setKhmc(orderDO.getBuyerMemberName());
        checkPickingNotifyReq.setSehj(orderDO.getTaxes());
        checkPickingNotifyReq.setMzhj(orderDO.getTotalWeight());
        checkPickingNotifyReq.setCkjzhj(orderDO.getTotalWeight());
        checkPickingNotifyReq.setJshj(orderDO.getProducts().stream().map(OrderProductDO::getQuantity).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add).intValue());
        checkPickingNotifyReq.setZdsj(orderDO.getCreateTime() != null ? DateTimeUtil.formatDate(orderDO.getCreateTime()) : null);
        checkPickingNotifyReq.setZjexsw(orderDO.getTotalAmount().multiply(BigDecimal.valueOf(100)).intValue());
        checkPickingNotifyReq.setQzjzxj(orderDO.getTotalWeight());
        List<PushSalesSportOrderDetailReq> pushSalesSportOrderDetailReqs = new ArrayList<>();
        checkPickingNotifyReq.setBt_content(pushSalesSportOrderDetailReqs);
        //工费统计
        BigDecimal sumBaseLaborCosts = BigDecimal.ZERO;
        BigDecimal sumAdditionalLaborCosts = BigDecimal.ZERO;
        BigDecimal sumPieceLaborCostsCosts = BigDecimal.ZERO;
        BigDecimal sumDiscountedBaseLaborCosts = BigDecimal.ZERO;
        BigDecimal sumDiscountedPieceLaborCosts = BigDecimal.ZERO;
        BigDecimal sumDiscountedAdditionalLaborCosts = BigDecimal.ZERO;
        //订单明细
        for (OrderProductDO product : orderDO.getProducts()) {
            PushSalesSportOrderDetailReq pushSalesSportOrderDetailReq = new PushSalesSportOrderDetailReq();
            pushSalesSportOrderDetailReq.setBtxh(product.getId());
            pushSalesSportOrderDetailReq.setDdhm(orderDO.getOrderNo());
            pushSalesSportOrderDetailReq.setSptm(product.getSingleCode());
            pushSalesSportOrderDetailReq.setCkbmb(product.getWarehouseCode());
            checkPickingNotifyReq.setJsckbm(product.getWarehouseCode());
            checkPickingNotifyReq.setJsckmc(product.getWarehouseName());
            //TODO 如果没有仓库编码先写死
            if(StringUtil.isBlank(product.getWarehouseCode())){
                pushSalesSportOrderDetailReq.setCkbmb(EosApiParamConstant.WAREHOUSE_CODE);
                checkPickingNotifyReq.setJsckbm(EosApiParamConstant.WAREHOUSE_CODE);
                checkPickingNotifyReq.setJsckmc(EosApiParamConstant.WAREHOUSE_NAME);
            }
            pushSalesSportOrderDetailReq.setCkmcb(product.getWarehouseName());
            checkPickingNotifyReq.setCzjh(product.getFineness());
            Set<CommodityExtraDataParamDO> commodityExtraDataParams = product.getCommodityExtraDataParams();
            if(!CollectionUtils.isEmpty(commodityExtraDataParams)){
                CommodityExtraDataParamDO commodityExtraDataParamDO = commodityExtraDataParams.iterator().next();
                pushSalesSportOrderDetailReq.setWpfl(commodityExtraDataParamDO.getType());
                pushSalesSportOrderDetailReq.setWpbm(commodityExtraDataParamDO.getCode());
                pushSalesSportOrderDetailReq.setWpmc(commodityExtraDataParamDO.getValue());
            }else{
                //同步数据缺少这些字段，如果没有先写死
                pushSalesSportOrderDetailReq.setWpfl("古法");
                pushSalesSportOrderDetailReq.setWpbm(EosApiParamConstant.ITEM_CODE);
                pushSalesSportOrderDetailReq.setWpmc(EosApiParamConstant.ITEM_NAME);
            }
            pushSalesSportOrderDetailReq.setCzmcb(product.getFineness());
            //TODO 先写死，等eos调整接口在删除
            pushSalesSportOrderDetailReq.setCzbmb(EosApiParamConstant.MATERIAL_CODE);
            //基本工费
            BigDecimal minDiscountedBaseLaborCosts = product.getDiscountedBaseLaborCosts().divide(product.getNetWeight(), 2, BigDecimal.ROUND_HALF_UP);
            BigDecimal minOriginalBaseLaborCosts = product.getBaseLaborCosts().divide(product.getNetWeight(), 2, BigDecimal.ROUND_HALF_UP);
            BigDecimal minBaseLaborCosts = BigDecimalUtil.subtract(minOriginalBaseLaborCosts, minDiscountedBaseLaborCosts);

            //附加克工费
            //BigDecimal additionalLaborCosts = BigDecimalUtil.subtract(product.getAdditionalLaborCosts(), product.getDiscountedAdditionalLaborCosts());
            BigDecimal minDiscountedAdditionalLaborCosts = product.getDiscountedAdditionalLaborCosts().divide(product.getNetWeight(), 2, BigDecimal.ROUND_HALF_UP);
            BigDecimal minOriginalAdditionalLaborCosts = product.getAdditionalLaborCosts().divide(product.getNetWeight(), 2, BigDecimal.ROUND_HALF_UP);
            BigDecimal minAdditionalLaborCosts = BigDecimalUtil.subtract(minOriginalAdditionalLaborCosts, minDiscountedAdditionalLaborCosts);

            //件工费
            BigDecimal pieceLaborCosts = BigDecimalUtil.subtract(product.getPieceLaborCosts(), product.getDiscountedPieceLaborCosts());
            BigDecimal minDiscountedPieceLaborCosts = product.getDiscountedPieceLaborCosts().divide(product.getQuantity(), 2, BigDecimal.ROUND_HALF_UP);
            BigDecimal minOriginalPieceLaborCosts = product.getPieceLaborCosts().divide(product.getQuantity(), 2, BigDecimal.ROUND_HALF_UP);
            BigDecimal minPieceLaborCosts = BigDecimalUtil.subtract(minOriginalPieceLaborCosts, minDiscountedPieceLaborCosts);
            pushSalesSportOrderDetailReq.setYjbgf(minOriginalBaseLaborCosts);
            pushSalesSportOrderDetailReq.setJbgfyh(minDiscountedBaseLaborCosts);
            pushSalesSportOrderDetailReq.setJbgf(minBaseLaborCosts);
            pushSalesSportOrderDetailReq.setYfjkgf(minOriginalAdditionalLaborCosts);
            pushSalesSportOrderDetailReq.setKgfyh(minDiscountedAdditionalLaborCosts);
            pushSalesSportOrderDetailReq.setFjkgf(minAdditionalLaborCosts);
            pushSalesSportOrderDetailReq.setYfjjgf(minOriginalPieceLaborCosts);
            pushSalesSportOrderDetailReq.setJgfyh(minDiscountedPieceLaborCosts);
            pushSalesSportOrderDetailReq.setFjgfje(minAdditionalLaborCosts.multiply(product.getNetWeight()).add(minPieceLaborCosts.multiply(product.getQuantity())));
            pushSalesSportOrderDetailReq.setJbgfje(minBaseLaborCosts.multiply(product.getNetWeight()));
            pushSalesSportOrderDetailReq.setKgfje(BigDecimalUtil.add(pushSalesSportOrderDetailReq.getJbgfje(), pushSalesSportOrderDetailReq.getFjkgf()));
            pushSalesSportOrderDetailReq.setJgfje(pieceLaborCosts);
            pushSalesSportOrderDetailReq.setJs(product.getQuantity() != null ? product.getQuantity().intValue() : null);
            pushSalesSportOrderDetailReq.setJz(product.getNetWeight());
            pushSalesSportOrderDetailReq.setBz(product.getRemark());
            pushSalesSportOrderDetailReq.setFjjgf(minPieceLaborCosts);
            pushSalesSportOrderDetailReq.setZgfje(BigDecimalUtil.add(pushSalesSportOrderDetailReq.getJbgfje(), pushSalesSportOrderDetailReq.getFjgfje()));
            pushSalesSportOrderDetailReq.setYzgfje(product.getAdditionalLaborCosts().add(product.getPieceLaborCosts()).add(product.getBaseLaborCosts()));

            //订单工费计算
            sumBaseLaborCosts = BigDecimalUtil.add(product.getBaseLaborCosts(), sumBaseLaborCosts);
            sumAdditionalLaborCosts = BigDecimalUtil.add(product.getAdditionalLaborCosts(), sumAdditionalLaborCosts);
            sumPieceLaborCostsCosts = BigDecimalUtil.add(product.getPieceLaborCosts(), sumPieceLaborCostsCosts);
            sumDiscountedBaseLaborCosts = BigDecimalUtil.add(product.getDiscountedBaseLaborCosts(), sumDiscountedBaseLaborCosts);
            sumDiscountedAdditionalLaborCosts = BigDecimalUtil.add(product.getDiscountedAdditionalLaborCosts(), sumDiscountedAdditionalLaborCosts);
            sumDiscountedPieceLaborCosts = BigDecimalUtil.add(product.getDiscountedPieceLaborCosts(), sumDiscountedPieceLaborCosts);
            pushSalesSportOrderDetailReqs.add(pushSalesSportOrderDetailReq);
        }

        //通过商品计算
        checkPickingNotifyReq.setJbgfhj(sumBaseLaborCosts);
        checkPickingNotifyReq.setFjgfhj(BigDecimalUtil.add(sumAdditionalLaborCosts, sumPieceLaborCostsCosts));
        BigDecimal sumLaborCosts = BigDecimalUtil.add(checkPickingNotifyReq.getJbgfhj(), checkPickingNotifyReq.getFjgfhj());
        checkPickingNotifyReq.setYzgfhj(sumLaborCosts);
        //减掉优惠费用
        BigDecimal sumPayLaborCosts = BigDecimalUtil.subtract(sumLaborCosts, sumDiscountedAdditionalLaborCosts);
        sumPayLaborCosts = BigDecimalUtil.subtract(sumPayLaborCosts, sumDiscountedBaseLaborCosts);
        sumPayLaborCosts = BigDecimalUtil.subtract(sumPayLaborCosts, sumDiscountedPieceLaborCosts);
        checkPickingNotifyReq.setZgfhj(sumPayLaborCosts);
        checkPickingNotifyReq.setJehj(checkPickingNotifyReq.getYzgfhj());
        checkPickingNotifyReq.setJjjzhj(orderDO.getTotalWeight());


        List<PushSalesOrderDetailReq> pushSalesOrderDetailReqs = new ArrayList<>();
        Set<OrderProductDO> products = orderDO.getProducts();
        List<SubOrderPaymentDO> subOrderPaymentDOS = subOrderPaymentRepository.findAllByOrderPaymentIdAndSubStatus(orderPaymentDO.getId(), SubPaymentOrderStatusEnum.SUCCESS.getCode());
        List<OrderPayChannelReq> orderPayChannelReqs = new ArrayList<>();

        //订单支付金额统计
        BigDecimal credit = BigDecimal.ZERO;
        BigDecimal settlementFee = BigDecimal.ZERO;
        BigDecimal settlementGoldWeight = BigDecimal.ZERO;

        //支付明细
        for (SubOrderPaymentDO subOrderPaymentDO : subOrderPaymentDOS) {
            OrderPayChannelReq orderPayChannelReq = new OrderPayChannelReq();
            orderPayChannelReq.setZfxh(subOrderPaymentDO.getId());
            if(OrderPayChannelEnum.CREDIT.getCode().equals(subOrderPaymentDO.getPayChannel())){
                BigDecimal weight = subOrderPaymentDO.getWeight();
                orderPayChannelReq.setJjz(weight);
                credit = BigDecimalUtil.add(credit, weight);
            }else{
                settlementFee = BigDecimalUtil.add(settlementFee, subOrderPaymentDO.getPayAmount());
                settlementGoldWeight = BigDecimalUtil.add(settlementGoldWeight, subOrderPaymentDO.getWeight());
                orderPayChannelReq.setZfje(subOrderPaymentDO.getPayAmount());
            }
            orderPayChannelReq.setJjz(subOrderPaymentDO.getWeight());
            orderPayChannelReq.setZflx(OrderPayTypeEnum.getNameByCode(subOrderPaymentDO.getPayType()));
            orderPayChannelReq.setZfqd(OrderPayChannelEnum.getNameByCode(subOrderPaymentDO.getPayChannel()));
            orderPayChannelReq.setZflsh(subOrderPaymentDO.getChannelTradeNo());
            orderPayChannelReq.setZfsj(DateTimeUtil.formatDateTime(subOrderPaymentDO.getPayTime()));
            orderPayChannelReq.setZfdh(subOrderPaymentDO.getTradeNo());
            orderPayChannelReqs.add(orderPayChannelReq);
        }

        //如果是尾款支付，在加一个定金
        if(orderPaymentDO.getBatchNo() == 2){
            OrderPaymentDO firstOrderPaymentDO = orderPaymentRepository.findByOrder(orderDO).stream().filter(paymentDO -> paymentDO.getBatchNo() == 1).findFirst().orElse(null);
            if(firstOrderPaymentDO != null){
                SubOrderPaymentDO firstSubOrderPaymentDO = subOrderPaymentRepository.findAllByOrderPaymentId(firstOrderPaymentDO.getId()).stream().findFirst().get();
                settlementGoldWeight = BigDecimalUtil.add(settlementGoldWeight, firstSubOrderPaymentDO.getWeight());
                OrderPayChannelReq orderPayChannelReq = new OrderPayChannelReq();
                orderPayChannelReq.setZfxh(firstSubOrderPaymentDO.getId());
                BigDecimal weight = firstSubOrderPaymentDO.getWeight();
                orderPayChannelReq.setJjz(weight);
                orderPayChannelReq.setZfje(firstSubOrderPaymentDO.getPayAmount());
                orderPayChannelReq.setZflx(OrderPayTypeEnum.getNameByCode(firstSubOrderPaymentDO.getPayType()));
                orderPayChannelReq.setZfqd(OrderPayChannelEnum.getNameByCode(firstSubOrderPaymentDO.getPayChannel()));
                orderPayChannelReq.setZflsh(firstSubOrderPaymentDO.getChannelTradeNo());
                orderPayChannelReq.setZfsj(DateTimeUtil.formatDateTime(firstSubOrderPaymentDO.getPayTime()));
                orderPayChannelReq.setZfdh(firstSubOrderPaymentDO.getTradeNo());
                orderPayChannelReqs.add(orderPayChannelReq);
            }
        }
        checkPickingNotifyReq.setZf_content(orderPayChannelReqs);
        int i= 0;
        //余额/线上支付
        if(!CollectionUtils.isEmpty(products)) {
            if(!BigDecimalUtil.isZeroOrNegative(settlementGoldWeight)){
                //余额/其他线上支付（黄金结价）
                PushSalesOrderDetailReq pushSalesOrderDetailReq = new PushSalesOrderDetailReq();
                pushSalesOrderDetailReq.setFtxh(++i);
                pushSalesOrderDetailReq.setFs(EosApiParamConstant.CLOSING_THE_PRICE);
                pushSalesOrderDetailReq.setJzf(settlementGoldWeight);
                pushSalesOrderDetailReq.setSjjzf(pushSalesOrderDetailReq.getJzf());
                pushSalesOrderDetailReq.setCsbmf(EosApiParamConstant.FINENESS_CODE);
                pushSalesOrderDetailReq.setCsmcf(EosApiParamConstant.FINENESS_NAME);
                pushSalesOrderDetailReq.setPlbmf(EosApiParamConstant.CATEGORY_CODE);
                pushSalesOrderDetailReq.setPlmcf(EosApiParamConstant.CATEGORY_NAME);
                pushSalesOrderDetailReq.setJdj(orderDO.getSettlementGoldPrice());
                pushSalesOrderDetailReq.setYgfje(BigDecimalUtil.multiply(settlementGoldWeight, orderDO.getSettlementGoldPrice()));
                pushSalesOrderDetailReq.setGfje(pushSalesOrderDetailReq.getYgfje());
                pushSalesOrderDetailReq.setDrjj(orderDO.getSettlementGoldPrice());
                pushSalesOrderDetailReq.setZshl(BigDecimal.valueOf(1));
                pushSalesOrderDetailReqs.add(pushSalesOrderDetailReq);
            }
            //授信/存料支付
            if(BigDecimalUtil.notNullOrZero(credit)){
                PushSalesOrderDetailReq pushSalesOrderDetailCreditReq = new PushSalesOrderDetailReq();
                pushSalesOrderDetailCreditReq.setFtxh(++i);
                pushSalesOrderDetailCreditReq.setFs(EosApiParamConstant.OWING);
                pushSalesOrderDetailCreditReq.setJzf(credit);
                pushSalesOrderDetailCreditReq.setSjjzf(orderDO.getProducts().stream().map(OrderProductDO::getNetWeight).reduce(BigDecimal.ZERO, BigDecimal::add));
                pushSalesOrderDetailCreditReq.setGfje(orderDO.getTotalAmount());
                pushSalesOrderDetailCreditReq.setDrjj(orderDO.getSettlementGoldPrice());
                pushSalesOrderDetailCreditReq.setBzf(EosApiParamConstant.CREDIT_PAY);
                pushSalesOrderDetailCreditReq.setCsbmf(EosApiParamConstant.FINENESS_CODE);
                pushSalesOrderDetailCreditReq.setCsmcf(EosApiParamConstant.FINENESS_NAME);
                pushSalesOrderDetailCreditReq.setPlbmf(EosApiParamConstant.CATEGORY_CODE);
                pushSalesOrderDetailCreditReq.setPlmcf(EosApiParamConstant.CATEGORY_NAME);
                pushSalesOrderDetailReqs.add(pushSalesOrderDetailCreditReq);
            }
        }

        //服务费用
        if(orderDO.getTotalServicePrice() != null) {
            PushSalesOrderDetailReq pushSalesOrderDetailReq = new PushSalesOrderDetailReq();
            pushSalesOrderDetailReq.setFtxh(++i);
            //pushSalesOrderDetailReq.setXmf(EosApiParamConstant.SERVICE_FEE);
            pushSalesOrderDetailReq.setFs(EosApiParamConstant.SERVICE_FEE);
            pushSalesOrderDetailReq.setJszl(EosApiParamConstant.COST);
            pushSalesOrderDetailReq.setGfje(orderDO.getTotalServicePrice());
            pushSalesOrderDetailReq.setYgfje(orderDO.getTotalServicePrice());
            pushSalesOrderDetailReqs.add(pushSalesOrderDetailReq);
        }

        //（总工费）克工费，附加克工费，附加件工费之和
        if(BigDecimalUtil.notNullOrZero(sumLaborCosts)) {
            PushSalesOrderDetailReq pushSalesOrderDetailReq = new PushSalesOrderDetailReq();
            pushSalesOrderDetailReq.setFtxh(++i);
            //pushSalesOrderDetailReq.setXmf(EosApiParamConstant.CRAFT_FEE);
            pushSalesOrderDetailReq.setFs(EosApiParamConstant.CRAFT_FEE);
            //pushSalesOrderDetailReq.setJszl(EosApiParamConstant.COST);
            pushSalesOrderDetailReq.setGfje(sumLaborCosts);
            pushSalesOrderDetailReq.setYgfje(sumLaborCosts);
            pushSalesOrderDetailReqs.add(pushSalesOrderDetailReq);
        }

        //保价费
        if(orderDO.getSfInsuranceSelected() || orderDO.getPacInsuranceSelected()) {
            PushSalesOrderDetailReq pushSalesOrderDetailReq = new PushSalesOrderDetailReq();
            pushSalesOrderDetailReq.setFtxh(++i);
            pushSalesOrderDetailReq.setFs(EosApiParamConstant.INSURANCE_COST);
            //pushSalesOrderDetailReq.setJsf(products.stream().map(OrderProductDO::getQuantity).filter(Objects::isNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            pushSalesOrderDetailReq.setJszl(EosApiParamConstant.COST);
            //pushSalesOrderDetailReq.setJzf(products.stream().map(OrderProductDO::getNetWeight).filter(Objects::isNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            pushSalesOrderDetailReq.setGfje(orderDO.getSfInsuranceSelected() ? orderDO.getSfInsuranceFee() : orderDO.getPacInsuranceFee());
            pushSalesOrderDetailReqs.add(pushSalesOrderDetailReq);
        }

        //其他工费
        BigDecimal otherLaborCosts = BigDecimalUtil.subtract(orderDO.getTotalCraftPrice(), sumPayLaborCosts);
        if(!BigDecimalUtil.isZeroOrNegative(otherLaborCosts)) {
            PushSalesOrderDetailReq pushSalesOrderDetailReq = new PushSalesOrderDetailReq();
            pushSalesOrderDetailReq.setFtxh(++i);
            pushSalesOrderDetailReq.setFs(EosApiParamConstant.OTHER_COST);
            //pushSalesOrderDetailReq.setJsf(products.stream().map(OrderProductDO::getQuantity).filter(Objects::isNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            pushSalesOrderDetailReq.setJszl(EosApiParamConstant.COST);
            //pushSalesOrderDetailReq.setJzf(products.stream().map(OrderProductDO::getNetWeight).filter(Objects::isNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            pushSalesOrderDetailReq.setGfje(otherLaborCosts);
            pushSalesOrderDetailReq.setYgfje(otherLaborCosts);
            pushSalesOrderDetailReqs.add(pushSalesOrderDetailReq);
        }
        checkPickingNotifyReq.setFt_content(pushSalesOrderDetailReqs);
        checkPickingNotifyReq.setJjjehj(pushSalesOrderDetailReqs.stream().filter(pushSalesOrderDetailReq -> EosApiParamConstant.CLOSING_THE_PRICE.equals(pushSalesOrderDetailReq.getFs()) && Objects.nonNull(pushSalesOrderDetailReq.getGfje())).map(PushSalesOrderDetailReq::getGfje).reduce(BigDecimal.ZERO, BigDecimal::add));
        checkPickingNotifyReq.setZqjzhj(pushSalesOrderDetailReqs.stream().filter(pushSalesOrderDetailReq -> EosApiParamConstant.OWING.equals(pushSalesOrderDetailReq.getFs())  && Objects.nonNull(pushSalesOrderDetailReq.getJzf())).map(PushSalesOrderDetailReq::getJzf).reduce(BigDecimal.ZERO, BigDecimal::add));
        checkPickingNotifyReq.setJjjzhj(pushSalesOrderDetailReqs.stream().filter(pushSalesOrderDetailReq -> EosApiParamConstant.CLOSING_THE_PRICE.equals(pushSalesOrderDetailReq.getFs()) && Objects.nonNull(pushSalesOrderDetailReq.getJzf())).map(PushSalesOrderDetailReq::getJzf).reduce(BigDecimal.ZERO, BigDecimal::add));
        checkPickingNotifyReq.setJjjehj(pushSalesOrderDetailReqs.stream().filter(pushSalesOrderDetailReq -> EosApiParamConstant.CLOSING_THE_PRICE.equals(pushSalesOrderDetailReq.getFs())).map(PushSalesOrderDetailReq::getGfje).reduce(BigDecimal.ZERO, BigDecimal::add));
        checkPickingNotifyReq.setJehj(pushSalesOrderDetailReqs.stream().filter(pushSalesOrderDetailReq -> pushSalesOrderDetailReq.getGfje() != null).map(PushSalesOrderDetailReq::getGfje).reduce(BigDecimal.ZERO, BigDecimal::add));
        checkPickingNotifyReq.setYjehj(pushSalesOrderDetailReqs.stream().filter(pushSalesOrderDetailReq -> pushSalesOrderDetailReq.getYgfje() != null).map(PushSalesOrderDetailReq::getYgfje).reduce(BigDecimal.ZERO, BigDecimal::add));
        return checkPickingNotifyReq;
    }



    private boolean nonSyncData(CommoditySkuDetailsResp commoditySkuStockResp){
        if(commoditySkuStockResp == null || commoditySkuStockResp.getSourceType() ==null
                || commoditySkuStockResp.getSourceType() != 3){
            return true;
        }
        return false;
    }

}
