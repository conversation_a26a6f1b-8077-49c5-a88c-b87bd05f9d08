package com.ssy.lingxi.order.model.resp.mobile;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 销售订单审核订单数量角标VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-03-31
 **/
@Getter
@Setter
@NoArgsConstructor
public class MobileVendorValidateSubscriptResp {
    public MobileVendorValidateSubscriptResp(Integer innerStatus, Long orderCount) {
        this.innerStatus = innerStatus;
        this.orderCount = orderCount;
    }

    /**
     * 订单内部状态
     */
    private Integer innerStatus;

    /**
     * 订单数量
     */
    private Long orderCount;
}
