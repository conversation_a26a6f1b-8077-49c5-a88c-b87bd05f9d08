package com.ssy.lingxi.order.model.req.basic;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 订单收货人信息接口参数
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-18
 */
@Getter
@Setter
@NoArgsConstructor
public class OrderConsigneeReq implements Serializable {
    private static final long serialVersionUID = -7774027488102095023L;

    /**
     * 收货人Id
     */
    @NotNull(message = "收货地址的收货人Id要大于0")
    @Positive(message = "收货地址的收货人Id要大于0")
    private Long consigneeId;

    /**
     * 收货人姓名
     */
    @NotBlank(message = "收货地址的收货人姓名不能为空")
    @Size(max = 50, message = "收货地址的收货人姓名最长50个字符")
    private String consignee;

    /**
     * 省编码
     */
//    @NotBlank(message = "收货地址的省编码不能为空")
    private String provinceCode;

    /**
     * 市编码
     */
//    @NotBlank(message = "收货地址的市编码不能为空")
    private String cityCode;

    /**
     * 区编码
     */
    private String districtCode;

    /**
     * 街道编码
     */
    private String streetCode;

    /**
     * 详细地址
     */
    @NotBlank(message = "收货地址的详细地址不能为空")
    @Size(max = 200, message = "收货地址的详细地址最长200个字符")
    private String address;

    /**
     * 邮政编码
     */
    @Size(max = 20, message = "收货地址的邮政编码最长20个字符")
    private String postalCode;

    /**
     * 国家编码（手机号码前缀）
     */
    @NotBlank(message = "收货地址的国家编码不能为空")
    @Size(max = 10, message = "收货地址的国家编码最长10个字符")
    private String countryCode;

    /**
     * 手机号码
     */
    @NotBlank(message = "收货地址的手机号码不能为空")
    @Size(max = 20, message = "收货地址的手机号码最长20个字符")
    private String phone;

    /**
     * 固定电话号码
     */
    @Size(max = 20, message = "收货地址的固话号码最长20个字符")
    private String telephone;

    /**
     * 是否默认，true-是，false-否
     */
    private Boolean defaultConsignee;
}
