package com.ssy.lingxi.order.service.baitai;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.order.model.req.baitai.CalculateReq;
import com.ssy.lingxi.order.model.req.baitai.LogisticsInsuranceFeeSettingCreateReq;
import com.ssy.lingxi.order.model.req.baitai.LogisticsInsuranceFeeSettingStatusReq;
import com.ssy.lingxi.order.model.resp.baitai.CalculateResp;
import com.ssy.lingxi.order.model.resp.baitai.LogisticsInsuranceFeeSettingDetailResp;
import com.ssy.lingxi.order.model.resp.baitai.LogisticsInsuranceFeeSettingPageResp;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/22
 */
public interface LogisticsInsuranceFeeSettingService {

    /**
     * 新增物流保费设置
     *
     * @param request 新增请求参数
     * @return 新增结果
     */
    WrapperResp<Void> create(LogisticsInsuranceFeeSettingCreateReq request);

    /**
     * 更新物流保费设置
     *
     * @param request 更新请求参数
     * @return 更新结果
     */
    WrapperResp<Void> update(LogisticsInsuranceFeeSettingCreateReq request);

    /**
     * 查询物流保费详情
     *
     * @param id 物流保费设置id
     * @return 物流保费设置详情
     */
    WrapperResp<LogisticsInsuranceFeeSettingDetailResp> getDetail(Long id);

    /**
     * 查询物流设置列表
     * @return 查询结果
     */
    WrapperResp<List<LogisticsInsuranceFeeSettingPageResp>> getList();

    /**
     * 根据保额计算保费
     * @param calculateReq 保额
     * @return 保费
     */
    WrapperResp<CalculateResp> calculateInsuranceFee(CalculateReq calculateReq);

    /**
     * 修改保费设置状态
     * @paraml LogisticsInsuranceFeeSettingStatusReq 状态请求参数
     * @return 操作结果
     */
    WrapperResp<Void> updateStatus(LogisticsInsuranceFeeSettingStatusReq request);


}
