package com.ssy.lingxi.order.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.model.resp.select.DropdownItemResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.dataauth.annotation.order.BuyerAuth;
import com.ssy.lingxi.order.api.model.req.OrderPayCallbackFeignReq;
import com.ssy.lingxi.order.model.req.basic.*;
import com.ssy.lingxi.order.model.req.buyer.*;
import com.ssy.lingxi.order.model.req.common.findOrderVersionReq;
import com.ssy.lingxi.order.model.resp.basic.*;
import com.ssy.lingxi.order.model.resp.buyer.*;
import com.ssy.lingxi.order.model.resp.common.OrderChangeRecordResp;
import com.ssy.lingxi.order.service.base.IBaseOrderPaymentService;
import com.ssy.lingxi.order.service.web.IBuyerOrderService;
import com.ssy.lingxi.order.service.web.IOrderChangeService;
import com.ssy.lingxi.order.service.web.IOrderModuleService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * 订单能力 - 采购订单相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-19
 */
@RestController
@RequestMapping(ServiceModuleConstant.ORDER_PATH_PREFIX + "/buyer")
public class BuyerOrderController extends BaseController {
    @Resource
    private IBuyerOrderService buyerOrderService;

    @Resource
    private IOrderModuleService orderModuleService;

    @Resource
    private IOrderChangeService orderChangeService;

    /**
     * “待分配订单” - 获取前端页面下拉框列表
     * @return 查询结果
     */
    @GetMapping("/take/page/items")
    public WrapperResp<List<DropdownItemResp>> getTakePageItems() {
        return WrapperUtil.success(buyerOrderService.getTakePageItems(getSysUser()));
    }

    /**
     * “待分配订单” - 查询订单列表
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/take/page")
    public WrapperResp<PageDataResp<BaseBuyerOrderQueryResp>> pageToTakeOrders(@Valid OrderPageDataReq pageVO) {
        return WrapperUtil.success(buyerOrderService.pageToTakeOrders(getSysUser(), pageVO));
    }

    /**
     * “待分配订单” - 订单详情
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/take/detail")
    public WrapperResp<BuyerOrderDetailResp> getToTakeOrderDetail(@Valid OrderIdReq idVO) {
        return WrapperUtil.success(buyerOrderService.getToTakeOrderDetail(getSysUser(), idVO));
    }

    /**
     * “待分配订单” - 领取订单
     * @param idVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/take")
    public WrapperResp<Void> takeOrder(@RequestBody @Valid OrderIdReq idVO) {
        return WrapperUtil.success(buyerOrderService.takeOrder(getSysUser(), idVO));
    }

    /**
     * “待分配订单” - 批量领取订单
     * @param orderIds 订单Id列表
     * @return 操作结果
     */
    @PostMapping("/take/batch")
    public WrapperResp<Void> batchTakeOrders(@RequestBody @Valid List<OrderIdReq> orderIds) {
        return WrapperUtil.success(buyerOrderService.batchTakeOrders(getSysUser(), orderIds));
    }

    /**
     * （订单查询页面）获取前端页面下拉框列表
     * @return 查询结果
     */
    @GetMapping("/page/items")
    public WrapperResp<PageItemResp> getPageItems() {
        return WrapperUtil.success(buyerOrderService.getPageItems(getSysUser()));
    }

    /**
     * 分页查询订单
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @BuyerAuth
    @GetMapping("/page")
    public WrapperResp<PageDataResp<BuyerOrderQueryResp>> pageOrders(@Valid OrderManagePageDataReq pageVO) {
        return WrapperUtil.success(buyerOrderService.pageOrders(getSysUser(), pageVO));
    }

    /**
     * 订单退款
     * @param idVO 接口参数
     * @return 退款结果
     */
    @PostMapping("/refund")
    public WrapperResp<Void> orderRefund(@RequestBody @Valid OrderIdReq idVO) {
        return WrapperUtil.success(buyerOrderService.orderRefund(getSysUser(), idVO));
    }

    @Resource
    private IBaseOrderPaymentService baseOrderPaymentService;
    /**
     * 订单退款
     * @param idVO 接口参数
     * @return 退款结果
     */
    @PostMapping("/refundNew")
    public WrapperResp<Void> orderRefundNew(@RequestBody @Valid OrderIdReq idVO) {
        baseOrderPaymentService.orderRefundNew(null,null,null);
        return WrapperUtil.success();
    }

    /**
     * 分页查询订单 - 订单删除
     * @param idVO    接口参数
     * @return 操作结果
     */
    @PostMapping("/page/delete")
    public WrapperResp<Void> pageOrderDelete(@RequestBody @Valid OrderIdReq idVO) {
        return WrapperUtil.success(buyerOrderService.pageOrderDelete(getSysUser(), idVO));
    }

    /**
     * 订单详情
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/detail")
    public WrapperResp<BuyerOrderDetailResp> getOrderDetail(@Valid OrderIdReq idVO) {
        return WrapperUtil.success(buyerOrderService.getOrderDetail(getSysUser(), idVO));
    }

    /**
     * 采购订单详情 - 申请开票
     * @param invoiceVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/invoice")
    public WrapperResp<Void> applyInvoice(@RequestBody @Valid BuyerApplyInvoiceReq invoiceVO) {
        return WrapperUtil.success(buyerOrderService.applyInvoice(getSysUser(), invoiceVO));
    }

    /**
     * 取消订单
     * @param reasonVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/cancel")
    public WrapperResp<Void> cancelOrder(@RequestBody @Valid OrderReasonReq reasonVO) {
        return WrapperUtil.success(buyerOrderService.cancelOrder(getSysUser(), reasonVO));
    }

    /**
     * 订单查询导出
     * @param accessToken 登录token
     * @param pageVO 接口参数
     * @param response 响应体
     */
    @GetMapping("/export")
    public void exportOrders(HttpServletResponse response, @Valid OrderManagePageDataReq pageVO, String accessToken) {
        buyerOrderService.exportOrders(response, pageVO, accessToken, getSysUser());
    }

    /**
     * 根据供应商会员信息和商城id获取预约时长和配置配送时间段
     * @param deliveryDateReq 接口参数
     * @return 查询结果
     */
    @GetMapping("/find/delivery/date")
    public WrapperResp<OrderDeliveryDateResp> findDeliveryDate(@Valid DeliveryDateReq deliveryDateReq) {
        return WrapperUtil.success(buyerOrderService.findBuyerDeliveryDate(getSysUser(), deliveryDateReq));
    }

    /**
     * 查看订单送货时间
     * @param timeVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/get/delivery/time")
    public WrapperResp<OrderDeliverTimeDetailResp> getDeliveryTime(@Valid BuyerOrderDeliveryTimeReq timeVO) {
        return WrapperUtil.success(buyerOrderService.getDeliveryTime(getSysUser(), timeVO));
    }

    /**
     * 未发货调整订单送货时间
     * @param updateVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/update/delivery/time")
    public WrapperResp<Void> updateDeliveryTime(@RequestBody @Valid BuyerOrderDeliveryTimeUpdateReq updateVO) {
        return WrapperUtil.success(buyerOrderService.updateDeliveryTime(getSysUser(), updateVO));
    }

    /**
     * “待新增订单” - 获取前端页面下拉框列表
     * @return 查询结果
     */
    @GetMapping("/create/page/items")
    public WrapperResp<BuyerToCreatePageItemResp> getToCreatePageItems() {
        return WrapperUtil.success(buyerOrderService.getToCreatePageItems(getSysUser()));
    }

    /**
     * “待新增订单” - 分页查询订单列表
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @BuyerAuth
    @GetMapping("/create/page")
    public WrapperResp<PageDataResp<BuyerToCreateQueryResp>> pageToCreateOrders(@Valid OrderPageDataReq pageVO) {
        return WrapperUtil.success(buyerOrderService.pageToCreateOrders(getSysUser(), pageVO));
    }

    /**
     * “新增订单” - “通用” - 查询订单详情
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/create/detail")
    public WrapperResp<BuyerOrderDetailResp> getToCreateOrderDetails(@Valid OrderIdReq idVO) {
        return WrapperUtil.success(buyerOrderService.getToCreateOrderDetails(getSysUser(), idVO));
    }

    /**
     * “待新增订单” - 创建SRM订单
     * @param orderVO 接口参数
     * @return 创建结果
     */
    @PostMapping("/create/srm")
    public WrapperResp<Void> createSrmOrder(@RequestBody @Valid BuyerSrmOrderReq orderVO) {
        return WrapperUtil.success(buyerOrderService.createSrmOrder(getSysUser(), orderVO));
    }

    /**
     * 变更SRM订单
     * @param orderVO 接口参数
     * @return 创建结果
     */
    @PostMapping("/change/srm")
    public WrapperResp<Void> changeSrmOrder(@RequestBody @Valid BuyerSrmOrderChangeReq orderVO) {
        return WrapperUtil.success(buyerOrderService.changeSrmOrder(getSysUser(), orderVO));
    }

    /**
     * “通用” - 查询订单变更记录详情
     * @param versionVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/change/version")
    public WrapperResp<OrderChangeRecordResp> getVersionOrder(@Valid findOrderVersionReq versionVO) {
        return WrapperUtil.success(orderChangeService.getVersionOrder(getSysUser(), versionVO));
    }


    /**
     * “待新增订单” - 修改Srm订单
     * @param updateVO 接口参数
     * @return 修改结果
     */
    @PostMapping("/create/srm/update")
    public WrapperResp<Void> updateSrmOrder(@RequestBody @Valid BuyerSrmOrderUpdateReq updateVO) {
        return WrapperUtil.success(buyerOrderService.updateSrmOrder(getSysUser(), updateVO));
    }

    /**
     * “新增订单” - 分页查询SRM订单列表
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @BuyerAuth
    @GetMapping("/create/srm/page")
    public WrapperResp<PageDataResp<BuyerToCreateQueryResp>> pageSrmOrders(@Valid OrderPageDataReq pageVO) {
        return WrapperUtil.success(buyerOrderService.pageSrmOrders(getSysUser(), pageVO));
    }

    /**
     * “SRM订单” - 查看物料关联的请购单
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/find/requisition")
    public WrapperResp<PageDataResp<OrderRequisitionQueryResp>> findSrmRequisition(@Valid OrderRequisitionPageDataReq pageVO) {
        return WrapperUtil.success(buyerOrderService.findSrmRequisition(getSysUser(), pageVO));
    }

    /**
     * “新增订单” - 通用 - 查询支付环节列表
     * @param payVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/create/payment/find")
    public WrapperResp<List<BuyerOrderPayNodeDetailResp>> findBusinessOrderProcessPayment(@RequestBody @Valid OrderProcessPayReq payVO) {
        return WrapperUtil.success(buyerOrderService.findBusinessOrderProcessPayment(getSysUser(), payVO));
    }

    /**
     * “新增订单” - “修改订单” - “通用” - 查询支付环节列表
     * @param updateVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/create/update/payment/find")
    public WrapperResp<List<BuyerOrderPayNodeQueryResp>> findBusinessOrderProcessPaymentUpdate(@Valid BuyerBusinessOrderProcessPayUpdateReq updateVO) {
        return WrapperUtil.success(buyerOrderService.findBusinessOrderProcessPaymentUpdate(getSysUser(), updateVO));
    }

    /**
     * “新增订单” - 通用 - 查询支付方式与支付渠道列表
     * @param vendorMemberVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/create/pay/types")
    public WrapperResp<List<OrderPayTypeDetailResp>> findBusinessOrderPayTypes(@Valid OrderVendorMemberReq vendorMemberVO) {
        return WrapperUtil.success(buyerOrderService.findBusinessOrderPayTypes(getSysUser(), vendorMemberVO));
    }

    /**
     * “待新增订单” - 创建B2B订单
     * @param orderVO 接口参数
     * @return 创建结果
     */
    @PostMapping("/create/b2b")
    public WrapperResp<Void> createBusinessOrder(@RequestBody @Valid BuyerBusinessOrderReq orderVO) {
        return WrapperUtil.success(buyerOrderService.createBusinessOrder(getSysUser(), orderVO));
    }

    /**
     * “待新增订单” - 修改B2B订单
     * @param updateVO 接口参数
     * @return 创建结果
     */
    @PostMapping("/create/b2b/update")
    public WrapperResp<Void> updateBusinessOrder(@RequestBody @Valid BuyerBusinessOrderUpdateReq updateVO) {
        return WrapperUtil.success(buyerOrderService.updateBusinessOrder(getSysUser(), updateVO));
    }

    /**
     * “新增现货采购订单” - 分页查询现货采购订单列表
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @BuyerAuth
    @GetMapping("/create/purchase/page")
    public WrapperResp<PageDataResp<BuyerToCreateQueryResp>> pagePurchaseOrders(@Valid BuyerOrderCreatePageDataReq pageVO) {
        return WrapperUtil.success(buyerOrderService.pagePurchaseOrders(getSysUser(), pageVO));
    }

    /**
     * “新增现货采购订单” - 根据商城类型查询订单类型和订单模式
     * @param shopTypeVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/create/purchase/types")
    public WrapperResp<List<OrderModeResp>> findPurchaseOrderTypes(@Valid PurchaseOrderShopTypeReq shopTypeVO) {
        return WrapperUtil.success(buyerOrderService.findPurchaseOrderTypes(getSysUser(), shopTypeVO));
    }

    /**
     * “新增现货采购订单” - 创建现货采购订单
     * @param orderVO 接口参数
     * @return 新增结果
     */
    @PostMapping("/create/purchase")
    public WrapperResp<Void> createPurchaseOrder(@RequestBody @Valid BuyerPurchaseOrderReq orderVO) {
        return WrapperUtil.success(buyerOrderService.createPurchaseOrder(getSysUser(), orderVO));
    }

    /**
     * “新增现货采购订单” - 修改现货采购订单
     * @param updateVO 接口参数
     * @return 新增结果
     */
    @PostMapping("/create/purchase/update")
    public WrapperResp<Void> updatePurchaseOrder(@RequestBody @Valid BuyerPurchaseOrderUpdateReq updateVO) {
        return WrapperUtil.success(buyerOrderService.updatePurchaseOrder(getSysUser(), updateVO));
    }

    /**
     * 请购单订单 - 新增订单
     * @param orderVO 接口参数
     * @return 新增结果
     */
    @PostMapping("/create/requisition")
    public WrapperResp<Void> createRequisitionOrder(@RequestBody @Valid RequisitionOrderCreateReq orderVO) {
        return WrapperUtil.success(buyerOrderService.createRequisitionOrder(getSysUser(), orderVO));
    }

    /**
     * 请购单订单 - 变更订单
     * @param orderVO 接口参数
     * @return 新增结果
     */
    @PostMapping("/change/requisition")
    public WrapperResp<Void> changeRequisitionOrder(@RequestBody @Valid RequisitionOrderChangeReq orderVO) {
        return WrapperUtil.success(buyerOrderService.changeRequisitionOrder(getSysUser(), orderVO));
    }

    /**
     * 请购单订单 - 修改订单
     * @param orderVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/update/requisition")
    public WrapperResp<Void> updateRequisitionOrder(@RequestBody @Valid RequisitionOrderUpdateReq orderVO) {
        return WrapperUtil.success(buyerOrderService.updateRequisitionOrder(getSysUser(), orderVO));
    }

    /**
     * 新增请购单订单 - 分页查询待新增订单
     * @param pageVO 接口参数
     * @return 新增结果
     */
    @BuyerAuth
    @GetMapping("/create/requisition/page")
    public WrapperResp<PageDataResp<BuyerToCreateQueryResp>> createRequisitionPage(@Valid BuyerOrderCreatePageDataReq pageVO) {
        return WrapperUtil.success(buyerOrderService.createRequisitionPage(getSysUser(), pageVO));
    }

    /**
     * 物料订单 - 新增订单
     * @param orderVO 接口参数
     * @return 新增结果
     */
    @PostMapping("/create/materiel")
    public WrapperResp<Void> createMaterielOrder(@RequestBody @Valid MaterielOrderCreateReq orderVO) {
        return WrapperUtil.success(buyerOrderService.createMaterielOrder(getSysUser(), orderVO));
    }

    /**
     * 物料订单 - 变更订单
     * @param orderVO 接口参数
     * @return 新增结果
     */
    @PostMapping("/change/materiel")
    public WrapperResp<Void> changeMaterielOrder(@RequestBody @Valid MaterielOrderChangeReq orderVO) {
        return WrapperUtil.success(buyerOrderService.changeMaterielOrder(getSysUser(), orderVO));
    }

    /**
     * 物料订单 - 修改订单
     * @param orderVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/update/materiel")
    public WrapperResp<Void> updateMaterielOrder(@RequestBody @Valid MaterielOrderUpdateReq orderVO) {
        return WrapperUtil.success(buyerOrderService.updateMaterielOrder(getSysUser(), orderVO));
    }

    /**
     * 物料订单 - 分页查询待新增订单列表
     * @param pageVO 接口参数
     * @return 列表数据
     */
    @BuyerAuth
    @GetMapping("/materiel/page")
    public WrapperResp<PageDataResp<BuyerToCreateQueryResp>> materielPage(@Valid BuyerOrderCreatePageDataReq pageVO) {
        return WrapperUtil.success(buyerOrderService.materielPage(getSysUser(), pageVO));
    }

    /**
     * “新增订单” - “通用” - 删除订单
     * @param idVO 接口参数
     * @return 删除结果
     */
    @PostMapping("/create/delete")
    public WrapperResp<Void> deleteOrder(@RequestBody @Valid OrderIdReq idVO) {
        return WrapperUtil.success(buyerOrderService.deleteOrder(getSysUser(), idVO));
    }

    /**
     * “新增订单” - “通用” - 批量删除订单
     * @param orderIds 接口参数
     * @return 删除结果
     */
    @PostMapping("/create/delete/batch")
    public WrapperResp<Void> batchDeleteOrder(@RequestBody @Valid List<OrderIdReq> orderIds) {
        return WrapperUtil.success(buyerOrderService.batchDeleteOrder(getSysUser(), orderIds));
    }

    /**
     * “新增订单” - “通用” - 提交
     * @param idVO 接口参数
     * @return 删除结果
     */
    @PostMapping("/create/submit")
    public WrapperResp<Void> submitOrder(@RequestBody @Valid OrderIdReq idVO) {
        return WrapperUtil.success(buyerOrderService.submitOrder(getSysUser(), idVO));
    }

    /**
     * “新增订单” - “通用” - 批量提交
     * @param orderIds 接口参数
     * @return 删除结果
     */
    @PostMapping("/create/submit/batch")
    public WrapperResp<Void> batchSubmitOrder(@RequestBody @Valid List<OrderIdReq> orderIds) {
        return WrapperUtil.success(buyerOrderService.batchSubmitOrder(getSysUser(), orderIds));
    }

    /**
     * （订单审核各个页面）获取前端页面下拉框列表
     * @return 查询结果
     */
    @GetMapping("/validate/page/items")
    public WrapperResp<ValidatePageItemResp> getValidatePageItems() {
        return WrapperUtil.success(buyerOrderService.getValidatePageItems(getSysUser()));
    }

    /**
     * “待审核订单（一级）” - 分页查询订单列表
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @BuyerAuth
    @GetMapping("/validate/grade/one/page")
    public WrapperResp<PageDataResp<OrderPageQueryResp>> pageToValidateGradeOneOrders(@Valid OrderPageDataReq pageVO) {
        return WrapperUtil.success(buyerOrderService.pageToValidateGradeOneOrders(getSysUser(), pageVO));
    }

    /**
     * “待审核订单（一级）” - 查询订单详情
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/validate/grade/one/detail")
    public WrapperResp<BuyerOrderDetailResp> getToValidateGradeOneOrderDetails(@Valid OrderIdReq idVO) {
        return WrapperUtil.success(buyerOrderService.getToValidateGradeOneOrderDetails(getSysUser(), idVO));
    }

    /**
     * 待拣货复核订单 - 分页查询订单列表
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @BuyerAuth
    @GetMapping("/validate/grade/one/picking/page")
    public WrapperResp<PageDataResp<OrderPageQueryResp>> pageToValidateGradeOnePickingOrders(@Valid OrderPageDataReq pageVO) {
        return WrapperUtil.success(buyerOrderService.pageToValidateGradeOnePickOrders(getSysUser(), pageVO));
    }

    /**
     * 待拣货复核订单 - 拣货复核
     * @param agreeVO 接口参数
     * @return 审核结果
     */
    @PostMapping("/validate/grade/one/picking")
    public WrapperResp<Void> validateOrderGradeOnePicking(@RequestBody @Valid OrderAgreeReq agreeVO) {
        return WrapperUtil.success(buyerOrderService.validateOrderGradeOnePick(getSysUser(), agreeVO));
    }

    /**
     * “待审核订单（一级）” - 审核
     * @param agreeVO 接口参数
     * @return 审核结果
     */
    @PostMapping("/validate/grade/one")
    public WrapperResp<Void> validateOrderGradeOne(@RequestBody @Valid OrderAgreeReq agreeVO) {
        return WrapperUtil.success(buyerOrderService.validateOrderGradeOne(getSysUser(), agreeVO));
    }

    /**
     * “待审核订单（一级）” - 批量审核
     * @param orderIds 接口参数
     * @return 审核结果
     */
    @PostMapping("/validate/grade/one/batch")
    public WrapperResp<Void> batchValidateOrdersGradeOne(@RequestBody @Valid List<OrderIdReq> orderIds) {
        return WrapperUtil.success(buyerOrderService.batchValidateOrdersGradeOne(getSysUser(), orderIds));
    }

    /**
     * “待审核订单（二级）” - 分页查询订单列表
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @BuyerAuth
    @GetMapping("/validate/grade/two/page")
    public WrapperResp<PageDataResp<OrderPageQueryResp>> pageToValidateGradeTwoOrders(@Valid OrderPageDataReq pageVO) {
        return WrapperUtil.success(buyerOrderService.pageToValidateGradeTwoOrders(getSysUser(), pageVO));
    }

    /**
     * “待审核订单（二级）” - 查询订单详情
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/validate/grade/two/detail")
    public WrapperResp<BuyerOrderDetailResp> getToValidateGradeTwoOrderDetails(@Valid OrderIdReq idVO) {
        return WrapperUtil.success(buyerOrderService.getToValidateGradeTwoOrderDetails(getSysUser(), idVO));
    }

    /**
     * “待审核订单（二级）” - 审核
     * @param agreeVO 接口参数
     * @return 审核结果
     */
    @PostMapping("/validate/grade/two")
    public WrapperResp<Void> validateOrderGradeTwo(@RequestBody @Valid OrderAgreeReq agreeVO) {
        return WrapperUtil.success(buyerOrderService.validateOrderGradeTwo(getSysUser(), agreeVO));
    }

    /**
     * “待审核订单（二级）” - 批量审核
     * @param orderIds 接口参数
     * @return 审核结果
     */
    @PostMapping("/validate/grade/two/batch")
    public WrapperResp<Void> batchValidateOrdersGradeTwo(@RequestBody @Valid List<OrderIdReq> orderIds) {
        return WrapperUtil.success(buyerOrderService.batchValidateOrdersGradeTwo(getSysUser(), orderIds));
    }

    /**
     * “待提交订单” - 分页查询订单列表
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @BuyerAuth
    @GetMapping("/validate/submit/page")
    public WrapperResp<PageDataResp<OrderPageQueryResp>> pageToSubmitOrders(@Valid OrderPageDataReq pageVO) {
        return WrapperUtil.success(buyerOrderService.pageToSubmitOrders(getSysUser(), pageVO));
    }

    /**
     * “待提交订单” - 查询订单详情
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/validate/submit/detail")
    public WrapperResp<BuyerOrderDetailResp> getToSubmitOrderDetails(@Valid OrderIdReq idVO) {
        return WrapperUtil.success(buyerOrderService.getToSubmitOrderDetails(getSysUser(), idVO));
    }

    /**
     * “待提交订单” - 提交
     * @param idVO 接口参数
     * @return 审核结果
     */
    @PostMapping("/validate/submit")
    public WrapperResp<Void> submitValidateOrder(@RequestBody @Valid OrderIdReq idVO) {
        buyerOrderService.submitValidateOrder(getSysUser(), idVO);
        return WrapperUtil.success();
    }

    /**
     * “待提交订单” - 批量提交
     * @param orderIds 接口参数
     * @return 审核结果
     */
    @PostMapping("/validate/submit/batch")
    public WrapperResp<Void> batchSubmitValidateOrders(@RequestBody @Valid List<OrderIdReq> orderIds) {
        return WrapperUtil.success(buyerOrderService.batchSubmitValidateOrders(getSysUser(), orderIds));
    }

    /**
     * “待确认电子合同订单” - 分页查询订单列表
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @BuyerAuth
    @GetMapping("/validate/confirm/electronic/contract/page")
    public WrapperResp<PageDataResp<OrderPageQueryResp>> pageToConfirmElectronicContract(@Valid OrderPageDataReq pageVO) {
        return WrapperUtil.success(buyerOrderService.pageToConfirmElectronicContract(getSysUser(), pageVO));
    }

    /**
     * “待确认电子合同订单” - 查询订单详情
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/validate/confirm/electronic/contract/detail")
    public WrapperResp<BuyerOrderDetailResp> getToConfirmElectronicContractDetails(@Valid OrderIdReq idVO) {
        return WrapperUtil.success(buyerOrderService.getToConfirmElectronicContractDetails(getSysUser(), idVO));
    }

    /**
     * “待确认电子合同订单” - 修改承诺交期日期
     * @param updateVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/validate/update/promised/delivery/date")
    public WrapperResp<Void> updateToConfirmElectronicContractPromisedDeliveryDate(@Valid @RequestBody OrderProductPromisedDeliveryDateReq updateVO) {
        return WrapperUtil.success(buyerOrderService.updateToConfirmElectronicContractPromisedDeliveryDate(getSysUser(), updateVO));
    }

    /**
     * “待确认电子合同订单” - 确认
     * @param agreeVO 接口参数
     * @return 审核结果
     */
    @PostMapping("/validate/confirm/electronic/contract")
    public WrapperResp<Void> confirmElectronicContractValidateOrder(@RequestBody @Valid OrderAgreeReq agreeVO) {
        return WrapperUtil.success(buyerOrderService.confirmElectronicContractValidateOrder(getSysUser(), agreeVO));
    }

    /**
     * “待确认电子合同订单” - 批量确认
     * @param orderIds 接口参数
     * @return 审核结果
     */
    @PostMapping("/validate/confirm/electronic/contract/batch")
    public WrapperResp<Void> batchConfirmElectronicContractValidateOrder(@RequestBody @Valid List<OrderIdReq> orderIds) {
        return WrapperUtil.success(buyerOrderService.batchConfirmElectronicContractValidateOrder(getSysUser(), orderIds));
    }

    /**
     * “待支付订单” - 查询订单列表
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @BuyerAuth
    @GetMapping("/validate/pay/page")
    public WrapperResp<PageDataResp<BuyerToPayQueryResp>> pageToPayOrders(@Valid OrderValidatePageDataReq pageVO) {
        return WrapperUtil.success(buyerOrderService.pageToPayOrders(getSysUser(), pageVO));
    }

    /**
     * “待支付订单” - 查询订单详情
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/validate/pay/detail")
    public WrapperResp<BuyerOrderDetailResp> getToPayOrderDetails(@Valid OrderIdReq idVO) {
        return WrapperUtil.success(buyerOrderService.getToPayOrderDetails(getSysUser(), idVO));
    }

    /**
     * “待支付订单” - 订单详情 - 查询支付方式与支付渠道列表
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/validate/pay/type")
    public WrapperResp<List<OrderPayTypeDetailResp>> getToPayOrderPayTypes(@Valid OrderIdReq idVO) {
        return WrapperUtil.success(buyerOrderService.getToPayOrderPayTypes(getSysUser(), idVO));
    }

    /**
     * “待支付订单” - 订单支付
     * @param payVO 接口参数
     * @return 支付链接（在线支付）
     */
    @PostMapping("/validate/pay")
    public WrapperResp<OrderPayResultDetailResp> orderPay(@RequestBody @Valid OrderBuyerMergePayReq payVO) {
        return WrapperUtil.success(orderModuleService.orderPay(getSysUser(), payVO));
    }

    /**
     * “待支付订单” - 查询支付结果
     * @param resultVO 接口参数
     * @return 支付结果
     */
    @GetMapping("/validate/pay/result")
    public WrapperResp<BuyerPayResultDetailResp> findPayResult(@Valid BuyerPayResultReq resultVO) {
        return WrapperUtil.success(buyerOrderService.findPayResult(getSysUser(), resultVO));
    }

    /**
     * “待发货订单” - 查询订单列表
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @BuyerAuth
    @GetMapping("/validate/delivery/page")
    public WrapperResp<PageDataResp<BuyerToDeliveryQueryResp>> pageToDeliveryOrders(@Valid OrderValidatePageDataReq pageVO) {
        return WrapperUtil.success(buyerOrderService.pageToDeliveryOrders(getSysUser(), pageVO));
    }

    /**
     * “待发货订单” - 订单详情 - 邀请好友拼团 - 生成小程序码分享链接
     * @param idVO    接口参数
     * @return 查询结果
     */
    @GetMapping("/delivery/mini/app/code")
    public WrapperResp<String> createDeliveryMiniAppCode(@Valid OrderIdReq idVO) {
        return WrapperUtil.success(buyerOrderService.createDeliveryMiniAppCode(getSysUser(), idVO));
    }

    /**
     * “待新增采购收货单” - 查询订单列表
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @BuyerAuth
    @GetMapping("/create/receive/page")
    public WrapperResp<PageDataResp<BuyerOrderReceiveQueryResp>> pageCreateReceiveOrders(@Valid BuyerOrderReceivePageDataReq pageVO) {
        return WrapperUtil.success(buyerOrderService.pageCreateReceiveOrders(getSysUser(), pageVO));
    }

    /**
     * “待新增采购收货单” - 创建收货单
     * @param orderVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/create/receive")
    public WrapperResp<Void> createReceiveOrder(@RequestBody @Valid BuyerCreateReceiveReq orderVO) {
        return WrapperUtil.success(buyerOrderService.createReceiveOrder(getSysUser(), orderVO));
    }

    /**
     * “待新增采购收货单” - 查看详情
     * @param idVO    接口参数
     * @return 查询结果
     */
    @GetMapping("/receive/detail")
    public WrapperResp<BuyerReceiveDetailResp> getReceiveDetail(@Valid OrderIdReq idVO) {
        return WrapperUtil.success(buyerOrderService.getReceiveDetail(getSysUser(), idVO));
    }

    /**
     * “待新增采购收货单” - 审核采购收货单
     * @param idVO    接口参数
     * @return 查询结果
     */
    @PostMapping("/validate/receive")
    public WrapperResp<Void> validateToReceiveOrder(@RequestBody @Valid OrderIdReq idVO) {
        return WrapperUtil.success(buyerOrderService.validateToReceiveOrder(getSysUser(), idVO));
    }

    /**
     * “待确认收货订单” -查询订单列表
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @BuyerAuth
    @GetMapping("/validate/receive/page")
    public WrapperResp<PageDataResp<BuyerReceiveQueryResp>> pageToReceiveOrders(@Valid OrderValidatePageDataReq pageVO) {
        return WrapperUtil.success(buyerOrderService.pageToReceiveOrders(getSysUser(), pageVO));
    }

    /**
     * 待自提订单 - 查询订单列表
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @BuyerAuth
    @GetMapping("/validate/pickup/page")
    public WrapperResp<PageDataResp<BuyerReceiveQueryResp>> pageToPickupOrders(@Valid OrderValidatePageDataReq pageVO) {
        return WrapperUtil.success(buyerOrderService.pageToPickupOrders(getSysUser(), pageVO));
    }

    /**
     * “待确认收货订单” -查询订单详情
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/validate/receive/detail")
    public WrapperResp<BuyerOrderDetailResp> getToReceiveOrderDetails(@Valid OrderIdReq idVO) {
        return WrapperUtil.success(buyerOrderService.getToReceiveOrderDetails(getSysUser(), idVO));
    }

    /**
     * “待确认收货订单” - 确认收货
     * @param receiveVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/validate/receive/confirm")
    public WrapperResp<Void> receiveOrder(@RequestBody @Valid BuyerReceiveReq receiveVO) {
        return WrapperUtil.success(buyerOrderService.receiveOrder(getSysUser(), receiveVO));
    }

    /**
     * 订单分页查询 - 通用 - 所有订单类型下拉框列表
     * @return 查询结果
     */
    @GetMapping("/order/type/all")
    public WrapperResp<List<DropdownItemResp>> getAllOrderType() {
        return WrapperUtil.success(buyerOrderService.getAllOrderType());
    }

    /**
     * 提交订单 - 查询满额包邮的商品总运费
     * @param freightVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/product/free/freight")
    public WrapperResp<BigDecimal> findProductFreeFreight(@RequestBody @Valid OrderProductFreeFreightReq freightVO) {
        return WrapperUtil.success(buyerOrderService.findProductFreeFreight(getSysUser(), freightVO));
    }

    /**
     * 所有付款方式下拉框列表
     * @return 查询结果
     */
    @GetMapping("/payment/type/all")
    public WrapperResp<List<DropdownItemResp>> getAllPaymentType() {
        return WrapperUtil.success(buyerOrderService.getAllPaymentType());
    }


    /**
     * 支付回调测试
     * @return 查询结果
     */
    @PostMapping("/pay/notify")
    public WrapperResp<List<DropdownItemResp>> getAllPaymentType(@RequestBody OrderPayCallbackFeignReq callbackFeignVO) {
        buyerOrderService.orderPayCallback(callbackFeignVO);
        return WrapperUtil.success();
    }
}
