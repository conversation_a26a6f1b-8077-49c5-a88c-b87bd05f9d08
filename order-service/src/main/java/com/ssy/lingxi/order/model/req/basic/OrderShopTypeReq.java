package com.ssy.lingxi.order.model.req.basic;

import com.ssy.lingxi.order.handler.annotation.OrderShopTypeAnnotation;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 商城类型作为接口参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-10-14
 */
@Getter
@Setter
@NoArgsConstructor
public class OrderShopTypeReq implements Serializable {
    private static final long serialVersionUID = -2826436983634531767L;

    /**
     * 商城类型，1-企业商城，2-积分商城，3-渠道商城，4-渠道自有商城，5-渠道积分商城，6-采购门户，7-物流服务门户，8-加工服务门户，9-行情资讯
     */
    @NotNull(message = "商城类型不能为空")
    @OrderShopTypeAnnotation
    private Integer shopType;
}
