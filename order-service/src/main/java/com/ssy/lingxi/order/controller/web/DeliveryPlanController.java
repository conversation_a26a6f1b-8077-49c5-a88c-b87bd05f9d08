package com.ssy.lingxi.order.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.order.enums.DeliveryHistoryTypeEnum;
import com.ssy.lingxi.order.enums.DeliveryOrderTypeStatusEnum;
import com.ssy.lingxi.order.model.req.basic.*;
import com.ssy.lingxi.order.model.resp.basic.*;
import com.ssy.lingxi.order.service.web.IDeliveryPlanService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 订单能力 - 送货计划
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-17
 */
@RestController
@RequestMapping(ServiceModuleConstant.ORDER_PATH_PREFIX + "/deliveryPlan")
public class DeliveryPlanController extends BaseController {

    @Resource
    private IDeliveryPlanService planService;

    /**
     * “送货计划” - 送货计划协同 - 分页查询
     * 
     * @param request 接口参数
     * @return 查询结果
     */
    @GetMapping("/vendor/page")
    public WrapperResp<PageDataResp<DeliveryPlanQueryResp>> pageByVendorPlan(@Valid DeliveryPlanPageDataReq request) {
        return WrapperUtil.success(planService.pageVendorDeliveryPlan(getSysUser(), request));
    }

    /**
     * “送货计划” - 送货计划 - 分页查询
     * 
     * @param request 接口参数
     * @return 查询结果
     */
    @GetMapping("/buyer/page")
    public WrapperResp<PageDataResp<DeliveryPlanQueryResp>> pageByBuyer(@Valid DeliveryPlanPageDataReq request) {
        return WrapperUtil.success(planService.pageBuyerDeliveryPlan(getSysUser(), request,request.getOrderType()));
    }

    /**
     * “送货计划” - 查询计划周期内的计划送货物料
     * 
     * @param request 接口参数
     * @return 查询结果
     */
    @GetMapping("/order/product/page")
    public WrapperResp<PageDataResp<DeliveryPlanProductQueryResp>> pageByOrderProductSRM(@Valid DeliveryPlanProductPageDataReq request) {
        return WrapperUtil.success(planService.pagePlanProducts(getSysUser(), request));
    }

    /**
     * “送货计划” - 新增送货计划（SRM）
     * 
     * @param request 接口参数
     * @return 新增结果
     */
    @PostMapping("/srm/create")
    public WrapperResp<Void> createBySRM(@RequestBody @Valid DeliveryPlanCreateReq request) {
        return WrapperUtil.success(planService.createDeliveryPlan(getSysUser(), request, DeliveryOrderTypeStatusEnum.SRM.getCode()));
    }


    /**
     * “送货计划” - 新增送货计划（B2B）
     * 
     * @param request 接口参数
     * @return 新增结果
     */
    @PostMapping("/b2b/create")
    public WrapperResp<Void> createByB2B(@RequestBody @Valid DeliveryPlanCreateReq request) {
        return WrapperUtil.success(planService.createDeliveryPlan(getSysUser(), request, DeliveryOrderTypeStatusEnum.B2B.getCode()));
    }

    /**
     * “送货计划” - 详情
     * 
     * @param request 接口参数
     * @return 查询结果
     */
    @GetMapping("/detail")
    public WrapperResp<DeliveryPlanDetailResp> detail(@Valid CommonIdReq request) {
        return WrapperUtil.success(planService.getDeliveryPlanDetail(getSysUser(), request));
    }

    /**
     * “送货计划” - 详情 - 流转记录
     * 
     * @param request 接口参数
     * @return 查询结果
     */
    @GetMapping("/deliveryHistory")
    public WrapperResp<List<DeliveryOuterHistoryResp>> deliveryHistory(@Valid CommonIdReq request) {
        return WrapperUtil.success(planService.deliveryHistory(getSysUser(), request, DeliveryHistoryTypeEnum.DELIVERY_PLAN.getCode()));
    }

    /**
     * “送货计划” - 详情 - 物料分页列表
     * 
     * @param request 接口参数
     * @return 查询结果
     */
    @GetMapping("/detail/product/page")
    public WrapperResp<PageDataResp<DeliveryPlanProductQueryResp>> pageProducts(@Valid IdPageDataReq request) {
        return WrapperUtil.success(planService.getDeliveryPlanProductDetail(getSysUser(), request));
    }

    /**
     * “送货计划” - 修改送货计划
     * 
     * @param request 接口参数
     * @return 返回结果
     */
    @PostMapping("/update")
    public WrapperResp<Void> update(@RequestBody @Valid DeliveryPlanUpdateReq request) {
        return WrapperUtil.success(planService.updateDeliveryPlan(getSysUser(), request));
    }

    /**
     * “送货计划” - 删除送货计划
     * 
     * @param request 接口参数
     * @return 返回结果
     */
    @PostMapping("/delete")
    public WrapperResp<Void> delete(@RequestBody @Valid CommonIdReq request) {
        return WrapperUtil.success(planService.deleteDeliveryPlan(getSysUser(), request));
    }

    /**
     * “送货计划” - 确认送货计划
     * 
     * @param request 接口参数
     * @return 返回结果
     */
    @PostMapping("/confirm")
    public WrapperResp<Void> confirm(@RequestBody @Valid DeliveryVerifyReq request) {
        return WrapperUtil.success(planService.confirmDeliveryPlan(getSysUser(), request));
    }

    /**
     * “送货计划” - 提交送货计划
     * 
     * @param request 接口参数
     * @return 返回结果
     */
    @PostMapping("/submit")
    public WrapperResp<Void> submit(@RequestBody @Valid CommonIdReq request) {
        return WrapperUtil.success(planService.submitDeliveryPlan(getSysUser(), request));
    }


    /**
     * 送货计划-生成送货通知单所需数据
     * 
     * @param request 接口参数
     * @return 查询结果
     */
    @GetMapping("/noticeOrder")
    public WrapperResp<NoticeOrderParamByPlanResp> NoticeOrderByPlanCreate(@Valid NoticeOrderByPlanCreateReq request) {
        return WrapperUtil.success(planService.NoticeOrderByPlanCreate(getSysUser(), request));
    }

    /**
     * 送货计划-生成送货单所需数据
     * 
     * @param request 接口参数
     * @return 查询结果
     */
    @GetMapping("/deliveryOrder")
    public WrapperResp<DeliveryOrderCreateByPlanResp> DeliveryOrderByPlanCreate(@Valid DeliveryOrderByPlanCreateReq request) {
        return WrapperUtil.success(planService.DeliveryOrderByPlanCreate(getSysUser(), request));
    }
}
