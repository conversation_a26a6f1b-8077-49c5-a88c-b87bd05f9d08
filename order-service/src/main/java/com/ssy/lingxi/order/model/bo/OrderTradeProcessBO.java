package com.ssy.lingxi.order.model.bo;

import com.ssy.lingxi.common.enums.workflow.WorkflowProcessKindEnum;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 交易流程查询结果
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-30
 */
@Getter
@Setter
@NoArgsConstructor
public class OrderTradeProcessBO implements Serializable {
    private static final long serialVersionUID = 4751714274018011921L;

    public OrderTradeProcessBO(String processKey, Integer payTimes, BigDecimal expireHours, Boolean skipFirstStep, Integer processKind, Boolean allProducts, List<PayNodeBO> payNodes, Boolean hasContract, Long contractTemplateId) {
        this.processKey = processKey;
        this.payTimes = payTimes;
        this.expireHours = expireHours;
        this.skipFirstStep = skipFirstStep;
        this.processKind = WorkflowProcessKindEnum.parse(processKind);
        this.allProducts = allProducts;
        this.payNodes = CollectionUtils.isEmpty(payNodes) ? new ArrayList<>() : payNodes.stream().filter(payNode -> payNode.getBatchNo().compareTo(0) > 0).sorted(Comparator.comparingInt(PayNodeBO::getBatchNo)).collect(Collectors.toList());
        this.hasContract = hasContract;
        this.contractTemplateId = contractTemplateId;
    }

    public OrderTradeProcessBO(List<TradeProcessDetailBO> details) {
        details.stream().findFirst().ifPresent(detail -> {
            this.processKey = detail.getProcessKey();
            this.payTimes = detail.getPayTimes();
            this.skipFirstStep = detail.getSkipFirstStep();
            this.processKind = WorkflowProcessKindEnum.parse(detail.getProcessKind());
            this.allProducts = detail.getAllProducts();
            this.hasContract = detail.getHasContract();
            this.contractTemplateId = detail.getContractTemplateId();
            this.payNodes = CollectionUtils.isEmpty(detail.getPayNodes()) ? new ArrayList<>() : detail.getPayNodes().stream().filter(payNode -> payNode.getBatchNo().compareTo(0) > 0).sorted(Comparator.comparingInt(PayNodeBO::getBatchNo)).collect(Collectors.toList());
            this.expireHours = detail.getExpireHours();
        });
    }

    /**
     * 流程的ProcessKey
     */
    private String processKey;

    /**
     * 流程中支付的次数
     */
    private Integer payTimes;

    /**
     * 订单自动取消时间（小时）
     */
    private BigDecimal expireHours;

    /**
     * 流程启动时，是否执行第一个任务
     */
    private Boolean skipFirstStep;

    /**
     * 流程类型
     */
    private WorkflowProcessKindEnum processKind;

    /**
     * 是否适用所有商品
     */
    private Boolean allProducts;

    /**
     * 支付环节配置
     */
    private List<PayNodeBO> payNodes;

    /**
     * 是否有电子合同
     */
    private Boolean hasContract;

    /**
     * 电子合同模板Id
     */
    private Long contractTemplateId;
}
