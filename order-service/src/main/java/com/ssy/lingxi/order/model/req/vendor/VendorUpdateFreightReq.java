package com.ssy.lingxi.order.model.req.vendor;

import com.ssy.lingxi.order.model.req.basic.OrderIdReq;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 销售订单 - 修改运费与商品到手价接口参数
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-09-17
 */
@Getter
@Setter
@NoArgsConstructor
public class VendorUpdateFreightReq extends OrderIdReq implements Serializable {
    private static final long serialVersionUID = -1229785594255239841L;

    /**
     * 运费
     */
    @Min(value = 0, message = "运费要大于等于0")
    @Max(value = 1000000000, message = "运费不能超过10亿")
    private BigDecimal freight;

    /**
     * 修改原因
     */
    @NotBlank(message = "修改原因不能为空")
    @Size(max = 60, message = "修改原因最长60个字符")
    private String reason;

    /**
     * 订单商品与单价（到手价）
     */
    @Valid
    private List<VendorUpdateProductPriceReq> prices;
}
