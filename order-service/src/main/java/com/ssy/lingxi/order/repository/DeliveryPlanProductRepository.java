package com.ssy.lingxi.order.repository;

import com.ssy.lingxi.order.entity.DeliveryPlanDO;
import com.ssy.lingxi.order.entity.DeliveryPlanProductDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 *  送货计划-送货物料Jpa仓库
 * <AUTHOR>
 * @since 2022/2/21
 * @version 2.0.0
 */
@Repository
public interface DeliveryPlanProductRepository extends JpaRepository<DeliveryPlanProductDO, Long>, JpaSpecificationExecutor<DeliveryPlanProductDO> {

    List<DeliveryPlanProductDO> findByIdIn(List<Long> ids);

    Page<DeliveryPlanProductDO> findByDeliveryPlan(DeliveryPlanDO param, Pageable page);

    DeliveryPlanProductDO findFirstByDeliveryPlanAndSkuIdAndProductNameAndCategoryAndBrandAndUnit(DeliveryPlanDO param,String skuId,String ProductName,String category,String brand,String unit);

    DeliveryPlanProductDO findFirstByDeliveryPlanAndSkuIdAndProductNameAndTypeAndCategoryAndBrandAndUnit(DeliveryPlanDO param,String skuId,String ProductName,String type,String category,String brand,String unit);

    void deleteByDeliveryPlan(DeliveryPlanDO entity);
}
