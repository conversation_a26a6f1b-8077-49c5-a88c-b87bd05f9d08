package com.ssy.lingxi.order.model.resp.platform;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 平台后台 - 查询会员支付策略详情返回
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-08-11
 */
@Getter
@Setter
@NoArgsConstructor
public class PlatformPaymentDetailResp implements Serializable {
    private static final long serialVersionUID = -4536726417380076588L;

    /**
     * 支付策略Id
     */
    private Long paymentId;

    /**
     * 支付策略名称
     */
    private String name;

    /**
     * 支付方式与支付渠道列表
     */
    private List<PlatformPaymentPayTypeDetailResp> payTypes;

    /**
     * 是否适用所有会员，ture-是，false-否
     */
    private Boolean allMembers;
}
