package com.ssy.lingxi.order.model.req.basic;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 分页查询收货单-物料列表接口参数
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-19
 */
@Getter
@Setter
public class ReceiveOrderProductPageDataReq extends PageDataReq implements Serializable {

    private static final long serialVersionUID = -7981789772012272369L;
    /**
     * 送货单id
     */
    @NotNull(message = "收货单id不能为空")
    private Long id;


}
