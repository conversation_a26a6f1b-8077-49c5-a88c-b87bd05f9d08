package com.ssy.lingxi.order.model.req.vendor;

import com.ssy.lingxi.order.model.req.basic.OrderIdReq;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 修改支付比例设置
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-09-17
 */
@Getter
@Setter
@NoArgsConstructor
public class VendorUpdatePayRateReq extends OrderIdReq implements Serializable {
    private static final long serialVersionUID = -1627473760104514235L;

    /**
     * 支付次数与支付比例
     */
    @NotEmpty(message = "支付次数与支付比例不能为空")
    @Valid
    private List<VendorPayRateReq> payRates;
}
