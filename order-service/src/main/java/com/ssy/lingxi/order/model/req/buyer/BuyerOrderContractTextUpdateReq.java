package com.ssy.lingxi.order.model.req.buyer;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 更新订单电子合同文本VO
 * <AUTHOR>
 * @since 2022/6/29 10:36
 */
@Getter
@Setter
public class BuyerOrderContractTextUpdateReq implements Serializable {
    private static final long serialVersionUID = -541384518511060667L;

    /**
     * 电子合同文本id（为0代表新增）
     */
    @NotNull(message = "电子合同文本id不能为空")
    private Long id;

    /**
     * 合同模板id
     */
    @NotNull(message = "合同模板id不能为空")
    private Long templateId;

    /**
     * 是否使用电子合同：0-否，1-是
     */
    @NotNull(message = "是否使用电子合同不能为空")
    private Integer isUseElectronicContract;

    /**
     * 合同名称
     */
//    @NotBlank(message = "合同名称不能为空")
    private String contractName;

    /**
     * 合同地址
     */
//    @NotBlank(message = "合同地址不能为空")
    private String contractUrl;
}
