package com.ssy.lingxi.order.repository;

import com.ssy.lingxi.order.entity.OrderDO;
import com.ssy.lingxi.order.entity.OrderDeductionDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * 订单商品关联的积分抵扣JPA仓库
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-03-02
 **/
@Repository
public interface OrderDeductionRepository extends JpaRepository<OrderDeductionDO, Long>, JpaSpecificationExecutor<OrderDeductionDO>, QuerydslPredicateExecutor<OrderDeductionDO> {

    @Transactional
    @Modifying
    void deleteByOrder(OrderDO order);

}
