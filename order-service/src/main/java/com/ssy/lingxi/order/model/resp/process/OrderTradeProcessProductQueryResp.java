package com.ssy.lingxi.order.model.resp.process;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * 查询交易流程关联的商品信息
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-24
 */
@Getter
@Setter
@NoArgsConstructor
public class OrderTradeProcessProductQueryResp implements Serializable {
    private static final long serialVersionUID = 6011555899938551268L;

    /**
     * 商品Id
     */
    private Long productId;

    /**
     * 商品SkuId
     */
    private Long skuId;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 商品品类
     */
    private String category;

    /**
     * 商品品牌
     */
    private String brand;

    /**
     * 商品定价类型枚举，1-现货价格，2-价格需要询价，3-积分兑换商品
     */
    private Integer priceType;

    /**
     * 商品定价类型名称
     */
    private String priceTypeName;

    /**
     * 商品类型
     */
    private Integer productType;
}
