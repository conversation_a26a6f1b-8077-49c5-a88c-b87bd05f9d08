package com.ssy.lingxi.order.model.req.process;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 新增/修改交易规则，支付环节设置
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-24
 */
@Getter
@Setter
@NoArgsConstructor
public class OrderTradeProcessPaymentReq implements Serializable {
    private static final long serialVersionUID = 6376276349347148148L;

    /**
     * 支付次数，前端生成，从1开始，递增+1
     */
    @NotNull(message = "支付次数要大于0")
    @Positive(message = "支付次数要大于0")
    private Integer batchNo;

    /**
     * 支付环节
     */
    @NotBlank(message = "支付环节不能为空")
    @Size(max = 30, message = "支付环节最长30个字符")
    private String payNode;

    /**
     * 支付比例
     */
    @NotNull(message = "支付比例要大于等于0，小于等于100")
    @PositiveOrZero(message = "支付比例要大于等于0，小于等于100")
    @Max(value = 100, message = "支付比例要大于等于0，小于等于100")
    private BigDecimal payRate;
}
