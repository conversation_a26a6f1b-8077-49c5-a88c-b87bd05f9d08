package com.ssy.lingxi.order.model.resp.common;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * 商品交易记录查询返回
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-29
 */
@Getter
@Setter
@NoArgsConstructor
public class OrderProductHistoryQueryResp implements Serializable {
    private static final long serialVersionUID = -6310329344719475666L;

    /**
     * 交易时间，格式为yyyy-MM-dd HH:mm:ss
     */
    private String createTime;

    /**
     * 买家（采购商）名称
     */
    private String buyerMemberName;

    /**
     * 成交数量
     */
    private String quantity;

    /**
     * 商品计价单位
     */
    private String unit;
}
