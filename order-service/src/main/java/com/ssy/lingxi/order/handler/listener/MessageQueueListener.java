package com.ssy.lingxi.order.handler.listener;

import cn.hutool.json.JSONUtil;
import com.rabbitmq.client.Channel;
import com.ssy.lingxi.common.constant.mq.MarketingMqConstant;
import com.ssy.lingxi.common.constant.mq.OrderMqConstant;
import com.ssy.lingxi.common.constant.mq.SettlementMqConstant;
import com.ssy.lingxi.common.util.SerializeUtil;
import com.ssy.lingxi.component.rabbitMQ.service.IMqUtils;
import com.ssy.lingxi.order.api.enums.OrderCommonMessageTypeEnum;
import com.ssy.lingxi.order.api.model.dto.OrderCommonMessageDTO;
import com.ssy.lingxi.order.enums.OrderQueueMessageTypeEnum;
import com.ssy.lingxi.order.model.dto.OrderQueueDTO;
import com.ssy.lingxi.order.service.base.IBaseOrderInvoiceService;
import com.ssy.lingxi.order.service.base.IBaseOrderScheduleService;
import com.ssy.lingxi.product.api.feign.IMaterielPriceFeign;
import com.ssy.lingxi.product.api.model.req.price.OrderPriceReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 消息队列监听类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-10-21
 */
@Slf4j
@Component
public class MessageQueueListener {
    @Resource
    private IBaseOrderScheduleService baseOrderScheduleService;

    @Resource
    private IBaseOrderInvoiceService baseOrderInvoiceService;

    @Resource
    private IMaterielPriceFeign materielPriceFeign;

    @Resource
    private IMqUtils mqUtils;

    /**
     * 内部延迟队列消费者
     * @param message 消息
     * @param channel  消息通道
     */
    @RabbitListener(queues = OrderMqConstant.ORDER_SERVICE_DELAY_QUERY, ackMode = "MANUAL")
    public void handler(Message message, Channel channel) {
        try {
            String jsonMessage = new String(message.getBody());
            log.info("内部延迟队列接收到消息 => " + jsonMessage);

            OrderQueueDTO queueDTO = SerializeUtil.deserialize(jsonMessage, OrderQueueDTO.class);
            if(Objects.isNull(queueDTO) || Objects.isNull(queueDTO.getType()) || !StringUtils.hasLength(queueDTO.getMessage())) {
                log.info("内部延迟队列消息反序列化后错误 => " + jsonMessage);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), true);
                return;
            }

            //校验是否可以消费该条消息
            boolean flag = mqUtils.canConsume(MarketingMqConstant.MK_DELAY_EXCHANGE, MarketingMqConstant.MK_DELAY_ROUTING_KEY, JSONUtil.toJsonStr(queueDTO));
            if(!flag) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), true);
                return;
            }

            OrderQueueMessageTypeEnum messageType = OrderQueueMessageTypeEnum.parse(queueDTO.getType());
            switch (messageType) {
                case AUTO_CANCEL:
                    baseOrderScheduleService.orderCancel(queueDTO.getMessage());
                    break;
                case AUTO_RECEIVE:
                    baseOrderScheduleService.scheduleOrderReceive(queueDTO.getMessage());
                    break;
                case PAYMENT_QUERY:
                    baseOrderScheduleService.orderQueueCallback(queueDTO.getTimes(), queueDTO.getMessage());
                    break;
                default:
                    break;
            }

            //确认消息
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), true);
        } catch (Exception e){
            log.error("内部延迟队列接收消息错误：" + e.getMessage());
        }
    }

    /**
     * 订单支付回调通知队列
     * @param message 消息
     * @param channel  消息通道
     */
    @RabbitListener(queues = OrderMqConstant.ORDER_PAY_CALLBACK_QUERY, ackMode = "MANUAL")
    public void orderPayCallbackHandler(Message message, Channel channel) {
        try {
            baseOrderScheduleService.orderPayCallback(new String(message.getBody()));

            //确认消息
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), true);
        } catch (Exception e){
            log.error("订单支付回调，从消息队列接收消息错误：" + e.getMessage());
        }
    }

    /**
     * 订单拼团结果异步通知
     * @param message 消息
     * @param channel  消息通道
     */
    @RabbitListener(queues = OrderMqConstant.ORDER_GROUP_QUERY, ackMode = "MANUAL")
    public void orderGroupHandler(Message message, Channel channel) {
        try {
            baseOrderScheduleService.updateOrderGroupStatus(new String(message.getBody()));

            //确认消息
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), true);
        } catch (Exception e){
            log.error("从消息队列接收拼团订单消息错误：" + e.getMessage());
        }
    }

    /**
     * 营销服务更新支付记录结算状态异步通知
     * @param message 消息
     * @param channel  消息通道
     */
    @RabbitListener(queues = SettlementMqConstant.SA_PLATFORM_SETTLEMENT_ALL_IN_PAY_QUEUE, ackMode = "MANUAL")
    public void paymentSettlementStatusHandler(Message message, Channel channel) {
        try {
            baseOrderScheduleService.updateOrderPaymentStatus(new String(message.getBody()));
            //确认消息
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), true);
        } catch (Exception e){
            log.error("从消息队列接收营销服务更新支付记录结算状态消息错误：" + e.getMessage());
        }
    }

    /**
     * 采购询价合同、采购竞价合同、采购招标合同、请购单采购 确认收货时, 同步更新物料价格库
     * @param message 消息
     * @param channel  消息通道
     */
    @RabbitListener(queues = OrderMqConstant.ORDER_MATERIEL_PRICE_CHANGE_QUEUE, ackMode = "MANUAL")
    public void materielPriceChangeReceive(Message message, Channel channel) {
        OrderPriceReq request = JSONUtil.toBean(new String(message.getBody()), OrderPriceReq.class);
        log.info("订单确认收货时,更新物料价格信息- 开始处理:接受内容:{}", JSONUtil.toJsonStr(request));
        try {
            materielPriceFeign.changeOrderMaterielPrice(request);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), true);
            log.info("订单确认收货时,更新物料价格信息- 处理完成");
        } catch (Exception e) {
            log.error("============订单确认收货时,更新物料价格信息 MQ消费失败==============");
            log.error(e.getMessage());
        }
    }

    /**
     * 订单公共队列监听
     * @param channel 消息通道
     * @param message 消息
     */
    @RabbitListener(queues = OrderMqConstant.ORDER_COMMON_QUEUE, ackMode = "MANUAL")
    public void orderCommonQueueHandler(Channel channel, Message message){
        try{
            String content = new String(message.getBody());
            log.info("订单公共队列监听, 消息内容: {}", content);

            OrderCommonMessageDTO commonMessage = SerializeUtil.deserialize(content, OrderCommonMessageDTO.class);

            if (Objects.isNull(commonMessage)){
                return;
            }

            OrderCommonMessageTypeEnum messageEnum = OrderCommonMessageTypeEnum.findByCode(commonMessage.getCode());
            if (Objects.isNull(messageEnum)) {
                return;
            }

            if (messageEnum == OrderCommonMessageTypeEnum.INVOICE_UPDATE) {
                baseOrderInvoiceService.updateInvoiceNo(commonMessage.getContent());
            }

            channel.basicAck(message.getMessageProperties().getDeliveryTag(), true);
        }catch (Exception e){
            log.error("订单公共队列监听, 异常信息: {}", e.getMessage(), e);
        }
    }
}
