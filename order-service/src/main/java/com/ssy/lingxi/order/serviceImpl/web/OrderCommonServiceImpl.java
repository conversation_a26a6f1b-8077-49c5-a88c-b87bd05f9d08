package com.ssy.lingxi.order.serviceImpl.web;

import com.querydsl.core.group.GroupBy;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.ssy.lingxi.common.enums.DepositTypeEnum;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.model.resp.select.DropdownItemResp;
import com.ssy.lingxi.common.util.BigDecimalUtil;
import com.ssy.lingxi.common.util.MoneyUtil;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberLevelTypeEnum;
import com.ssy.lingxi.component.base.enums.member.RoleTypeEnum;
import com.ssy.lingxi.component.base.enums.order.OrderPayChannelEnum;
import com.ssy.lingxi.component.base.enums.order.OrderTradeProcessTypeEnum;
import com.ssy.lingxi.component.base.enums.order.OrderTypeEnum;
import com.ssy.lingxi.component.base.enums.product.CommoditySaleModeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.component.rest.model.resp.eos.GoldPriceResp;
import com.ssy.lingxi.component.rest.service.EosApiService;
import com.ssy.lingxi.contract.api.enums.CurrencyTypeEnum;
import com.ssy.lingxi.contract.api.model.req.ContractOrderProductReq;
import com.ssy.lingxi.contract.api.model.req.ContractOrderReq;
import com.ssy.lingxi.contract.api.model.req.ContractSignReq;
import com.ssy.lingxi.order.api.model.resp.OrderDepositResp;
import com.ssy.lingxi.order.constant.OrderConstant;
import com.ssy.lingxi.order.entity.*;
import com.ssy.lingxi.order.enums.OrderContractTypeEnum;
import com.ssy.lingxi.order.enums.OrderOuterStatusEnum;
import com.ssy.lingxi.order.model.bo.OrderTradeProcessBO;
import com.ssy.lingxi.order.model.bo.PlatformPayTypeBO;
import com.ssy.lingxi.order.model.bo.VendorBO;
import com.ssy.lingxi.order.model.dto.VendorLogoDTO;
import com.ssy.lingxi.order.model.req.basic.OrderIdReq;
import com.ssy.lingxi.order.model.req.basic.OrderNoReq;
import com.ssy.lingxi.order.model.req.basic.ShopIdPageDataReq;
import com.ssy.lingxi.order.model.req.common.*;
import com.ssy.lingxi.order.model.req.mobile.GetPayOrderInfoReq;
import com.ssy.lingxi.order.model.resp.basic.OrderPayChannelDetailResp;
import com.ssy.lingxi.order.model.resp.basic.OrderProductPositionResp;
import com.ssy.lingxi.order.model.resp.common.*;
import com.ssy.lingxi.order.model.resp.mobile.PayOrderDetailResp;
import com.ssy.lingxi.order.model.resp.vendor.VendorOrderDetailResp;
import com.ssy.lingxi.order.repository.OrderPaymentRepository;
import com.ssy.lingxi.order.repository.OrderRepository;
import com.ssy.lingxi.order.service.base.IBaseOrderHistoryService;
import com.ssy.lingxi.order.service.base.IBaseOrderProcessService;
import com.ssy.lingxi.order.service.base.IBaseOrderService;
import com.ssy.lingxi.order.service.feign.IContractFeignService;
import com.ssy.lingxi.order.service.feign.IPlatformTemplateFeignService;
import com.ssy.lingxi.order.service.web.IOrderCommonService;
import com.ssy.lingxi.order.service.web.IOrderParamConfigService;
import com.ssy.lingxi.pay.api.feign.IAssetAccountFeign;
import com.ssy.lingxi.pay.api.model.req.assetAccount.GetEosAccountBalanceAndFrozenAccountBalanceReq;
import com.ssy.lingxi.pay.api.model.resp.assetAccount.MemberAssetAccountResp;
import com.ssy.lingxi.product.api.feign.ICommodityFeign;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 订单相关的其他接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-29
 */
@Service
public class OrderCommonServiceImpl implements IOrderCommonService {
    @Resource
    private IPlatformTemplateFeignService platformTemplateFeignService;

    @Resource
    private IBaseOrderHistoryService baseOrderHistoryService;

    @Resource
    private IBaseOrderProcessService baseOrderProcessService;

    @Resource
    private JPAQueryFactory jpaQueryFactory;

    @Resource
    private IBaseOrderService baseOrderService;

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private ICommodityFeign commodityFeign;

    @Resource
    private IContractFeignService contractFeignService;

    @Resource
    private OrderPaymentRepository orderPaymentDORepository;
    @Resource
    private EosApiService eosApiService;
    @Resource
    private IOrderParamConfigService orderParamConfigService;
    @Resource
    private IAssetAccountFeign assetAccountFeign;

    /**
     * 通用 - 查询所有订单类型
     *
     * @return 查询结果
     */
    @Override
    public List<DropdownItemResp> listOrderTypes() {
        return Arrays.stream(OrderTypeEnum.values()).map(v -> new DropdownItemResp(v.getCode(), v.getName())).sorted(Comparator.comparing(DropdownItemResp::getId)).collect(Collectors.toList());
    }

    /**
     * 商城首页 - 分页查询商品交易记录
     *
     * @param loginUser 登录用户
     * @param historyVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<OrderProductHistoryQueryResp> pageOrderProductHistories(UserLoginCacheDTO loginUser, OrderProductHistoryDataReq historyVO) {
        return baseOrderHistoryService.pageOrderProductHistories(historyVO);
    }

    /**
     * 商城首页 - 分页查询商品成交动态
     *
     * @param loginUser 登录用户
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<OrderProductShopHistoryResp> pageOrderProductShopHistories(UserLoginCacheDTO loginUser, ShopIdPageDataReq pageVO) {
        QOrderProductDO qOrderProduct = QOrderProductDO.orderProductDO;
        QOrderDO qOrder = QOrderDO.orderDO;
        JPAQuery<OrderProductShopHistoryResp> query = jpaQueryFactory
                .select(Projections.constructor(OrderProductShopHistoryResp.class, qOrder.createTime, qOrderProduct.name,qOrderProduct.category, qOrderProduct.brand, qOrderProduct.spec, qOrderProduct.unit, qOrderProduct.quantity))
                .from(qOrderProduct)
                .leftJoin(qOrder).on(qOrderProduct.order.id.eq(qOrder.id))
                .where(qOrder.shopId.eq(pageVO.getShopId()))
                .orderBy(qOrderProduct.id.desc());
        long totalCount = query.fetchCount();
        return new PageDataResp<>(totalCount, query.limit(pageVO.getPageSize()).offset(pageVO.getCurrentOffset()).fetch());
    }

    /**
     * 售后能力 - 查询退货换货商品列表
     *
     * @param loginUser 登录用户
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<OrderAfterSalePageQueryResp> pageAfterSaleOrders(UserLoginCacheDTO loginUser, OrderAfterSalePageDataReq pageVO) {
        //Step 1: 参数判断
        OrderTypeEnum orderType = OrderTypeEnum.parse(pageVO.getOrderType());
        if(orderType == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_TYPE_DOES_NOT_EXIST);
        }

        //Step 2: 规则
        // 2-1. 查询采购订单中选择的供应会员的订单数据，根据选择的售后订单类型进行筛选，只显示已收到第一批货之后的订单且当前订单外部状态不是待确认支付结果
        // 2-2. 如果订单类型为现货采购、询价采购、集采、积分兑换、渠道直采、渠道现货、渠道积分兑换，
        //      查询交易流程规则配置中流程类型为售后换货流程的工作流
        // 2-3. 如果订单类型为采购询价合同、采购竞价合同、采购招标合同只允许选择合同编号相同的订单，
        //      查询采购流程规则配置的流程类型为售后换货流程的适用当前合同的工作流

        //Step 3: 在IMobileOrderCommonService中有个类似的接口，如这里修改了则要做相应的修改
        switch (orderType) {
            case SPOT_PURCHASING:
            case INQUIRY_TO_PURCHASE:
            case COLLECTIVE_PURCHASE:
            case CREDITS_EXCHANGE:
//            case CHANNEL_STRAIGHT_MINING:
//            case CHANNEL_SPOT:
            case FRAME_CONTRACT_ORDER:
            case MANUAL_MATERIAL_ORDER_PLACEMENT:
            case MATERIAL_SAMPLE_ORDER:
//            case CHANNEL_POINT_EXCHANGE:
                return pageAfterSaleOrdersByTradeProcess(loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO);
            case QUERY_PRICE_CONTRACT:
            case PRICE_COMPETITION_CONTRACT_PURCHASE:
            case PURCHASE_CONTRACT_BIDDING:
            case REQUISITION_TO_PURCHASE:
            case REQUISITION_CONTRACT:
                return pageAfterSaleOrdersByPurchaseProcess(loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO);
            default:
                return new PageDataResp<>(0L, new ArrayList<>());
        }
    }

    /**
     * 售后能力 - “代客售后” - 查询退货换货商品列表
     *
     * @param loginUser 登录用户
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<OrderAfterSalePageQueryResp> pageAgentAfterSaleOrders(UserLoginCacheDTO loginUser, OrderAgentAfterSalePageDataReq pageVO) {
        //Step 1: 参数判断
        OrderTypeEnum orderType = OrderTypeEnum.parse(pageVO.getOrderType());
        if(orderType == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_TYPE_DOES_NOT_EXIST);
        }

        //Step 2: 规则
        // 2-1. 查询采购订单中选择的供应会员的订单数据，根据选择的售后订单类型进行筛选，只显示已收到第一批货之后的订单且当前订单外部状态不是待确认支付结果
        // 2-2. 如果订单类型为现货采购、询价采购、集采、积分兑换、渠道直采、渠道现货、渠道积分兑换，
        //      查询交易流程规则配置中流程类型为售后换货流程的工作流
        // 2-3. 如果订单类型为采购询价合同、采购竞价合同、采购招标合同只允许选择合同编号相同的订单，
        //      查询采购流程规则配置的流程类型为售后换货流程的适用当前合同的工作流

        //Step 3: 在IMobileOrderCommonService中有个类似的接口，如这里修改了则要做相应的修改
        switch (orderType) {
            case SPOT_PURCHASING:
            case INQUIRY_TO_PURCHASE:
            case COLLECTIVE_PURCHASE:
            case CREDITS_EXCHANGE:
//            case CHANNEL_STRAIGHT_MINING:
//            case CHANNEL_SPOT:
//            case CHANNEL_POINT_EXCHANGE:
                return pageAgentAfterSaleOrdersByTradeProcess(loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO);
            case QUERY_PRICE_CONTRACT:
            case PRICE_COMPETITION_CONTRACT_PURCHASE:
            case PURCHASE_CONTRACT_BIDDING:
            case REQUISITION_TO_PURCHASE:
                return pageAgentAfterSaleOrdersByPurchaseProcess(loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO);
            default:
                return new PageDataResp<>(0L, new ArrayList<>());
        }
    }

    /**
     * 售后能力 - 查询已经确认支付结果的支付记录列表
     *
     * @param loginUser 登录用户
     * @param idVO    接口参数
     * @return 查询结果
     */
    @Override
    public List<OrderAfterSalePaymentDetailResp> findAfterSaleOrderPayments(UserLoginCacheDTO loginUser, OrderIdReq idVO) {
        return findAfterSaleOrderPayments(idVO);
    }

    /**
     * 售后能力 - 查询已经确认支付结果的支付记录列表
     *
     * @param idVO 接口参数
     * @return 查询结果
     */
    @Override
    public List<OrderAfterSalePaymentDetailResp> findAfterSaleOrderPayments(OrderIdReq idVO) {
        QOrderPaymentDO qOrderPayment = QOrderPaymentDO.orderPaymentDO;
        return jpaQueryFactory.select(Projections.constructor(OrderAfterSalePaymentDetailResp.class, qOrderPayment.id, qOrderPayment.payTime, qOrderPayment.batchNo, qOrderPayment.payNode, qOrderPayment.payRate, qOrderPayment.payAmount, qOrderPayment.fundMode, qOrderPayment.payType, qOrderPayment.payChannel, qOrderPayment.tradeNo))
                .from(qOrderPayment)
                .where(qOrderPayment.order.id.eq(idVO.getOrderId()))
                .where(qOrderPayment.payAmount.gt(BigDecimal.ZERO))
                .where(qOrderPayment.outerStatus.eq(OrderOuterStatusEnum.ACCOMPLISHED.getCode())).fetch();
    }

    /**
     * 物流能力 - 分页查询“待发货”状态的订单列表
     *
     * @param loginUser 登录用户
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<LogisticsOrderQueryResp> pageLogisticsOrders(UserLoginCacheDTO loginUser, OrderLogisticsPageDataReq pageVO) {
        QOrderDO qOrder = QOrderDO.orderDO;
        JPAQuery<LogisticsOrderQueryResp> query = jpaQueryFactory.select(Projections.constructor(LogisticsOrderQueryResp.class, qOrder.id, qOrder.orderNo, qOrder.submitTime, qOrder.digest, qOrder.buyerMemberId, qOrder.buyerRoleId, qOrder.buyerMemberName))
                .from(qOrder)
                .where(qOrder.vendorMemberId.eq(loginUser.getMemberId()).and(qOrder.vendorRoleId.eq(loginUser.getMemberRoleId())))
                .where(qOrder.outerStatus.eq(OrderOuterStatusEnum.TO_CONFIRM_DELIVERY.getCode()));

        //订单编号
        if(StringUtils.hasLength(pageVO.getOrderNo())) {
            query.where(qOrder.orderNo.like("%" + pageVO.getOrderNo().trim() + "%"));
        }

        //订单摘要
        if(StringUtils.hasLength(pageVO.getDigest())) {
            query.where(qOrder.digest.like("%" + pageVO.getDigest().trim() + "%"));
        }

        //订单起始时间
        if(StringUtils.hasLength(pageVO.getStartDate())) {
            query.where(qOrder.submitTime.after(LocalDateTime.parse(pageVO.getStartDate().concat(" 00:00:00"), OrderConstant.DEFAULT_TIME_FORMATTER)));
        }

        //订单起始时间
        if(StringUtils.hasLength(pageVO.getEndDate())) {
            query.where(qOrder.submitTime.before(LocalDateTime.parse(pageVO.getEndDate().concat(" 23:59:59"), OrderConstant.DEFAULT_TIME_FORMATTER)));
        }

        //Step 4: 倒序排序、分页、总数
        long totalCount = query.fetchCount();
        query.orderBy(qOrder.submitTime.desc());
        query.limit(pageVO.getPageSize()).offset(pageVO.getCurrentOffset());

        return new PageDataResp<>(totalCount, query.fetch());
    }

    /**
     * 物流能力 - 根据订单Id，分页查询订单商品列表
     *
     * @param loginUser 登录用户
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<LogisticsOrderProductQueryResp> pageLogisticsOrderProducts(UserLoginCacheDTO loginUser, OrderProductLogisticsPageDataReq pageVO) {
        QOrderProductDO qOrderProduct = QOrderProductDO.orderProductDO;
        JPAQuery<LogisticsOrderProductQueryResp> query = jpaQueryFactory.select(Projections.constructor(LogisticsOrderProductQueryResp.class, qOrderProduct.id, qOrderProduct.productId, qOrderProduct.name, qOrderProduct.category, qOrderProduct.brand, qOrderProduct.spec))
                .from(qOrderProduct)
                .where(qOrderProduct.order.id.eq(pageVO.getOrderId()));

        //商品名称
        if(StringUtils.hasLength(pageVO.getName())) {
            query.where(qOrderProduct.name.like("%" + pageVO.getName().trim() + "%"));
        }

        //商品品类
        if(StringUtils.hasLength(pageVO.getCategory())) {
            query.where(qOrderProduct.category.like("%" + pageVO.getCategory().trim() + "%"));
        }

        //商品品牌
        if(StringUtils.hasLength(pageVO.getBrand())) {
            query.where(qOrderProduct.category.like("%" + pageVO.getBrand().trim() + "%"));
        }

        //Step 4: 顺序排序、分页、总数
        long totalCount = query.fetchCount();
        query.orderBy(qOrderProduct.id.asc());
        query.limit(pageVO.getPageSize()).offset(pageVO.getCurrentOffset());

        return new PageDataResp<>(totalCount, query.fetch());
    }

    /**
     * 加工能力 - 分页查询加工订单列表
     *
     * @param loginUser 登录用户
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<OrderEnhancePageQueryResp> pageEnhanceOrders(UserLoginCacheDTO loginUser, OrderEnhancePageDataReq pageVO) {
        //Step 1: 根据会员等级类型，校验订单类型参数
        //Step 1-1 : 会员类型：企业会员+个人会员，角色类型：服务提供者，数据来源于销售订单管理，取外部状态为已确认的订单且订单类型为现货采购、询价采购、需求采购、集采。
        //Step 1-2 : 会员类型：渠道企业会员+渠道个人会员，角色类型：服务提供者，数据来源于销售订单管理，取外部状态为已确认的订单且订单类型为渠道直采、渠道现货。
        if(!loginUser.getMemberRoleType().equals(RoleTypeEnum.SERVICE_PROVIDER.getCode())) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        List<Integer> orderTypes = Stream.of(OrderTypeEnum.INQUIRY_TO_PURCHASE.getCode(), OrderTypeEnum.NEED_TO_PURCHASE.getCode(), OrderTypeEnum.SPOT_PURCHASING.getCode(), OrderTypeEnum.COLLECTIVE_PURCHASE.getCode()).collect(Collectors.toList());
        if(NumberUtil.notNullOrZero(pageVO.getOrderType())) {
            if(loginUser.getMemberLevelType().equals(MemberLevelTypeEnum.MERCHANT.getCode())) {
                orderTypes = Stream.of(OrderTypeEnum.INQUIRY_TO_PURCHASE.getCode(), OrderTypeEnum.NEED_TO_PURCHASE.getCode(), OrderTypeEnum.SPOT_PURCHASING.getCode(), OrderTypeEnum.COLLECTIVE_PURCHASE.getCode()).collect(Collectors.toList());
            }

            if(!orderTypes.contains(pageVO.getOrderType())) {
                throw new BusinessException(ResponseCodeEnum.ORDER_TYPE_DOES_NOT_EXIST);
            }
        }

        QOrderDO qOrder = QOrderDO.orderDO;
        QOrderProductDO qOrderProduct = QOrderProductDO.orderProductDO;
        QOrderTradeProcessDO qOrderTradeProcess = QOrderTradeProcessDO.orderTradeProcessDO;
        QOrderTradeProcessProductDO qOrderTradeProcessProduct = QOrderTradeProcessProductDO.orderTradeProcessProductDO;

        //Step 2: 定义总数查询关系
        JPAQuery<Long> countQuery = jpaQueryFactory.selectDistinct(qOrder.id).from(qOrder)
                .leftJoin(qOrderProduct).on(qOrder.id.eq(qOrderProduct.order.id))
                .leftJoin(qOrderTradeProcess).on(qOrder.shopId.eq(qOrderTradeProcess.shopId).and(qOrder.vendorMemberId.eq(qOrderTradeProcess.memberId)).and(qOrder.vendorRoleId.eq(qOrderTradeProcess.roleId)))
                .leftJoin(qOrderTradeProcessProduct).on(qOrderTradeProcess.id.eq(qOrderTradeProcessProduct.process.id))
                .where(qOrder.vendorMemberId.eq(loginUser.getMemberId()).and(qOrder.vendorRoleId.eq(loginUser.getMemberRoleId())));

        //Step 2: 固定的查询条件
        //订单状态：外部状态为“已确认”
        List<Integer> notInOuterStatus = Stream.of(OrderOuterStatusEnum.TO_SUBMIT.getCode(), OrderOuterStatusEnum.TO_CONFIRM.getCode(), OrderOuterStatusEnum.NOT_ACCEPT.getCode(), OrderOuterStatusEnum.TO_ARCHIVE.getCode(), OrderOuterStatusEnum.ACCOMPLISHED.getCode(), OrderOuterStatusEnum.CANCELLED.getCode(), OrderOuterStatusEnum.TERMINATED.getCode()).collect(Collectors.toList());
        countQuery.where(qOrder.outerStatus.notIn(notInOuterStatus));

        //Step 3: 动态的查询条件
        //合同编号
        if(StringUtils.hasLength(pageVO.getOrderNo())) {
            countQuery.where(qOrder.orderNo.like("%" + pageVO.getOrderNo().trim() + "%"));
        }

        //订单摘要
        if(StringUtils.hasLength(pageVO.getDigest())) {
            countQuery.where(qOrder.digest.like("%" + pageVO.getDigest().trim() + "%"));
        }

        //采购会员名称
        if(StringUtils.hasLength(pageVO.getBuyerMemberName())) {
            countQuery.where(qOrder.buyerMemberName.like("%" + pageVO.getBuyerMemberName().trim() + "%"));
        }

        //订单起始时间
        if(StringUtils.hasLength(pageVO.getStartDate())) {
            countQuery.where(qOrder.createTime.after(LocalDateTime.parse(pageVO.getStartDate().concat(" 00:00:00"), OrderConstant.DEFAULT_TIME_FORMATTER)));
        }

        //订单起始时间
        if(StringUtils.hasLength(pageVO.getEndDate())) {
            countQuery.where(qOrder.createTime.before(LocalDateTime.parse(pageVO.getEndDate().concat(" 23:59:59"), OrderConstant.DEFAULT_TIME_FORMATTER)));
        }

        //订单类型
        if(NumberUtil.notNullOrZero(pageVO.getOrderType())) {
            countQuery.where(qOrder.orderType.eq(pageVO.getOrderType()));
        } else {
            countQuery.where(qOrder.orderType.in(orderTypes));
        }

        //Step 4: 共用的查询条件（字表的查询条件）
        //流程类型
        countQuery.where(qOrderTradeProcess.processType.eq(OrderTradeProcessTypeEnum.PRODUCTION.getCode()));
        //流程规则配置
        countQuery.where(qOrderTradeProcess.allProducts.isTrue().or(qOrderTradeProcessProduct.productId.eq(qOrderProduct.productId).and(qOrderTradeProcessProduct.skuId.eq(qOrderProduct.skuId))));

        //Step 5: 倒序排序、分页、总数
        long totalCount = countQuery.fetchCount();
        countQuery.orderBy(qOrder.id.desc());
        countQuery.limit(pageVO.getPageSize()).offset(pageVO.getCurrentOffset());

        //Step 6: 定义关联关系，OrderDO表必须要有distinct()
        JPAQuery<?> query = jpaQueryFactory.from(qOrder).distinct()
                .leftJoin(qOrderProduct).on(qOrder.id.eq(qOrderProduct.order.id))
                .leftJoin(qOrderTradeProcess).on(qOrder.shopId.eq(qOrderTradeProcess.shopId).and(qOrder.vendorMemberId.eq(qOrderTradeProcess.memberId)).and(qOrder.vendorRoleId.eq(qOrderTradeProcess.roleId)))
                .leftJoin(qOrderTradeProcessProduct).on(qOrderTradeProcess.id.eq(qOrderTradeProcessProduct.process.id))
                .where(qOrder.id.in(countQuery.fetch()));

        //Step 7: 共有的查询条件
        //流程类型
        query.where(qOrderTradeProcess.processType.eq(OrderTradeProcessTypeEnum.PRODUCTION.getCode()));
        //流程规则配置
        query.where(qOrderTradeProcess.allProducts.isTrue().or(qOrderTradeProcessProduct.productId.eq(qOrderProduct.productId).and(qOrderTradeProcessProduct.skuId.eq(qOrderProduct.skuId))));

        //Step 8: 使用transform()对结果进行聚合统计，并通过Projections.Constructor到VO对象
        Map<Long, OrderEnhancePageQueryResp> transform = query.transform(GroupBy.groupBy(qOrder.id).as(
                Projections.constructor(OrderEnhancePageQueryResp.class, qOrder.id, qOrder.orderNo, qOrder.digest, qOrder.buyerMemberId, qOrder.buyerRoleId, qOrder.buyerMemberName, qOrder.orderType, qOrder.submitTime, qOrder.outerStatus,
                        GroupBy.list(Projections.constructor(OrderEnhanceProductDetailResp.class,
                                qOrderTradeProcess.processKey,
                                qOrderProduct.id,
                                qOrderProduct.name,
                                qOrderProduct.category,
                                qOrderProduct.brand,
                                qOrderProduct.spec,
                                qOrderProduct.unit,
                                qOrderProduct.quantity,
                                qOrderProduct.enhanceCount
                        )))));

        return new PageDataResp<>(totalCount, new ArrayList<>(transform.values()));
    }

    /**
     * 支付能力 - 查询会员支付渠道列表
     *
     * @param loginUser 登录用户
     * @param payTypeVO 接口参数
     * @return 查询结果
     */
    @Override
    public List<OrderPayChannelDetailResp> findMemberPayChannels(UserLoginCacheDTO loginUser, OrderMemberPayTypeReq payTypeVO) {
        List<PlatformPayTypeBO> platformPayTypes = baseOrderProcessService.findPlatformMemberPayment(payTypeVO.getMemberId(), payTypeVO.getRoleId());
        return platformPayTypes.stream().filter(platformPayType -> platformPayType.getPayType().equals(payTypeVO.getPayType())).flatMap(platformPayType -> platformPayType.getChannels().stream()).map(payChannel -> new OrderPayChannelDetailResp(payChannel.getPayChannel(), OrderPayChannelEnum.getNameByCode(payChannel.getPayChannel()))).sorted(Comparator.comparingInt(OrderPayChannelDetailResp::getPayChannel)).collect(Collectors.toList());
    }

    /**
     * 结算能力 - 开票管理 - 根据订单号查看订单详情
     * @param loginUser 登录用户
     * @param orderVO 接口参数
     * @return 查询结果
     */
    @Override
    public VendorOrderDetailResp findSettleOrderDetailByOrderNo(UserLoginCacheDTO loginUser, OrderNoReq orderVO) {
        OrderDO order = orderRepository.findFirstByOrderNo(orderVO.getOrderNo());
        if (order == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }
        if (!order.getVendorMemberId().equals(loginUser.getMemberId()) || !order.getVendorRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_WRONG_ROLE);
        }

        return baseOrderService.getVendorOrderDetail(order);
    }

    /**
     * 商品能力 - 上架指引查询商品是否配置了交易流程
     *
     * @param loginUser 登录用户
     * @param processVO 接口参数
     * @return 查询结果
     */
    @Override
    public List<OrderProductProcessQueryResp> findProductProcess(UserLoginCacheDTO loginUser, OrderProductProcessReq processVO) {
        return baseOrderProcessService.findProductProcess(loginUser.getMemberId(), loginUser.getMemberRoleId(), processVO.getShopIds(), processVO.getShopType(), processVO.getProducts());
    }

    /**
     * 根据交易流程规则配置查询售后订单
     * @param buyerMemberId 采购会员Id（当前会员Id）
     * @param buyerRoleId   采购会员角色Id（当前会员角色Id）
     * @param pageVO         接口参数
     * @return               查询结果
     */
    private PageDataResp<OrderAfterSalePageQueryResp> pageAfterSaleOrdersByTradeProcess(Long buyerMemberId, Long buyerRoleId, OrderAfterSalePageDataReq pageVO) {
        QOrderDO qOrder = QOrderDO.orderDO;
        QOrderProductDO qOrderProduct = QOrderProductDO.orderProductDO;
        QOrderMaterialDO qOrderMaterial = QOrderMaterialDO.orderMaterialDO;
//        QOrderContractDO qOrderContract = QOrderContractDO.orderContractDO;
        QOrderTradeProcessDO qOrderTradeProcess = QOrderTradeProcessDO.orderTradeProcessDO;
        QOrderTradeProcessProductDO qOrderTradeProcessProduct = QOrderTradeProcessProductDO.orderTradeProcessProductDO;

        //由于QueryDsl的关联查询中，无法对主表进行分页，所以要分开查询
        //Step 1: 构造总数查询，所有查询条件的表都必须关联定义
        // 构建链接表条件
        BooleanExpression joinOrderTradeProcessOn = Expressions.anyOf(qOrder.vendorMemberId.eq(qOrderTradeProcess.memberId).and(qOrder.vendorRoleId.eq(qOrderTradeProcess.roleId)), qOrderTradeProcess.memberId.eq(0L).and(qOrderTradeProcess.roleId.eq(0L)));
        JPAQuery<Long> countQuery = jpaQueryFactory.selectDistinct(qOrder.id).from(qOrder)
                .leftJoin(qOrderProduct).on(qOrder.id.eq(qOrderProduct.order.id))
                .leftJoin(qOrderTradeProcess).on(joinOrderTradeProcessOn)
                .leftJoin(qOrderTradeProcessProduct).on(qOrderTradeProcess.id.eq(qOrderTradeProcessProduct.process.id))
                .where(qOrder.orderType.eq(pageVO.getOrderType()));

        //Step 2: 固定的查询条件
        //采购会员、供应会员
        countQuery.where(qOrder.buyerMemberId.eq(buyerMemberId).and(qOrder.buyerRoleId.eq(buyerRoleId)).and(qOrder.vendorMemberId.eq(pageVO.getVendorMemberId())).and(qOrder.vendorRoleId.eq(pageVO.getVendorRoleId())));

        //订单状态：
        //换货：只显示已收到第一批货之后的订单且当前订单外部状态不是待确认支付结果
        //退货：只显示已发过第一批货之后、或已经支付成功过一次的订单且当前订单外部状态不是待确认支付结果，且订单状态不是已中止
        //维修：只显示已收到第一批货之后的订单
        if(pageVO.getAfterSalesType().equals(OrderTradeProcessTypeEnum.AFTER_SALES_EXCHANGES.getCode())) {
            countQuery.where(qOrder.hasReceived.isTrue().and(qOrder.outerStatus.ne(OrderOuterStatusEnum.TO_CONFIRM_PAYMENT.getCode())));
        } else if(pageVO.getAfterSalesType().equals(OrderTradeProcessTypeEnum.AFTER_SALES_RETURNS.getCode())) {
            countQuery.where(qOrder.hasDelivered.isTrue().or(qOrder.hasPaid.isTrue()));
            countQuery.where(qOrder.outerStatus.ne(OrderOuterStatusEnum.TO_CONFIRM_PAYMENT.getCode()));
        } else {
            countQuery.where(qOrder.hasReceived.isTrue());
        }

        //Step 3: 动态的查询条件
        //订单编号
        if(StringUtils.hasLength(pageVO.getOrderNo())) {
            countQuery.where(qOrder.orderNo.like("%" + pageVO.getOrderNo().trim() + "%"));
        }

        //订单摘要
        if(StringUtils.hasLength(pageVO.getDigest())) {
            countQuery.where(qOrder.digest.like("%" + pageVO.getDigest().trim() + "%"));
        }

        //订单起始时间
        if(StringUtils.hasLength(pageVO.getStartDate())) {
            countQuery.where(qOrder.createTime.after(LocalDateTime.parse(pageVO.getStartDate().concat(" 00:00:00"), OrderConstant.DEFAULT_TIME_FORMATTER)));
        }

        //订单起始时间
        if(StringUtils.hasLength(pageVO.getEndDate())) {
            countQuery.where(qOrder.createTime.before(LocalDateTime.parse(pageVO.getEndDate().concat(" 23:59:59"), OrderConstant.DEFAULT_TIME_FORMATTER)));
        }

        //Step 4: 总数查询和分页查询共用的查询条件（即关联表的查询条件），必须在列表查询中再次定义
        //只可选择已退货数量小于订单数量的订单商品
        countQuery.where(qOrderProduct.returnCount.loe(qOrderProduct.quantity));
        //流程类型、状态
        countQuery.where(qOrderTradeProcess.processType.eq(pageVO.getAfterSalesType()).and(qOrderTradeProcess.status.eq(EnableDisableStatusEnum.ENABLE.getCode())));
        //流程规则配置
//        countQuery.where(qOrderTradeProcess.allProducts.isTrue().or(qOrderTradeProcessProduct.productId.eq(qOrderProduct.productId).and(qOrderTradeProcessProduct.skuId.eq(qOrderProduct.skuId))));

        //Step 5: 先查总数，然后倒序排序、分页
        long totalCount = countQuery.fetchCount();
        countQuery.orderBy(qOrder.id.desc());
        countQuery.limit(pageVO.getPageSize()).offset(pageVO.getCurrentOffset());

        //Step 6: 定义关联关系，OrderDO表必须要有distinct()，这里对主表（订单表的查询条件不需要再定义了，要用 in 查询）
        JPAQuery<?> query = jpaQueryFactory.from(qOrder).distinct()
                .leftJoin(qOrderProduct).on(qOrder.id.eq(qOrderProduct.order.id))
                .leftJoin(qOrderMaterial).on(qOrderProduct.id.eq(qOrderMaterial.orderProduct.id))
//                .leftJoin(qOrderContract).on(qOrder.id.eq(qOrderContract.order.id))
                .leftJoin(qOrderTradeProcess).on(joinOrderTradeProcessOn)
                .leftJoin(qOrderTradeProcessProduct).on(qOrderTradeProcess.id.eq(qOrderTradeProcessProduct.process.id))
                .where(qOrder.id.in(countQuery.fetch()));

        //共用的查询条件也必须定义
        //只可选择已退货数量小于订单数量的订单商品
        query.where(qOrderProduct.returnCount.loe(qOrderProduct.quantity));
        //流程类型、状态
        query.where(qOrderTradeProcess.processType.eq(pageVO.getAfterSalesType()).and(qOrderTradeProcess.status.eq(EnableDisableStatusEnum.ENABLE.getCode())));
        //流程规则配置
//        query.where(qOrderTradeProcess.allProducts.isTrue().or(qOrderTradeProcessProduct.productId.eq(qOrderProduct.productId).and(qOrderTradeProcessProduct.skuId.eq(qOrderProduct.skuId))));

        //Step 7: 使用transform()对结果进行聚合统计，并通过Projections.Constructor到VO对象
        Map<Long, OrderAfterSalePageQueryResp> transform = query.transform(GroupBy.groupBy(qOrder.id).as(
                Projections.constructor(OrderAfterSalePageQueryResp.class, qOrder.shopId, qOrder.id, qOrder.orderNo, qOrder.digest, qOrder.vendorMemberId, qOrder.vendorRoleId, qOrder.vendorMemberName, qOrder.orderType, qOrder.createTime, qOrder.outerStatus,
                        GroupBy.list(Projections.constructor(OrderAfterSaleProductDetailResp.class,
                                qOrderProduct.id,
                                qOrder.orderType,
//                                qOrderTradeProcess.processKey,
                                qOrderProduct.skuId,
                                qOrderProduct.productNo,
                                qOrderProduct.logo,
                                qOrderProduct.name,
                                qOrderProduct.category,
                                qOrderProduct.brand,
                                qOrderProduct.unit,
                                qOrderProduct.spec,
                                qOrderProduct.quantity,
                                qOrderProduct.refPrice,
                                qOrderProduct.amount,
                                qOrderProduct.paidAmount,
                                qOrderProduct.exchangeCount,
                                qOrderProduct.returnCount,
                                qOrderProduct.maintainCount,
                                qOrderProduct.returnAmount,
                                qOrderProduct.tax,
                                qOrderProduct.taxRate,
                                qOrderProduct.priceType,
                                qOrderMaterial.skuId,
                                qOrderMaterial.productNo,
                                qOrderMaterial.name,
                                qOrderMaterial.category,
                                qOrderMaterial.brand,
                                qOrderMaterial.spec
                        )))));

        List<OrderAfterSalePageQueryResp> queryResult = new ArrayList<>(transform.values());
        //Step 8: 查询店铺Logo
        findShopLogos(queryResult);

        return new PageDataResp<>(totalCount, queryResult);
    }

    /**
     * 根据采购流程规则配置查询售后订单
     * @param buyerMemberId 采购会员Id（当前会员Id）
     * @param buyerRoleId   采购会员角色Id（当前会员角色Id）
     * @param pageVO         接口参数
     * @return               查询结果
     */
    private PageDataResp<OrderAfterSalePageQueryResp> pageAfterSaleOrdersByPurchaseProcess(Long buyerMemberId, Long buyerRoleId, OrderAfterSalePageDataReq pageVO) {
        QOrderDO qOrder = QOrderDO.orderDO;
        QOrderProductDO qOrderProduct = QOrderProductDO.orderProductDO;
        QOrderMaterialDO qOrderMaterial = QOrderMaterialDO.orderMaterialDO;
//        QOrderContractDO qOrderContract = QOrderContractDO.orderContractDO;
        QOrderPurchaseProcessDO qOrderPurchaseProcess = QOrderPurchaseProcessDO.orderPurchaseProcessDO;
        QOrderPurchaseProcessContractDO qOrderPurchaseProcessContract = QOrderPurchaseProcessContractDO.orderPurchaseProcessContractDO;

        //Step 1: 定义总数查询
        BooleanExpression joinOrderTradeProcessOn = Expressions.anyOf(qOrder.vendorMemberId.eq(qOrderPurchaseProcess.memberId).and(qOrder.vendorRoleId.eq(qOrderPurchaseProcess.roleId)), qOrderPurchaseProcess.memberId.eq(0L).and(qOrderPurchaseProcess.roleId.eq(0L)));
        JPAQuery<Long> countQuery = jpaQueryFactory.selectDistinct(qOrder.id).from(qOrder)
                .leftJoin(qOrderProduct).on(qOrder.id.eq(qOrderProduct.order.id))
                .leftJoin(qOrderMaterial).on(qOrderProduct.id.eq(qOrderMaterial.orderProduct.id))
//                .leftJoin(qOrderContract).on(qOrder.id.eq(qOrderContract.order.id))
                .leftJoin(qOrderPurchaseProcess).on(joinOrderTradeProcessOn)
                .leftJoin(qOrderPurchaseProcessContract).on(qOrderPurchaseProcess.id.eq(qOrderPurchaseProcessContract.process.id))
                .where(qOrder.orderType.eq(pageVO.getOrderType()));

        //Step 2: 固定的查询条件
        //采购会员、供应会员与订单类型
        countQuery.where(qOrder.buyerMemberId.eq(buyerMemberId).and(qOrder.buyerRoleId.eq(buyerRoleId)).and(qOrder.vendorMemberId.eq(pageVO.getVendorMemberId())).and(qOrder.vendorRoleId.eq(pageVO.getVendorRoleId())));

        //订单状态：
        //换货：只显示已收到第一批货之后的订单且当前订单外部状态不是待确认支付结果
        //退货：只显示已发过第一批货之后、或已经支付成功过一次的订单且当前订单外部状态不是待确认支付结果，且订单状态不是已中止
        //维修：只显示已收到第一批货之后的订单
        if(pageVO.getAfterSalesType().equals(OrderTradeProcessTypeEnum.AFTER_SALES_EXCHANGES.getCode())) {
            countQuery.where(qOrder.hasReceived.isTrue().and(qOrder.outerStatus.ne(OrderOuterStatusEnum.TO_CONFIRM_PAYMENT.getCode())));
        } else if(pageVO.getAfterSalesType().equals(OrderTradeProcessTypeEnum.AFTER_SALES_RETURNS.getCode())) {
            countQuery.where(qOrder.hasDelivered.isTrue().or(qOrder.hasPaid.isTrue()));
            countQuery.where(qOrder.outerStatus.ne(OrderOuterStatusEnum.TO_CONFIRM_PAYMENT.getCode()).and(qOrder.outerStatus.ne(OrderOuterStatusEnum.TERMINATED.getCode())));
        } else {
            countQuery.where(qOrder.hasReceived.isTrue());
        }

        //Step 3: 动态的查询条件
        //合同编号
        if(StringUtils.hasLength(pageVO.getOrderNo())) {
            countQuery.where(qOrder.orderNo.like("%" + pageVO.getOrderNo().trim() + "%"));
        }

        //订单摘要
        if(StringUtils.hasLength(pageVO.getDigest())) {
            countQuery.where(qOrder.digest.like("%" + pageVO.getDigest().trim() + "%"));
        }

        //订单起始时间
        if(StringUtils.hasLength(pageVO.getStartDate())) {
            countQuery.where(qOrder.createTime.after(LocalDateTime.parse(pageVO.getStartDate().concat(" 00:00:00"), OrderConstant.DEFAULT_TIME_FORMATTER)));
        }

        //订单起始时间
        if(StringUtils.hasLength(pageVO.getEndDate())) {
            countQuery.where(qOrder.createTime.before(LocalDateTime.parse(pageVO.getEndDate().concat(" 23:59:59"), OrderConstant.DEFAULT_TIME_FORMATTER)));
        }

        //Step 4: 共用的查询条件
        //只可选择已退货数量小于订单数量的订单商品
        countQuery.where(qOrderProduct.returnCount.lt(qOrderProduct.quantity));
        //流程类型、状态
        countQuery.where(qOrderPurchaseProcess.processType.eq(pageVO.getAfterSalesType()).and(qOrderPurchaseProcess.status.eq(EnableDisableStatusEnum.ENABLE.getCode())));
        //流程规则配置
//        countQuery.where(qOrderPurchaseProcess.allContracts.isTrue().or(qOrderPurchaseProcessContract.contractId.eq(qOrderContract.contractId)));

        //Step 5: 倒序排序、分页、总数
        long totalCount = countQuery.fetchCount();
        countQuery.orderBy(qOrder.id.desc());
        countQuery.limit(pageVO.getPageSize()).offset(pageVO.getCurrentOffset());

        //Step 6: 定义关联关系，OrderDO表必须要有distinct()
        //        这里订单Id用了in() 查询
        JPAQuery<?> query = jpaQueryFactory.from(qOrder).distinct()
                .leftJoin(qOrderProduct).on(qOrder.id.eq(qOrderProduct.order.id))
                .leftJoin(qOrderMaterial).on(qOrderProduct.id.eq(qOrderMaterial.orderProduct.id))
//                .leftJoin(qOrderContract).on(qOrder.id.eq(qOrderContract.order.id))
                .leftJoin(qOrderPurchaseProcess).on(joinOrderTradeProcessOn)
                .leftJoin(qOrderPurchaseProcessContract).on(qOrderPurchaseProcess.id.eq(qOrderPurchaseProcessContract.process.id))
                .where(qOrder.id.in(countQuery.fetch()));

        //Step 7: 共用的查询条件
        //只可选择已退货数量小于订单数量的订单商品
        query.where(qOrderProduct.returnCount.lt(qOrderProduct.quantity));
        //流程类型、状态
        query.where(qOrderPurchaseProcess.processType.eq(pageVO.getAfterSalesType()).and(qOrderPurchaseProcess.status.eq(EnableDisableStatusEnum.ENABLE.getCode())));
        //流程规则配置
//        query.where(qOrderPurchaseProcess.allContracts.isTrue().or(qOrderPurchaseProcessContract.contractId.eq(qOrderContract.contractId)));

        //Step 8: 使用transform()对结果进行聚合统计，并通过Projections.Constructor到VO对象
        Map<Long, OrderAfterSalePageQueryResp> transform = query.transform(GroupBy.groupBy(qOrder.id).as(
                Projections.constructor(OrderAfterSalePageQueryResp.class, qOrder.shopId, qOrder.id, qOrder.orderNo, qOrder.digest, qOrder.vendorMemberId, qOrder.vendorRoleId, qOrder.vendorMemberName, qOrder.orderType, qOrder.createTime, qOrder.outerStatus,
                        GroupBy.list(Projections.constructor(OrderAfterSaleProductDetailResp.class,
                                qOrderProduct.id,
                                qOrder.orderType,
//                                qOrderPurchaseProcess.processKey,
                                qOrderProduct.skuId,
                                qOrderProduct.productNo,
                                qOrderProduct.logo,
                                qOrderProduct.name,
                                qOrderProduct.category,
                                qOrderProduct.brand,
                                qOrderProduct.unit,
                                qOrderProduct.spec,
                                qOrderProduct.quantity,
                                qOrderProduct.refPrice,
                                qOrderProduct.amount,
                                qOrderProduct.paidAmount,
                                qOrderProduct.exchangeCount,
                                qOrderProduct.returnCount,
                                qOrderProduct.maintainCount,
                                qOrderProduct.returnAmount,
                                qOrderProduct.tax,
                                qOrderProduct.taxRate,
                                qOrderProduct.priceType,
                                qOrderMaterial.skuId,
                                qOrderMaterial.productNo,
                                qOrderMaterial.name,
                                qOrderMaterial.category,
                                qOrderMaterial.brand,
                                qOrderMaterial.spec
                        )))));

        List<OrderAfterSalePageQueryResp> queryResult = new ArrayList<>(transform.values());

        //Step 9: 查询店铺Logo
        findShopLogos(queryResult);

        return new PageDataResp<>(totalCount, queryResult);
    }

    /**
     * “代客售后” - 根据交易流程规则配置查询售后订单
     * @param vendorMemberId 供应会员Id（当前会员Id）
     * @param vendorRoleId   供应会员角色Id（当前会员角色Id）
     * @param pageVO         接口参数
     * @return               查询结果
     */
    private PageDataResp<OrderAfterSalePageQueryResp> pageAgentAfterSaleOrdersByTradeProcess(Long vendorMemberId, Long vendorRoleId, OrderAgentAfterSalePageDataReq pageVO) {
        QOrderDO qOrder = QOrderDO.orderDO;
        QOrderProductDO qOrderProduct = QOrderProductDO.orderProductDO;
        QOrderMaterialDO qOrderMaterial = QOrderMaterialDO.orderMaterialDO;
//        QOrderContractDO qOrderContract = QOrderContractDO.orderContractDO;
        QOrderTradeProcessDO qOrderTradeProcess = QOrderTradeProcessDO.orderTradeProcessDO;
        QOrderTradeProcessProductDO qOrderTradeProcessProduct = QOrderTradeProcessProductDO.orderTradeProcessProductDO;

        //Step 1: 定义总数查询
        BooleanExpression joinOrderTradeProcessOn = Expressions.anyOf(qOrder.vendorMemberId.eq(qOrderTradeProcess.memberId).and(qOrder.vendorRoleId.eq(qOrderTradeProcess.roleId)), qOrderTradeProcess.memberId.eq(0L).and(qOrderTradeProcess.roleId.eq(0L)));
        JPAQuery<Long> countQuery = jpaQueryFactory.selectDistinct(qOrder.id).from(qOrder)
                .leftJoin(qOrderProduct).on(qOrder.id.eq(qOrderProduct.order.id))
                .leftJoin(qOrderMaterial).on(qOrderProduct.id.eq(qOrderMaterial.orderProduct.id))
//                .leftJoin(qOrderContract).on(qOrder.id.eq(qOrderContract.order.id))
                .leftJoin(qOrderTradeProcess).on(joinOrderTradeProcessOn)
                .leftJoin(qOrderTradeProcessProduct).on(qOrderTradeProcess.id.eq(qOrderTradeProcessProduct.process.id))
                .where(qOrder.orderType.eq(pageVO.getOrderType()));

        //Step 2: 固定的查询条件
        //采购会员、供应会员
        countQuery.where(qOrder.buyerMemberId.eq(pageVO.getBuyerMemberId()).and(qOrder.buyerRoleId.eq(pageVO.getBuyerRoleId())).and(qOrder.vendorMemberId.eq(vendorMemberId)).and(qOrder.vendorRoleId.eq(vendorRoleId)));

        //订单状态：
        //换货：只显示已收到第一批货之后的订单且当前订单外部状态不是待确认支付结果,排除外部状态是待支付
        //退货：只显示已发过第一批货之后、或已经支付成功过一次的订单且当前订单外部状态不是待确认支付结果，且订单状态不是已中止, 排除待支付
        //维修：只显示已收到第一批货之后的订单,排除待发货
        if(pageVO.getAfterSalesType().equals(OrderTradeProcessTypeEnum.AFTER_SALES_EXCHANGES.getCode())) {
            countQuery.where(qOrder.hasReceived.isTrue()
                    .and(qOrder.outerStatus.ne(OrderOuterStatusEnum.TO_CONFIRM_PAYMENT.getCode()))
                    .and(qOrder.outerStatus.ne(OrderOuterStatusEnum.TO_PAY.getCode())));
        } else if(pageVO.getAfterSalesType().equals(OrderTradeProcessTypeEnum.AFTER_SALES_RETURNS.getCode())) {
            countQuery.where(qOrder.hasDelivered.isTrue().or(qOrder.hasPaid.isTrue()));
            countQuery.where(qOrder.outerStatus.ne(OrderOuterStatusEnum.TO_CONFIRM_PAYMENT.getCode())
                    .and(qOrder.outerStatus.ne(OrderOuterStatusEnum.TERMINATED.getCode()))
                    .and(qOrder.outerStatus.ne(OrderOuterStatusEnum.TO_CONFIRM_DELIVERY.getCode()))
                    .and(qOrder.outerStatus.ne(OrderOuterStatusEnum.TO_PAY.getCode())));
        } else {
            countQuery.where(qOrder.hasReceived.isTrue());
            countQuery.where(qOrder.outerStatus.ne(OrderOuterStatusEnum.TO_CONFIRM_DELIVERY.getCode())
                    .and(qOrder.outerStatus.ne(OrderOuterStatusEnum.TO_PAY.getCode())));

        }

        //Step 3: 动态的查询条件
        //合同编号
        if(StringUtils.hasLength(pageVO.getOrderNo())) {
            countQuery.where(qOrder.orderNo.like("%" + pageVO.getOrderNo().trim() + "%"));
        }

        //订单摘要
        if(StringUtils.hasLength(pageVO.getDigest())) {
            countQuery.where(qOrder.digest.like("%" + pageVO.getDigest().trim() + "%"));
        }

        //订单起始时间
        if(StringUtils.hasLength(pageVO.getStartDate())) {
            countQuery.where(qOrder.createTime.after(LocalDateTime.parse(pageVO.getStartDate().concat(" 00:00:00"), OrderConstant.DEFAULT_TIME_FORMATTER)));
        }

        //订单起始时间
        if(StringUtils.hasLength(pageVO.getEndDate())) {
            countQuery.where(qOrder.createTime.before(LocalDateTime.parse(pageVO.getEndDate().concat(" 23:59:59"), OrderConstant.DEFAULT_TIME_FORMATTER)));
        }

        //Step 4: 共用的查询条件（字表的查询条件）
        //只可选择已退货数量小于订单数量的订单商品
        countQuery.where(qOrderProduct.returnCount.lt(qOrderProduct.quantity));
        //流程类型、状态
        countQuery.where(qOrderTradeProcess.processType.eq(pageVO.getAfterSalesType()).and(qOrderTradeProcess.status.eq(EnableDisableStatusEnum.ENABLE.getCode())));
        //流程规则配置
//        countQuery.where(qOrderTradeProcess.allProducts.isTrue().or(qOrderTradeProcessProduct.productId.eq(qOrderProduct.productId).and(qOrderTradeProcessProduct.skuId.eq(qOrderProduct.skuId))));

        //Step 5: 总数、倒序排序、分页
        long totalCount = countQuery.fetchCount();
        countQuery.orderBy(qOrder.id.desc());
        countQuery.limit(pageVO.getPageSize()).offset(pageVO.getCurrentOffset());

        //Step 6: 定义关联关系，OrderDO表必须要有distinct()
        JPAQuery<?> query = jpaQueryFactory.from(qOrder).distinct()
                .leftJoin(qOrderProduct).on(qOrder.id.eq(qOrderProduct.order.id))
                .leftJoin(qOrderMaterial).on(qOrderProduct.id.eq(qOrderMaterial.orderProduct.id))
//                .leftJoin(qOrderContract).on(qOrder.id.eq(qOrderContract.order.id))
                .leftJoin(qOrderTradeProcess).on(joinOrderTradeProcessOn)
                .leftJoin(qOrderTradeProcessProduct).on(qOrderTradeProcess.id.eq(qOrderTradeProcessProduct.process.id))
                .where(qOrder.id.in(countQuery.fetch()));

        //Step 7: 共用的查询条件（字表的查询条件）
        //只可选择已退货数量小于订单数量的订单商品
        query.where(qOrderProduct.returnCount.lt(qOrderProduct.quantity));
        //流程类型、状态
        query.where(qOrderTradeProcess.processType.eq(pageVO.getAfterSalesType()).and(qOrderTradeProcess.status.eq(EnableDisableStatusEnum.ENABLE.getCode())));
        //流程规则配置
//        query.where(qOrderTradeProcess.allProducts.isTrue().or(qOrderTradeProcessProduct.productId.eq(qOrderProduct.productId).and(qOrderTradeProcessProduct.skuId.eq(qOrderProduct.skuId))));

        //Step 8: 使用transform()对结果进行聚合统计，并通过Projections.Constructor到VO对象
        Map<Long, OrderAfterSalePageQueryResp> transform = query.transform(GroupBy.groupBy(qOrder.id).as(
                Projections.constructor(OrderAfterSalePageQueryResp.class, qOrder.shopId, qOrder.id, qOrder.orderNo, qOrder.digest, qOrder.vendorMemberId, qOrder.vendorRoleId, qOrder.vendorMemberName, qOrder.orderType, qOrder.createTime, qOrder.outerStatus,
                        GroupBy.list(Projections.constructor(OrderAfterSaleProductDetailResp.class,
                                qOrderProduct.id,
                                qOrder.orderType,
//                                qOrderTradeProcess.processKey,
                                qOrderProduct.skuId,
                                qOrderProduct.productNo,
                                qOrderProduct.logo,
                                qOrderProduct.name,
                                qOrderProduct.category,
                                qOrderProduct.brand,
                                qOrderProduct.unit,
                                qOrderProduct.spec,
                                qOrderProduct.quantity,
                                qOrderProduct.refPrice,
                                qOrderProduct.amount,
                                qOrderProduct.paidAmount,
                                qOrderProduct.exchangeCount,
                                qOrderProduct.returnCount,
                                qOrderProduct.maintainCount,
                                qOrderProduct.returnAmount,
                                qOrderProduct.tax,
                                qOrderProduct.taxRate,
                                qOrderProduct.priceType,
                                qOrderMaterial.skuId,
                                qOrderMaterial.productNo,
                                qOrderMaterial.name,
                                qOrderMaterial.category,
                                qOrderMaterial.brand,
                                qOrderMaterial.spec
                        )))));

        List<OrderAfterSalePageQueryResp> queryResult = new ArrayList<>(transform.values());
        findShopLogos(queryResult);

        return new PageDataResp<>(totalCount, queryResult);
    }

    /**
     * “代客售后” - 根据采购流程规则配置查询售后订单
     * @param vendorMemberId 供应会员Id（当前会员Id）
     * @param vendorRoleId   供应会员角色Id（当前会员角色Id）
     * @param pageVO         接口参数
     * @return               查询结果
     */
    private PageDataResp<OrderAfterSalePageQueryResp> pageAgentAfterSaleOrdersByPurchaseProcess(Long vendorMemberId, Long vendorRoleId, OrderAgentAfterSalePageDataReq pageVO) {
        QOrderDO qOrder = QOrderDO.orderDO;
        QOrderProductDO qOrderProduct = QOrderProductDO.orderProductDO;
        QOrderMaterialDO qOrderMaterial = QOrderMaterialDO.orderMaterialDO;
//        QOrderContractDO qOrderContract = QOrderContractDO.orderContractDO;
        QOrderPurchaseProcessDO qOrderPurchaseProcess = QOrderPurchaseProcessDO.orderPurchaseProcessDO;
        QOrderPurchaseProcessContractDO qOrderPurchaseProcessContract = QOrderPurchaseProcessContractDO.orderPurchaseProcessContractDO;

        //Step 1: 定义总数查询
        BooleanExpression joinOrderTradeProcessOn = Expressions.anyOf(qOrder.vendorMemberId.eq(qOrderPurchaseProcess.memberId).and(qOrder.vendorRoleId.eq(qOrderPurchaseProcess.roleId)), qOrderPurchaseProcess.memberId.eq(0L).and(qOrderPurchaseProcess.roleId.eq(0L)));
        JPAQuery<Long> countQuery = jpaQueryFactory.selectDistinct(qOrder.id).from(qOrder)
                .leftJoin(qOrderProduct).on(qOrder.id.eq(qOrderProduct.order.id))
                .leftJoin(qOrderMaterial).on(qOrderProduct.id.eq(qOrderMaterial.orderProduct.id))
//                .leftJoin(qOrderContract).on(qOrder.id.eq(qOrderContract.order.id))
                .leftJoin(qOrderPurchaseProcess).on(joinOrderTradeProcessOn)
                .leftJoin(qOrderPurchaseProcessContract).on(qOrderPurchaseProcess.id.eq(qOrderPurchaseProcessContract.process.id))
                .where(qOrder.orderType.eq(pageVO.getOrderType()));

        //Step 2: 固定的查询条件
        //采购会员、供应会员与订单类型
        countQuery.where(qOrder.buyerMemberId.eq(pageVO.getBuyerMemberId()).and(qOrder.buyerRoleId.eq(pageVO.getBuyerRoleId())).and(qOrder.vendorMemberId.eq(vendorMemberId)).and(qOrder.vendorRoleId.eq(vendorRoleId)));

        //订单状态：
        //换货：只显示已收到第一批货之后的订单且当前订单外部状态不是待确认支付结果，排除外部状态待支付
        //退货：只显示已发过第一批货之后、或已经支付成功过一次的订单且当前订单外部状态不是待确认支付结果，且订单状态不是已中止
        //维修：只显示已收到第一批货之后的订单
        if(pageVO.getAfterSalesType().equals(OrderTradeProcessTypeEnum.AFTER_SALES_EXCHANGES.getCode())) {
            countQuery.where(qOrder.hasReceived.isTrue()
                    .and(qOrder.outerStatus.ne(OrderOuterStatusEnum.TO_CONFIRM_PAYMENT.getCode()))
                    .and(qOrder.outerStatus.ne(OrderOuterStatusEnum.TO_PAY.getCode())));
        } else if(pageVO.getAfterSalesType().equals(OrderTradeProcessTypeEnum.AFTER_SALES_RETURNS.getCode())) {
            countQuery.where(qOrder.hasDelivered.isTrue().or(qOrder.hasPaid.isTrue()));
            countQuery.where(qOrder.outerStatus.ne(OrderOuterStatusEnum.TO_CONFIRM_PAYMENT.getCode())
                    .and(qOrder.outerStatus.ne(OrderOuterStatusEnum.TERMINATED.getCode()))
                    .and(qOrder.outerStatus.ne(OrderOuterStatusEnum.TO_CONFIRM_DELIVERY.getCode()))
                    .and(qOrder.outerStatus.ne(OrderOuterStatusEnum.TO_PAY.getCode())));
        } else {
            countQuery.where(qOrder.hasReceived.isTrue());
            countQuery.where(qOrder.outerStatus.ne(OrderOuterStatusEnum.TO_CONFIRM_DELIVERY.getCode())
                    .and(qOrder.outerStatus.ne(OrderOuterStatusEnum.TO_PAY.getCode())));

        }

        //Step 3: 动态的查询条件
        //合同编号
        if(StringUtils.hasLength(pageVO.getOrderNo())) {
            countQuery.where(qOrder.orderNo.like("%" + pageVO.getOrderNo().trim() + "%"));
        }

        //订单摘要
        if(StringUtils.hasLength(pageVO.getDigest())) {
            countQuery.where(qOrder.digest.like("%" + pageVO.getDigest().trim() + "%"));
        }

        //订单起始时间
        if(StringUtils.hasLength(pageVO.getStartDate())) {
            countQuery.where(qOrder.createTime.after(LocalDateTime.parse(pageVO.getStartDate().concat(" 00:00:00"), OrderConstant.DEFAULT_TIME_FORMATTER)));
        }

        //订单起始时间
        if(StringUtils.hasLength(pageVO.getEndDate())) {
            countQuery.where(qOrder.createTime.before(LocalDateTime.parse(pageVO.getEndDate().concat(" 23:59:59"), OrderConstant.DEFAULT_TIME_FORMATTER)));
        }

        //Step 4: 共有的查询条件（字表的查询条件）
        //只可选择已退货数量小于订单数量的订单商品
        countQuery.where(qOrderProduct.returnCount.lt(qOrderProduct.quantity));
        //流程类型、状态
        countQuery.where(qOrderPurchaseProcess.processType.eq(pageVO.getAfterSalesType()).and(qOrderPurchaseProcess.status.eq(EnableDisableStatusEnum.ENABLE.getCode())));
        //流程规则配置
//        countQuery.where(qOrderPurchaseProcess.allContracts.isTrue().or(qOrderPurchaseProcessContract.contractId.eq(qOrderContract.contractId)));

        //Step 5: 倒序排序、分页、总数
        long totalCount = countQuery.fetchCount();
        countQuery.orderBy(qOrder.id.desc());
        countQuery.limit(pageVO.getPageSize()).offset(pageVO.getCurrentOffset());

        //Step 6: 定义关联关系，OrderDO表必须要有distinct()
        JPAQuery<?> query = jpaQueryFactory.from(qOrder).distinct()
                .leftJoin(qOrderProduct).on(qOrder.id.eq(qOrderProduct.order.id))
                .leftJoin(qOrderMaterial).on(qOrderProduct.id.eq(qOrderMaterial.orderProduct.id))
//                .leftJoin(qOrderContract).on(qOrder.id.eq(qOrderContract.order.id))
                .leftJoin(qOrderPurchaseProcess).on(joinOrderTradeProcessOn)
                .leftJoin(qOrderPurchaseProcessContract).on(qOrderPurchaseProcess.id.eq(qOrderPurchaseProcessContract.process.id))
                .where(qOrder.id.in(countQuery.fetch()));

        //Step 7: 共有的查询条件
        //只可选择已退货数量小于订单数量的订单商品
        query.where(qOrderProduct.returnCount.lt(qOrderProduct.quantity));
        //流程类型、状态
        query.where(qOrderPurchaseProcess.processType.eq(pageVO.getAfterSalesType()).and(qOrderPurchaseProcess.status.eq(EnableDisableStatusEnum.ENABLE.getCode())));
        //流程规则配置
//        query.where(qOrderPurchaseProcess.allContracts.isTrue().or(qOrderPurchaseProcessContract.contractId.eq(qOrderContract.contractId)));

        //Step 8: 使用transform()对结果进行聚合统计，并通过Projections.Constructor到VO对象
        Map<Long, OrderAfterSalePageQueryResp> transform = query.transform(GroupBy.groupBy(qOrder.id).as(
                Projections.constructor(OrderAfterSalePageQueryResp.class, qOrder.shopId, qOrder.id, qOrder.orderNo, qOrder.digest, qOrder.vendorMemberId, qOrder.vendorRoleId, qOrder.vendorMemberName, qOrder.orderType, qOrder.createTime, qOrder.outerStatus,
                        GroupBy.list(Projections.constructor(OrderAfterSaleProductDetailResp.class,
                                qOrderProduct.id,
                                qOrder.orderType,
//                                qOrderPurchaseProcess.processKey,
                                qOrderProduct.skuId,
                                qOrderProduct.productNo,
                                qOrderProduct.logo,
                                qOrderProduct.name,
                                qOrderProduct.category,
                                qOrderProduct.brand,
                                qOrderProduct.unit,
                                qOrderProduct.spec,
                                qOrderProduct.quantity,
                                qOrderProduct.refPrice,
                                qOrderProduct.amount,
                                qOrderProduct.paidAmount,
                                qOrderProduct.exchangeCount,
                                qOrderProduct.returnCount,
                                qOrderProduct.maintainCount,
                                qOrderProduct.returnAmount,
                                qOrderProduct.tax,
                                qOrderProduct.taxRate,
                                qOrderProduct.priceType,
                                qOrderMaterial.skuId,
                                qOrderMaterial.productNo,
                                qOrderMaterial.name,
                                qOrderMaterial.category,
                                qOrderMaterial.brand,
                                qOrderMaterial.spec
                        )))));

        List<OrderAfterSalePageQueryResp> queryResult = new ArrayList<>(transform.values());
        findShopLogos(queryResult);

        return new PageDataResp<>(totalCount, queryResult);
    }

    /**
     * 从店铺模板服务查询供应商店铺、自营商城Logo
     * @param queryResult 查询结果
     */
    private void findShopLogos(List<OrderAfterSalePageQueryResp> queryResult) {
        if(CollectionUtils.isEmpty(queryResult)) {
            return;
        }

        Map<Long, List<OrderAfterSalePageQueryResp>> map = queryResult.stream().filter(orderAfterSalePageQueryResp -> Objects.nonNull(orderAfterSalePageQueryResp.getShopId()) && orderAfterSalePageQueryResp.getShopId() > 0).collect(Collectors.groupingBy(OrderAfterSalePageQueryResp::getShopId));
        for (Map.Entry<Long, List<OrderAfterSalePageQueryResp>> entry : map.entrySet()) {
            List<VendorLogoDTO> logoResult = platformTemplateFeignService.findVendorShopLogos(entry.getKey(), entry.getValue().stream().map(q -> new VendorBO(q.getVendorMemberId(), q.getVendorRoleId())).distinct().collect(Collectors.toList()));
            if(!CollectionUtils.isEmpty(logoResult)) {
                queryResult.stream().filter(query -> query.getShopId().equals(entry.getKey())).forEach(query -> logoResult.stream().filter(logo -> logo.getVendorMemberId().equals(query.getVendorMemberId()) && logo.getVendorRoleId().equals(query.getVendorRoleId())).findFirst().ifPresent(logo -> query.setLogo(logo.getLogo())));
            }
        }
    }

    /**
     * 采购能力 - 销售订单转请购单获取当前会员关联销售订单明细
     * @param loginUser 登录用户
     * @param pageVO 查询参数
     * @return 当前会员关联销售订单明细
     */
    @Override
    public PageDataResp<VendorOrderProductQueryResp> pageVendorOrderProductsByVendorOrderConvert(UserLoginCacheDTO loginUser, VendorOrderProductPageDataReq pageVO) {
        //是否只显示未请购采购订单
        Boolean  isNotRequisitioned = pageVO.getRequisitioned()!=null && !pageVO.getRequisitioned() ? pageVO.getRequisitioned() : null;

        return findVendorOrderProducts(pageVO.getCurrentOffset(),pageVO.getPageSize(),loginUser.getMemberId()
                ,pageVO.getOrderNo(),pageVO.getDigest(),pageVO.getOuterStatus(),pageVO.getBuyerMemberName(),isNotRequisitioned,
                pageVO.getStartTime(),pageVO.getEndTime(),null,null);
    }

    /**
     * 返回销售订单转请购单获取当前会员关联销售订单明细
     *
     * @param current         当前页数
     * @param pageSize        每页行数
     * @param memberId        供应商会员id
     * @param orderNo         订单号
     * @param digest          摘要
     * @param outerStatus     订单外部状态
     * @param buyerMemberName 采购会员名称
     * @param requisitioned   是否已请购
     * @param startTime       下单时间开始
     * @param endTime         下单时间结束
     * @param skuIdList       物料关联商品sku集合
     * @param inputSkuId      输入skuId
     * @return 当前会员关联销售订单明细
     */
    private PageDataResp<VendorOrderProductQueryResp> findVendorOrderProducts(Integer current, Integer pageSize, Long memberId,
                                                                              String orderNo, String digest, Integer outerStatus,
                                                                              String buyerMemberName, Boolean requisitioned, String startTime,
                                                                              String endTime, List<Long> skuIdList, Long inputSkuId) {
        QOrderDO qOrder = QOrderDO.orderDO;
        QOrderProductDO qOrderProduct = QOrderProductDO.orderProductDO;
        QOrderProductPositionDO qOrderProductPositionDO = QOrderProductPositionDO.orderProductPositionDO;
        //Step 1: 定义总数查询
        JPAQuery<Long> countQuery = jpaQueryFactory.select(qOrderProduct.id).from(qOrderProduct)
                .leftJoin(qOrder).on(qOrder.id.eq(qOrderProduct.order.id))
                .where(qOrder.vendorMemberId.eq(memberId).and(qOrder.orderType.in(OrderTypeEnum.INQUIRY_TO_PURCHASE.getCode(), OrderTypeEnum.SPOT_PURCHASING.getCode()))
                        .and(qOrder.outerStatus.notIn(OrderOuterStatusEnum.TO_SUBMIT.getCode(), OrderOuterStatusEnum.TO_CONFIRM.getCode(),
                                OrderOuterStatusEnum.NOT_ACCEPT.getCode(), OrderOuterStatusEnum.ACCOMPLISHED.getCode(),
                                OrderOuterStatusEnum.CANCELLED.getCode(), OrderOuterStatusEnum.TERMINATED.getCode())));

        //订单号
        if (StringUtils.hasLength(orderNo)) {
            countQuery.where(qOrder.orderNo.like("%" + orderNo.trim() + "%"));
        }

        //订单摘要
        if (StringUtils.hasLength(digest)) {
            countQuery.where(qOrder.digest.like("%" + digest.trim() + "%"));
        }

        //外部状态
        if (outerStatus != null) {
            countQuery.where(qOrder.outerStatus.eq(outerStatus));
        }

        //采购会员名称
        if (StringUtils.hasLength(buyerMemberName)) {
            countQuery.where(qOrder.buyerMemberName.like("%" + buyerMemberName.trim() + "%"));
        }

        //是否已请购
        if (requisitioned != null) {
            if (!requisitioned) {
                countQuery.where(qOrderProduct.requisitioned.eq(requisitioned).or(qOrderProduct.requisitioned.isNull()));
            } else {
                countQuery.where(qOrderProduct.requisitioned.eq(requisitioned));
            }
        }

        //下单时间开始
        if (StringUtils.hasLength(startTime)) {
            countQuery.where(qOrder.createTime.goe(LocalDateTime.parse(startTime.concat(" 00:00:00"), OrderConstant.DEFAULT_TIME_FORMATTER)));
        }

        //下单时间结束
        if (StringUtils.hasLength(endTime)) {
            countQuery.where(qOrder.createTime.loe(LocalDateTime.parse(endTime.concat(" 23:59:59"), OrderConstant.DEFAULT_TIME_FORMATTER)));
        }

        //只显示当前物料绑定的商品skuID
        if (!CollectionUtils.isEmpty(skuIdList)) {
            countQuery.where(qOrderProduct.skuId.in(skuIdList));
        }

        //输入skuId
        if (inputSkuId != null) {
            countQuery.where(qOrderProduct.skuId.eq(inputSkuId));
        }

        //Step 2: 倒序排序、分页、总数
        long totalCount = countQuery.fetchCount();
        countQuery.orderBy(qOrder.id.desc());
        countQuery.limit(pageSize).offset(current);
        List<Long> fetch = countQuery.fetch();

        List<VendorOrderProductQueryResp> vendorOrderProductQuery = getVendorOrderProductQuery(fetch, qOrder, qOrderProduct, qOrderProductPositionDO);

        return new PageDataResp<>(totalCount,vendorOrderProductQuery);
    }

    /**
     * 采购能力 - 添加请购单关联销售订单获取当前会员关联销售订单明细
     *
     * @param loginUser 登录用户
     * @param pageVO  查询参数
     * @return 当前会员关联销售订单明细
     */
    @Override
    public PageDataResp<VendorOrderProductQueryResp> pageVendorOrderProductsByVendorOrderAssociation(UserLoginCacheDTO loginUser, VendorOrderProductAssociationPageDataReq pageVO) {
        Boolean isNotRequisitioned = pageVO.getRequisitioned() != null && !pageVO.getRequisitioned() ? pageVO.getRequisitioned() : null;

        List<Long> skuIdList = new ArrayList<>();
        if (pageVO.getIsBind() && pageVO.getProductId() != null) {
            // 获取当前物料绑定商品sku集合
            WrapperResp<List<Long>> wrapperResp = commodityFeign.getSkuListByMaterielId(pageVO.getProductId());
            if (wrapperResp.getCode() == ResponseCodeEnum.SUCCESS.getCode()) {
                skuIdList= wrapperResp.getData();
                if (CollectionUtils.isEmpty(skuIdList)) {
                    return new PageDataResp<>(0L,new ArrayList<>());
                }
            }
        }

        return findVendorOrderProducts(pageVO.getCurrentOffset(), pageVO.getPageSize(), loginUser.getMemberId()
                , pageVO.getOrderNo(), pageVO.getDigest(), pageVO.getOuterStatus(), pageVO.getBuyerMemberName(), isNotRequisitioned,
                pageVO.getStartTime(), pageVO.getEndTime(), skuIdList, pageVO.getSkuId());
    }

    /**
     * 销售订单转请购单场景查询销售订单获取订单外部状态下拉框
     *
     * @param loginUser 登录用户
     * @return 下拉框列表
     */
    @Override
    public List<DropdownItemResp> getDropItemsByOuterStatus(UserLoginCacheDTO loginUser) {
        return OrderOuterStatusEnum.toDropdownList(Stream.of(OrderOuterStatusEnum.TO_CONFIRM_CONTRACT,
                OrderOuterStatusEnum.NOT_ACCEPT_CONTRACT, OrderOuterStatusEnum.TO_PAY, OrderOuterStatusEnum.TO_CONFIRM_PAYMENT,
                OrderOuterStatusEnum.PAYMENT_NOT_ACCOMPLISH, OrderOuterStatusEnum.TO_DELIVER, OrderOuterStatusEnum.TO_LOGISTICS,
                OrderOuterStatusEnum.TO_CONFIRM_DELIVERY, OrderOuterStatusEnum.TO_DEPOSIT, OrderOuterStatusEnum.TO_RECEIPT,
                OrderOuterStatusEnum.TO_CONFIRM_RECEIPT, OrderOuterStatusEnum.TO_ARCHIVE).collect(Collectors.toList()));
    }

    /**
     * 采购能力 - 请购单详情获取已关联的销售订单明细分页列表
     *
     * @param loginUser 登录用户
     * @param pageVO  分页参数列表
     * @return 已关联的销售订单明细分页列表
     */
    @Override
    public PageDataResp<VendorOrderProductQueryResp> vendorOrderPageByRequisitionDetail(UserLoginCacheDTO loginUser, VendorOrderProductRequisitionPageDataReq pageVO) {
        QOrderRequisitionProductDO qOrderRequisitionProduct = QOrderRequisitionProductDO.orderRequisitionProductDO;
        QOrderProductDO qOrderProduct = QOrderProductDO.orderProductDO;
        QOrderDO qOrder = QOrderDO.orderDO;
        QOrderProductPositionDO qOrderProductPositionDO=QOrderProductPositionDO.orderProductPositionDO;

        JPAQuery<Long> countQuery = jpaQueryFactory.selectDistinct(qOrderProduct.id).from(qOrderRequisitionProduct)
                .leftJoin(qOrderProduct).on(qOrderRequisitionProduct.product.id.eq(qOrderProduct.id))
                .leftJoin(qOrder).on(qOrderProduct.order.id.eq(qOrder.id))
                .where(qOrderRequisitionProduct.purchaseProductId.eq(pageVO.getPurchaseProductId())
                        .and(qOrder.vendorMemberId.eq(loginUser.getMemberId())));

        long totalCount = countQuery.fetchCount();

        countQuery.limit(pageVO.getPageSize()).offset(pageVO.getCurrentOffset());
        List<Long> fetch = countQuery.fetch();

        List<VendorOrderProductQueryResp> vendorOrderProductQuery = getVendorOrderProductQuery(fetch, qOrder, qOrderProduct, qOrderProductPositionDO);

        return new PageDataResp<>(totalCount, vendorOrderProductQuery);
    }

    /**
     * 根据创建时间查询订单数量
     * @param startTime      开始时间
     * @param endTime        结束时间
     */
    @Override
    public long countByCreateTimeBetween(LocalDateTime startTime, LocalDateTime endTime) {
        return orderRepository.countByCreateTimeBetween(startTime, endTime);
    }

    /**
     * 请购单详情获取已关联的销售订单明细分页列表条件组装
     *
     * @param fetch         对应订单明细id集合
     * @param qOrder        订单dsl对象
     * @param qOrderProduct 订单明细dsl对象
     * @return 条件
     */
    private List<VendorOrderProductQueryResp> getVendorOrderProductQuery(List<Long> fetch, QOrderDO qOrder, QOrderProductDO qOrderProduct, QOrderProductPositionDO qOrderProductPositionDO) {
        JPAQuery<OrderProductDO> query = jpaQueryFactory.select(qOrderProduct)
                .from(qOrderProduct)
                .leftJoin(qOrder).on(qOrderProduct.order.id.eq(qOrder.id))
                .leftJoin(qOrderProductPositionDO).on(qOrderProductPositionDO.product.id.eq(qOrderProduct.id))
                .where(qOrderProduct.id.in(fetch))
                .orderBy(qOrder.id.desc());
        return new ArrayList<>(query.transform(GroupBy.groupBy(qOrderProduct.id).as(
                Projections.constructor(VendorOrderProductQueryResp.class,
                        qOrderProduct.id,
                        qOrder.id,
                        qOrder.orderNo,
                        qOrder.buyerMemberName,
                        qOrder.createTime,
                        qOrder.digest,
                        qOrderProduct.productId,
                        qOrderProduct.name,
                        qOrderProduct.price,
                        qOrderProduct.quantity,
                        qOrderProduct.amount,
                        qOrder.outerStatus,
                        qOrderProduct.requisitioned,
                        qOrderProduct.skuId,
                        GroupBy.list(Projections.constructor(OrderProductPositionResp.class,
                                qOrderProductPositionDO.positionId,
                                qOrderProductPositionDO.positionName,
                                qOrderProductPositionDO.positionQuantity,
                                qOrderProductPositionDO.warehouseId,
                                qOrderProductPositionDO.warehouseName
                        ))))).values());
    }

    /**
     * 获取订单合同待签署链接
     * @param sysUser       登录用户
     * @param commonIdReq   待签署订单id
     */
    @Override
    public String getSignUrl(UserLoginCacheDTO sysUser, CommonIdReq commonIdReq) {
        OrderDO orderDO = orderRepository.findById(commonIdReq.getId()).orElse(null);
        if(Objects.isNull(orderDO)){
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }
        if(!(sysUser.getMemberId().equals(orderDO.getVendorMemberId()) && sysUser.getMemberRoleId().equals(orderDO.getVendorRoleId()))){
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        if(!OrderContractTypeEnum.ELECTRONIC_CONTRACT.getCode().equals(orderDO.getOrderContractType())){
            throw new BusinessException(ResponseCodeEnum.ORDER_CONTRACT_TYPE_NOT_ELECTRONIC);
        }

        OrderTradeProcessBO b2BOrderProcess = baseOrderProcessService.findB2BOrderProcess(Collections.singletonList(orderDO), OrderTradeProcessTypeEnum.ORDER_TRADE);
        if(Objects.isNull(b2BOrderProcess)){
            throw new BusinessException(ResponseCodeEnum.ORDER_TRADE_PROCESS_DOES_NOT_EXIST);
        }

        return contractFeignService.getSignUrl(getContractSignReq(sysUser, orderDO, b2BOrderProcess.getContractTemplateId()));
    }

    /**
     * 组装请求参数
     * @param orderDO               订单
     * @param contractTemplateId    合同模板id
     */
    private ContractSignReq getContractSignReq(UserLoginCacheDTO sysUser, OrderDO orderDO, Long contractTemplateId){
        ContractSignReq contractSignReq = new ContractSignReq();
        contractSignReq.setContractTemplateId(contractTemplateId);
        contractSignReq.setLoginMemberId(sysUser.getMemberId());
        contractSignReq.setLoginMemberRoleId(sysUser.getMemberRoleId());
        contractSignReq.setVendorMemberId(orderDO.getVendorMemberId());
        contractSignReq.setVendorMemberRoleId(orderDO.getVendorRoleId());
        contractSignReq.setBuyerMemberId(orderDO.getBuyerMemberId());
        contractSignReq.setBuyerMemberRoleId(orderDO.getBuyerRoleId());

        //订单信息
        ContractOrderReq contractOrderReq = new ContractOrderReq();
        contractOrderReq.setCurrencyName(CurrencyTypeEnum.getMessage(orderDO.getCurrencyType()));
        contractOrderReq.setOrderId(orderDO.getId());
        contractOrderReq.setFreightAmount(orderDO.getFreight());
        contractOrderReq.setFreightAmountWord(MoneyUtil.convert(orderDO.getFreight()));
        contractOrderReq.setProductAmount(orderDO.getProductAmount());
        contractOrderReq.setProductAmountWord(MoneyUtil.convert(orderDO.getProductAmount()));
        contractOrderReq.setTotalAmount(orderDO.getTotalAmount());
        contractOrderReq.setTotalAmountWord(MoneyUtil.convert(orderDO.getTotalAmount()));

        //订单商品信息
        Set<OrderProductDO> orderProductDOSet = orderDO.getProducts();
        List<ContractOrderProductReq> orderProductReqList = orderProductDOSet.stream().map(orderProductDO -> {
            ContractOrderProductReq contractOrderProductReq = new ContractOrderProductReq();
            contractOrderProductReq.setName(orderProductDO.getName());
            contractOrderProductReq.setCode(orderProductDO.getProductNo());
            contractOrderProductReq.setBrand(orderProductDO.getBrand());
            contractOrderProductReq.setModel(orderProductDO.getSpec());
            contractOrderProductReq.setCount(orderProductDO.getQuantity());
            contractOrderProductReq.setPrice(Objects.nonNull(orderProductDO.getRefPrice()) ? orderProductDO.getRefPrice() : orderProductDO.getPrice());
            contractOrderProductReq.setTotalAmount(orderProductDO.getAmount());
            contractOrderProductReq.setRemark(orderProductDO.getRemark());
            contractOrderProductReq.setMaterielName(Objects.nonNull(orderProductDO.getMaterial()) ? orderProductDO.getMaterial().getName() : null);
            return contractOrderProductReq;
        }).collect(Collectors.toList());
        contractOrderReq.setOrderProductList(orderProductReqList);

        contractSignReq.setContractOrder(contractOrderReq);
        return contractSignReq;
    }


    @Override
    public PayOrderDetailResp getOrderPayInfo(UserLoginCacheDTO sysUser, GetPayOrderInfoReq getPayOrderInfoReq) {
        List<OrderDO> orderDOS = orderRepository.findAllById(getPayOrderInfoReq.getOrderIds());
        if(CollectionUtils.isEmpty(orderDOS)){
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }
        Integer batchNo = getPayOrderInfoReq.getBatchNo();
        GoldPriceResp goldPriceResp = eosApiService.getGoldPrice();
        BigDecimal goldPrice = goldPriceResp.getJj();
        //获取过期时间最短的订单(过滤器订货单的尾款支付)
        List<OrderPaymentDO> orderPaymentDOS = orderPaymentDORepository.findByOrderIn(orderDOS);
        orderPaymentDOS = orderPaymentDOS.stream().filter(orderPaymentDO -> batchNo.equals(orderPaymentDO.getBatchNo())).collect(Collectors.toList());
        //总共需要支付的其他费用(如果是定货订单，这里是支付金额金额)
        BigDecimal sumPayOtherAmount = BigDecimal.ZERO;
        //未支付的其他总费用(如果是定货订单，这里是支付金额金额)
        BigDecimal unPaysumPayOtherAmount = BigDecimal.ZERO;
        //总共需要支付的金料
        BigDecimal sumGoldPayWeight = BigDecimal.ZERO;
        //总共未支付的金料
        BigDecimal unPaysumGoldPayWeight = BigDecimal.ZERO;
        //BigDecimal sumGoleWeight = BigDecimal.ZERO;
        for (OrderPaymentDO orderPaymentDO : orderPaymentDOS) {
            Integer saleMode = orderPaymentDO.getOrder().getSaleMode();
            if(saleMode != null && CommoditySaleModeEnum.ORDER.getCode().equals(saleMode) && batchNo == 1){
                //计算定金
                if(DepositTypeEnum.FIXED_AMOUNT.getCode().equals(orderPaymentDO.getDepositType())){
                    sumPayOtherAmount = BigDecimalUtil.add(sumPayOtherAmount, orderPaymentDO.getAdvancePayAmount());
                }else{
                    BigDecimal goldFee = orderPaymentDO.getTotalWeight().multiply(goldPrice);
                    BigDecimal payRate = orderPaymentDO.getPayRate().divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
                    BigDecimal payAmount = goldFee.multiply(payRate);
                    sumPayOtherAmount = BigDecimalUtil.add(sumPayOtherAmount, payAmount);
                    //sumGoleWeight = BigDecimalUtil.add(sumGoleWeight, orderPaymentDO.getTotalWeight());
                }
            }else{
                //BigDecimal goldFee = orderPaymentDO.getTotalWeight().multiply(goldPrice);
                sumPayOtherAmount = BigDecimalUtil.add(sumPayOtherAmount, orderPaymentDO.getOtherFee());
                sumPayOtherAmount = BigDecimalUtil.add(sumPayOtherAmount, orderPaymentDO.getLaborFee());
                sumGoldPayWeight = BigDecimalUtil.add(sumGoldPayWeight, orderPaymentDO.getTotalWeight());
            }
        }
        unPaysumPayOtherAmount = sumPayOtherAmount;
        unPaysumGoldPayWeight = sumGoldPayWeight;

        PayOrderDetailResp payOrderInfoResp = new PayOrderDetailResp();
        //获取账号信息
        Set<String> orderPaymentIds = orderPaymentDOS.stream().map(orderPaymentDO -> {return orderPaymentDO.getId().toString();}).collect(Collectors.toSet());
        GetEosAccountBalanceAndFrozenAccountBalanceReq eosAccountBalanceReq = new GetEosAccountBalanceAndFrozenAccountBalanceReq();
        eosAccountBalanceReq.setMemberId(orderDOS.get(0).getBuyerMemberId());
        eosAccountBalanceReq.setPaymentIds(orderPaymentIds);
        WrapperResp<MemberAssetAccountResp> accountFeignEosAccountBalance = assetAccountFeign.getEosAccountBalanceAndFrozenAccountBalance(eosAccountBalanceReq);
        MemberAssetAccountResp memberAssetAccountResp = WrapperUtil.getDataOrThrow(accountFeignEosAccountBalance);
        payOrderInfoResp.setNumber(orderDOS.stream().map(orderDO -> orderDO.getProducts().size()).reduce(Integer.valueOf(0), Integer::sum));
        payOrderInfoResp.setGoldWeight(orderDOS.stream().map(OrderDO::getTotalWeight).reduce(BigDecimal.ZERO, BigDecimal::add));
        BigDecimal currentUsedGoleWeight = BigDecimal.ZERO;
        if(getPayOrderInfoReq.getPayChannels().contains(6) && !BigDecimalUtil.isZeroOrNegative(unPaysumGoldPayWeight)
                && !BigDecimalUtil.isZeroOrNegative(memberAssetAccountResp.getMaterialStockAndCredit())){
            currentUsedGoleWeight = BigDecimalUtil.subtract(memberAssetAccountResp.getMaterialStockAndCredit(), unPaysumGoldPayWeight);
            //如果结果为负数（则当前支付金额 = 钱包存料，否则就是）
            currentUsedGoleWeight = BigDecimalUtil.isNegative(currentUsedGoleWeight) ? memberAssetAccountResp.getMaterialStockAndCredit() : unPaysumGoldPayWeight;
            //重新计算未支付的金料
            unPaysumGoldPayWeight = BigDecimalUtil.subtract(unPaysumGoldPayWeight, currentUsedGoleWeight);
        }

        if(getPayOrderInfoReq.getPayChannels().contains(4) && !BigDecimalUtil.isZeroOrNegative(unPaysumPayOtherAmount) && !BigDecimalUtil.isZeroOrNegative(memberAssetAccountResp.getUnUsedAccountBalanceCash())){
            BigDecimal unUsedAccountBalanceCash = BigDecimalUtil.subtract(memberAssetAccountResp.getUnUsedAccountBalanceCash(), unPaysumPayOtherAmount);
            BigDecimal currentPayOtherAmount = BigDecimalUtil.isNegative(unUsedAccountBalanceCash) ? memberAssetAccountResp.getUnUsedAccountBalanceCash() : unPaysumPayOtherAmount;
            unPaysumPayOtherAmount = BigDecimalUtil.subtract(unPaysumPayOtherAmount, currentPayOtherAmount);
            BigDecimal cureentPayGoleAmount = BigDecimal.ZERO;
            if(!BigDecimalUtil.isZeroOrNegative(unPaysumGoldPayWeight) && !BigDecimalUtil.isNegative(unUsedAccountBalanceCash)){
                //将未支付的黄金转金额
                BigDecimal unPayGoleWeightAmount = BigDecimalUtil.multiply(goldPrice, unPaysumGoldPayWeight);
                cureentPayGoleAmount = BigDecimalUtil.subtract(unUsedAccountBalanceCash, unPayGoleWeightAmount);
                cureentPayGoleAmount = BigDecimalUtil.isNegative(cureentPayGoleAmount) ? unUsedAccountBalanceCash : unPayGoleWeightAmount;
                unPayGoleWeightAmount = BigDecimalUtil.subtract(unPayGoleWeightAmount, cureentPayGoleAmount);
                unPaysumGoldPayWeight = unPayGoleWeightAmount.divide(goldPrice, 2, RoundingMode.HALF_UP);
                payOrderInfoResp.setAmountPayGoleWeight(cureentPayGoleAmount.divide(goldPrice, 2, RoundingMode.HALF_UP));
            }
            payOrderInfoResp.setCurrentUsedAmount(BigDecimalUtil.add(currentPayOtherAmount, cureentPayGoleAmount));
        }
        payOrderInfoResp.setTotalQuota(memberAssetAccountResp.getTotalAmount());
        if(BigDecimalUtil.isZeroOrNegative(memberAssetAccountResp.getMaterialStock())){
            payOrderInfoResp.setUsedCredit(memberAssetAccountResp.getMaterialStock());
        }
        payOrderInfoResp.setCurrentTotalQuota(BigDecimalUtil.add(payOrderInfoResp.getCurrentUsedCredit(), payOrderInfoResp.getAmountPayGoleWeight()));
        payOrderInfoResp.setRemainingPayableGoleWeight(unPaysumGoldPayWeight);
        //payOrderInfoResp.setRemainingSumPayableAmount(BigDecimalUtil.add(unPaySumPayOtherAmount, currentPayAmount));
        payOrderInfoResp.setCurrentUsedCredit(currentUsedGoleWeight);
        payOrderInfoResp.setRemainingPayableAmount(unPaysumPayOtherAmount);
        payOrderInfoResp.setRemainingSumPayableAmount(BigDecimalUtil.add(BigDecimalUtil.multiply(unPaysumGoldPayWeight, goldPrice), unPaysumPayOtherAmount));
        payOrderInfoResp.setAmountPayable(sumPayOtherAmount);
        payOrderInfoResp.setGolePayable(sumGoldPayWeight);
        payOrderInfoResp.setUsedAmount(memberAssetAccountResp.getLockBalance());
        OrderDepositResp orderDepositConfig = orderParamConfigService.getOrderDepositConfig();
        LocalDateTime minPayTimeOut = orderDOS.stream().map(OrderDO::getPayTimeout).filter(time -> time != null && getPayOrderInfoReq.getBatchNo() == 1).min(Comparator.comparing(LocalDateTime::toLocalTime)).orElse(null);
        payOrderInfoResp.setPayTimeout(minPayTimeOut);
        if(orderDepositConfig != null){
            payOrderInfoResp.setServiceCharge(orderDepositConfig.getServiceCharge());
        }
        return payOrderInfoResp;
    }
}
