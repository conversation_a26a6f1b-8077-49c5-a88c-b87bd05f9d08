package com.ssy.lingxi.order.model.resp.mobile;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 订单商品优惠信息
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/24
 */
@Getter
@Setter
@NoArgsConstructor
public class OrderLaborCostsResp {

    /**
     * 工费名称
     */
    private String laborName;

    /**
     * 原工费
     */
    private BigDecimal originalLaborCost;

    /**
     * 优惠后的工费
     */
    private BigDecimal discountLaborCost;
}
