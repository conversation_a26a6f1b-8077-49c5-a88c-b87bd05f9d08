package com.ssy.lingxi.order.enums;

import com.ssy.lingxi.common.model.resp.select.DropdownItemResp;
import com.ssy.lingxi.component.base.language.LanguageHolder;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 送货相关外部状态枚举
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-16
 */
public enum DeliveryOrderTypeStatusEnum {

    B2B(1, "b2b订单"),

    SRM(2, "srm订单"),

    UNKNOWN(3, "未知");

    DeliveryOrderTypeStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 外部状态枚举值
     */
    private final Integer code;
    private final String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return LanguageHolder.getTranslation(this.getClass(), this.name, this.code);
    }

    /**
     * 根据外部状态枚举值获得外部状态名称
     * @param code 外部状态枚举值
     * @return 外部状态名称
     */
    public static String getNameByCode(Integer code) {
        DeliveryOrderTypeStatusEnum deliveryOrderTypeStatusEnum = Arrays.stream(DeliveryOrderTypeStatusEnum.values()).filter(f -> Objects.equals(f.getCode(), code)).findFirst().orElse(null);
        return Objects.nonNull(deliveryOrderTypeStatusEnum) ? deliveryOrderTypeStatusEnum.getName() : null;
    }

    /**
     * 获得状态列表
     * @return 前端下拉框内容
     */
    public static List<DropdownItemResp> toDropdownList() {
        return Arrays.stream(DeliveryOrderTypeStatusEnum.values()).map(e -> new DropdownItemResp(e.getCode(), DeliveryOrderTypeStatusEnum.getNameByCode(e.getCode()))).collect(Collectors.toList());
    }
}
