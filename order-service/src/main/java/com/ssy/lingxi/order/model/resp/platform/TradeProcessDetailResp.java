package com.ssy.lingxi.order.model.resp.platform;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * 平台后台 - 查询交易、采购规则详情返回
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-28
 */
@Getter
@Setter
@NoArgsConstructor
public class TradeProcessDetailResp implements Serializable {
    private static final long serialVersionUID = 8078674239591924106L;

    /**
     * 流程规则Id
     */
    private Long processId;

    /**
     * 流程规则名称
     */
    private String name;

    /**
     * 状态，0-停用，1-启用
     */
    private Integer status;

    /**
     * 状态名称
     */
    private String statusName;

    /**
     * 基础流程Id
     */
    private Long baseProcessId;

    /**
     * 是否适用所有会员，true-是，false-否
     */
    private Boolean allMembers;

    /**
     * 是否默认 0.否 1.是
     */
    private Integer isDefault;

    /**
     * 基础流程
     */
    private PlatformBaseTradeProcessResp baseProcess;
}
