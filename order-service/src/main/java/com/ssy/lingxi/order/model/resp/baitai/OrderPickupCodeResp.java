package com.ssy.lingxi.order.model.resp.baitai;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/27
 */
@Data
public class OrderPickupCodeResp implements Serializable {
    private static final long serialVersionUID = 3967124008494366505L;

    /**
     * 动态验证码
     */
    private String dynamicCode;

    /**
     * 验证码过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expireTime;
}
