package com.ssy.lingxi.order.model.resp.baitai;

import lombok.Data;

import java.util.List;

@Data
public class MsgData {
    /**
     * 顺丰订单路由信息
     */
    private List<RouteResp> routeResps;

    @Data
    public static class RouteResp {
        /**
         * 订单号
         */
        private String mailNo;

        /**
         * 路由信息
         */
        private List<Route> routes;

        @Data
        public static class Route {
            /**
             * 路由节点发生的地点
             */
            private String acceptAddress;
            /**
             * 路由节点发生的时间，格式：YYYY-MM-
             * DD HH24:MM:SS，示例：2012-7-30
             * 09:30:00
             */
            private String acceptTime;
            /**
             * 物流状态描述
             */
            private String remark;
            /**
             * 操作码
             */
            private String opCode;
        }
    }
}