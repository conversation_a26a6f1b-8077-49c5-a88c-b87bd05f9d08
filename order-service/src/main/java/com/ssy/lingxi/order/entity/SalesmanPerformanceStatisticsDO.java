package com.ssy.lingxi.order.entity;

import com.ssy.lingxi.common.constant.TableNameConstant;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 业务员业绩统计实体类
 * <AUTHOR>
 * @version v2-0418
 * @since 2022-03-28
 */
@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_ORDER_SERVICE + "salesman_performance_statistics")
public class SalesmanPerformanceStatisticsDO {
    /**
     * 主键Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 业务员Id
     */
    @Column
    private Long memberUserId;

    /**
     * 统计月份
     */
    @Column(columnDefinition = "timestamp")
    private LocalDate monthStatistical;

    /**
     * 订单下单金额
     */
    @Column(columnDefinition = "numeric")
    private BigDecimal amountPayable;

    /**
     * 下单会员数
     */
    @Column(columnDefinition = "numeric")
    private Long memberCount;

    /**
     * 订单数量
     */
    @Column(columnDefinition = "numeric")
    private Long orderCount;

    /**
     * 品类数量
     */
    @Column(columnDefinition = "numeric")
    private Long categoryCount;

    /**
     * 已收款总金额
     */
    @Column(columnDefinition = "numeric")
    private BigDecimal amountPaid;

    /**
     * 商品数量
     */
    @Column(columnDefinition = "numeric")
    private Long commodityCount;

    /**
     * 售后退款额
     */
    @Column(columnDefinition = "numeric")
    private BigDecimal refundAmount;

    @Override
    public String toString() {
        return "SalesmanPerformanceStatisticsDO{" +
                "id=" + id +
                ", memberUserId=" + memberUserId +
                ", monthStatistical=" + monthStatistical +
                ", amountPayable=" + amountPayable +
                ", memberCount=" + memberCount +
                ", orderCount=" + orderCount +
                ", categoryCount=" + categoryCount +
                ", amountPaid=" + amountPaid +
                ", commodityCount=" + commodityCount +
                ", refundAmount=" + refundAmount +
                '}';
    }
}
