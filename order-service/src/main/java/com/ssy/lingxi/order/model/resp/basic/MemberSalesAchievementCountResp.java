package com.ssy.lingxi.order.model.resp.basic;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 业务员业绩统计
 * <AUTHOR>
 * @version 2.02.18
 * @since 2022-03-14
 */
@Getter
@Setter
@NoArgsConstructor
public class MemberSalesAchievementCountResp {

    /**
     * 所属机构
     */
    private String title;

    /**
     * 业务员姓名
     */
    private String name;

    /**
     * 业务员id
     */
    private Long userId;

    /**
     * 下级会员数
     */
    private Integer memberCount;

    /**
     * 下单次数
     */
    private Integer orderCount;

    /**
     * 订单应付金额
     */
    private BigDecimal amountPayable;

    /**
     * 订单已付金额
     */
    private BigDecimal amountPaid;

    /**
     * 售后退款金额
     */
    private BigDecimal refundAmount;

    @Override
    public String toString() {
        return "MemberSalesAchievementCountResp{" +
                "title='" + title + '\'' +
                ", name='" + name + '\'' +
                ", userId=" + userId +
                ", memberCount=" + memberCount +
                ", orderCount=" + orderCount +
                ", amountPayable=" + amountPayable +
                ", amountPaid=" + amountPaid +
                ", refundAmount=" + refundAmount +
                '}';
    }
}
