package com.ssy.lingxi.order.model.resp.basic;

import com.ssy.lingxi.common.model.resp.select.DropdownItemResp;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 订单审核各个页面，获取查询条件接口返回
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-19
 */
@Getter
@Setter
@NoArgsConstructor
public class ValidatePageItemResp implements Serializable {
    private static final long serialVersionUID = -1251050260260287904L;

    /**
     * 订单类型
     */
    private List<DropdownItemResp> orderTypes;
}
