//package com.ssy.lingxi.order.service.base;
//
//import com.ssy.lingxi.order.entity.OrderTradeProcessDO;
//import com.ssy.lingxi.order.model.req.process.OrderTradeProcessContractReq;
//import com.ssy.lingxi.order.model.resp.process.OrderTradeProcessContractDetailResp;
//
///**
// * 交易规则关联的合同相关接口
// * <AUTHOR>
// * @version 2.0.0
// * @since 2021-07-24
// */
//public interface IBaseOrderTradeProcessContractService {
//
//    /**
//     * 新增、修改交易规则配置时，校验合同信息
//     * @param hasContract    是否使用合同
//     * @param contractVO     合同信息
//     */
//    void checkContract(Boolean hasContract, OrderTradeProcessContractReq contractVO);
//
//    /**
//     * 保存交易规则关联的合同信息，调用方要保存OrderTradeProcessDO
//     * @param tradeProcess 交易流程规则
//     * @param hasContract 是否使用电子合同
//     * @param contractVO  电子合同信息
//     */
//    void saveContract(OrderTradeProcessDO tradeProcess, Boolean hasContract, OrderTradeProcessContractReq contractVO);
//
//    /**
//     * 查询交易流程规则关联的合同信息
//     * @param tradeProcess 交易流程规则
//     * @return 合同信息
//     */
//    OrderTradeProcessContractDetailResp getContract(OrderTradeProcessDO tradeProcess);
//
//    /**
//     * 更新交易规则关联的合同信息，调用方要保存OrderTradeProcessDO
//     * @param tradeProcess 交易流程规则
//     * @param hasContract 是否使用电子合同
//     * @param contractVO  电子合同信息
//     */
//    void updateContract(OrderTradeProcessDO tradeProcess, Boolean hasContract, OrderTradeProcessContractReq contractVO);
//}
