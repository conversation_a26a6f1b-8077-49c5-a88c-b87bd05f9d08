package com.ssy.lingxi.order.model.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * 订单支付结果缓存
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-08-19
 */
@Getter
@Setter
@NoArgsConstructor
public class PayResultDTO implements Serializable {
    private static final long serialVersionUID = 1471070422168590690L;

    public PayResultDTO(Boolean paySuccess) {
        this.paySuccess = paySuccess;
    }

    /**
     * 支付是否成功
     */
    private Boolean paySuccess;
}
