package com.ssy.lingxi.order.controller.platform;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.order.api.model.req.OrderScoreConfigUpdateReq;
import com.ssy.lingxi.order.api.model.resp.OrderScoreConfigDetailResp;
import com.ssy.lingxi.order.service.web.IOrderParamConfigService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 平台后台-业务参数配置-积分相关
 * <AUTHOR>
 * @since 2022/2/22
 * @version 2.0.0
 */
@RestController
@RequestMapping(ServiceModuleConstant.ORDER_PATH_PREFIX + "/platform/param/config")
public class PlatformOrderParamConfigController extends BaseController {
    @Resource
    private IOrderParamConfigService orderConfigService;


    /**
     * 查看-积分抵扣订单
     * @return 查询结果
     */
    @GetMapping("/score/get")
    public WrapperResp<OrderScoreConfigDetailResp> getScoreConfig() {
        return WrapperUtil.success(orderConfigService.getScoreConfig(getPlatformUser()));
    }

    /**
     * 添加/修改-积分抵扣订单
     * @param updateVO 接口参数
     * @return 修改结果
     */
    @PostMapping("/score/update")
    public WrapperResp<Void> updateScoreConfig(@RequestBody @Valid OrderScoreConfigUpdateReq updateVO) {
        return WrapperUtil.success(orderConfigService.updateScoreConfig(getPlatformUser(), updateVO));
    }
}
