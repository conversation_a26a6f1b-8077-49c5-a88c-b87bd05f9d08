package com.ssy.lingxi.order.service.platform;

import com.ssy.lingxi.order.api.model.resp.PlatformSettlementTypeResp;

import java.util.List;

/**
 * 平台后台 - 结算支付方式相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-12-9
 */
public interface IPlatformSettlementTypeService {

    /**
     * 获取平台列表
     */
    List<PlatformSettlementTypeResp> findSettlementTypeByEnable();

    /**
     * 获取平台启用的结算方式[账期、月结]
     */
    List<PlatformSettlementTypeResp> findSettlementCategoryByEnable();
}
