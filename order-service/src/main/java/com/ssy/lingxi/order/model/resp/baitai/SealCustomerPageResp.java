package com.ssy.lingxi.order.model.resp.baitai;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * 字印客户信息
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/20
 */
@Getter
@Setter
@NoArgsConstructor
public class SealCustomerPageResp implements Serializable {

    private static final long serialVersionUID = -1531382356844018932L;

    /**
     * 主键Id
     */
    private Long id;

    /**
     * 会员id
     */
    private Long subMemberId;

    /**
     * 角色id
     */
    private Long roleId;

    /**
     * 会员名称
     */
    private String name;

    /**
     * 会员角色名称
     */
    private String roleName;

    /**
     * 客户类型
     */
    private Integer customerType;

    /**
     * 客户类型名称
     */
    private String customerTypeName;
}
