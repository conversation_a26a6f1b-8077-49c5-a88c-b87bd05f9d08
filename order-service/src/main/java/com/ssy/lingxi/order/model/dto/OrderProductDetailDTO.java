package com.ssy.lingxi.order.model.dto;

import com.ssy.lingxi.common.enums.product.UnitVisaTypeEnum;
import com.ssy.lingxi.order.model.req.basic.OrderPromotionReq;
import com.ssy.lingxi.product.api.model.resp.warehouse.FreightSpaceSingleProductExtendResp;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 从多个服务查询订单商品信息返回
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-10-27
 */
@Getter
@Setter
public class OrderProductDetailDTO implements Serializable {
    private static final long serialVersionUID = 5753616442774698472L;

    public OrderProductDetailDTO() {
        this.buyerMemberId = 0L;
        this.buyerRoleId = 0L;
        this.vendorMemberId = 0L;
        this.vendorRoleId = 0L;
        this.price = BigDecimal.ZERO;
        this.refPrice = BigDecimal.ZERO;
        this.address = "";
        this.receiver = "";
        this.phone = "";
        this.promotions = new ArrayList<>();
    }

    /**
     * 采购会员Id
     */
    private Long buyerMemberId;

    /**
     * 采购会员角色Id
     */
    private Long buyerRoleId;

    /**
     * 供应会员Id
     */
    private Long vendorMemberId;

    /**
     * 供应会员角色Id
     */
    private Long vendorRoleId;

    /**
     * 商品Id
     */
    private Long productId;

    /**
     * 商品SkuId
     */
    private Long skuId;

    /**
     * 营销活动类型，定义在 OrderPromotionTypeEnum 中
     */
    private Integer promotionType;

    /**
     * 套餐主商品，关联的套餐号
     */
    private Integer groupNo;

    /**
     * 被换购商品，关联的商品SkuId
     */
    private Long parentSkuId;

    /**
     * 购买数量
     */
    private BigDecimal quantity;

    /**
     * 是否上架
     */
    private Boolean published;

    /**
     * 渠道商品库存Id
     */
    private Long stockId;

    /**
     * 购物车Id
     */
    private Long cartId;

    /**
     * 商品价格类型，1-现货价格，2-询价价格，3-积分兑换，4-赠品
     */
    private Integer priceType = 1;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 商品图片
     */
    private String logo;

    /**
     * 商品品类
     */
    private String category;

    /**
     * 商品品牌
     */
    private String brand;

    /**
     * 计价单位
     */
    private String unit;

    /**
     * 商品规格
     */
    private String spec;

    /**
     * 最小起订数
     */
    private BigDecimal minOrder;

    /**
     * 是否允许会员折扣
     */
    private Boolean discountable;

    /**
     * 折扣
     */
    private BigDecimal discount;

    /**
     * 商品阶梯价
     */
    private Map<String,BigDecimal> priceMap;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 到手价
     */
    private BigDecimal refPrice;

    /**
     * 供方库存
     */
    private BigDecimal stock;

    /**
     * 是否含税（true-含税，false-不含税）
     */
    private Boolean tax;

    /**
     * 税率（百分比的分子部分）
     */
    private BigDecimal taxRate;

    /**
     * 商品配送方式：1-物流，2-自提，3-无需配送
     */
    private Integer deliveryType;

    /**
     * 运费类型，1-卖家承担，2-买家承担
     */
    private Integer freightType;

    /**
     * 商品重量，当配送方式是物流时要非空且大于0
     */
    private BigDecimal weight;

    /**
     * 物流模板Id，当配送方式是物流时要非空且大于0
     */
    private Long logisticsTemplateId;

    /**
     * 发货地址id
     */
    private Long consigneeId;

    /**
     * 自提地址（如配送方式为自提，必填）
     */
    private String address;

    /**
     * 接收人（如配送方式为自提，必填）
     */
    private String receiver;

    /**
     * 接收人电话（如配送方式为自提，必填）
     */
    private String phone;

    /**
     * 是否跨境商品
     */
    private Boolean isCrossBorder;

    /**
     * 店铺ID (通过收货地址获取)
     */
    private Long storeId;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 店铺logo
     */
    private String storeLogo;

    /**
     * 商品关联的营销活动列表，如商品没有营销活动则不需要填写此字段
     */
    private List<OrderPromotionReq> promotions;

    /**
     * 单件id
     */
    private Long singleId;

    /**
     * 商品单件编码
     */
    private String commoditySingleCode;

    /**
     * 基础工费
     */
    private BigDecimal baseLaborCosts;

    /**
     * 附加工费
     */
    private BigDecimal additionalLaborCosts;

    /**
     * 件工费
     */
    private BigDecimal pieceLaborCosts;

    /**
     * 净重
     */
    private BigDecimal netWeight;

    /**
     * new 订单商品定金
     */
    private BigDecimal deposit;

    /**
     * 挂签价格
     */
    private BigDecimal visaPrice = BigDecimal.ZERO;

    /**
     * 计价单位 1 克，2件
     *
     * @see UnitVisaTypeEnum
     */
    private Integer unitVisaType;

    /**
     * 其它工费
     */
    private List<FreightSpaceSingleProductExtendDTO> freightSpaceSingleProductExtendList;

    /**
     * new 是否镶嵌
     */
    private Boolean isInlay = Boolean.FALSE;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }

        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        OrderProductDetailDTO that = (OrderProductDetailDTO) o;
        return Objects.equals(skuId, that.skuId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(skuId);
    }
}
