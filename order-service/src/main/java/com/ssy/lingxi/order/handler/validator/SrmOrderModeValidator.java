package com.ssy.lingxi.order.handler.validator;

import com.ssy.lingxi.component.base.enums.order.OrderModeEnum;
import com.ssy.lingxi.order.handler.annotation.SrmOrderModeAnnotation;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Srm订单下单模式校验注解验证类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-15
 */
public class SrmOrderModeValidator implements ConstraintValidator<SrmOrderModeAnnotation, Integer> {
    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext context) {
        if(value == null) {
            return false;
        }

        return Stream.of(OrderModeEnum.PURCHASE_INQUIRY, OrderModeEnum.PURCHASE_PRICE_COMPETITION, OrderModeEnum.PURCHASE_BIDDING, OrderModeEnum.REQUISITION_CONTRACT, OrderModeEnum.FRAME_CONTRACT_ORDER).map(OrderModeEnum::getCode).collect(Collectors.toList()).contains(value);
    }
}
