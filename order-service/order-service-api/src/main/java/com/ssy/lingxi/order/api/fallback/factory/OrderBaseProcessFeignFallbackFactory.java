package com.ssy.lingxi.order.api.fallback.factory;

import com.ssy.lingxi.order.api.fallback.OrderBaseProcessFeignFallback;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 订单流程内部接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-05-30
 */
@Component
public class OrderBaseProcessFeignFallbackFactory implements FallbackFactory<OrderBaseProcessFeignFallback> {

    @Override
    public OrderBaseProcessFeignFallback create(Throwable cause) {
        return new OrderBaseProcessFeignFallback(cause);
    }

}
