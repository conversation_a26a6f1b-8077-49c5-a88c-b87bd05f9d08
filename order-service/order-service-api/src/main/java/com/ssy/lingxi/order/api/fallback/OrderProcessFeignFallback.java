package com.ssy.lingxi.order.api.fallback;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.order.api.feign.IOrderProcessFeign;
import com.ssy.lingxi.order.api.model.req.*;
import com.ssy.lingxi.order.api.model.resp.*;
import lombok.extern.slf4j.Slf4j;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 订单服务 - OpenFeign接口Fallback类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-31
 */
@Slf4j
public class OrderProcessFeignFallback implements IOrderProcessFeign {

    private final Throwable throwable;

    public OrderProcessFeignFallback(Throwable cause) {
        this.throwable = cause;
    }

    /**
     * 查询（供应）会员交易流程规则配置
     *
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<OrderTradeProcessFeignDetailResp> findVendorTradeProcess(@Valid OrderTradeProcessFeignReq feignVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    /**
     * 查询（采购）会员采购流程规则配置
     *
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<OrderPurchaseProcessFeignDetailResp> findBuyerPurchaseProcess(@Valid OrderPurchaseProcessFeignReq feignVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    /**
     * 查询平台后台 - 会员支付策略设置
     *
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<PlatformPayTypeFeignDetailResp>> findPlatformPayments(@Valid OrderMemberFeignReq feignVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    /**
     * 查询支付参数
     *
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<PaymentParameterFeignDetailResp> findPaymentParameters(@Valid OrderPayParameterFeignReq feignVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    /**
     * 查询支付参数（直接查询平台支付参数配置）
     *
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<PaymentParameterFeignDetailResp> findPlatformPaymentParameters(@Valid OrderPayChannelFeignReq feignVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    /**
     * 查询支付参数（直接查询会员支付参数配置）
     *
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<PaymentParameterFeignDetailResp> findMemberPaymentParameters(@Valid OrderPayParameterFeignReq feignVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    /**
     * 售后服务 - 更新订单商品的换货、退货、维修数量
     *
     * @param afterSales 接口参数
     * @return 更新结果
     */
    @Override
    public WrapperResp<Void> updateAfterSaleOrderProduct(@Valid List<OrderAfterSaleReq> afterSales) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    /**
     * 售后服务 - 更新订单商品的实际退款金额
     *
     * @param orderReturnAmountUpdateReq 退款信息VO
     * @return 更新结果
     */
    @Override
    public WrapperResp<Void> updateAfterSaleOrderProductAmount(OrderReturnAmountUpdateReq orderReturnAmountUpdateReq) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    /**
     * 售后服务 - 查询订单商品的换货、退货、维修数量、退款金额
     *
     * @param orderProductIds 订单商品Id列表
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<OrderAfterSaleProductFeignDetailResp>> findAfterSaleOrderProducts(@Valid List<OrderProductIdFeignReq> orderProductIds) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    /**
     * 售后服务 - 批量查询订单外部状态
     *
     * @param feignVO 订单Id列表
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<OrderAfterSaleStatusResp>> findAfterSaleOrderStatuses(@Valid OrderIdsFeignReq feignVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    /**
     * 售后服务 - 根据合同Id查询订单列表
     *
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<OrderAfterSaleOrderContractFeignResp>> findAfterSaleOrderContracts(@Valid OrderContractFeignReq feignVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    /**
     * 售后能力 - 查询已经确认支付结果的支付记录列表
     *
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<OrderAfterSalePaymentFeignDetailResp>> findAfterSaleOrderPayments(@Valid OrderIdsFeignReq feignVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    /**
     * 物流能力 - 确认物流单后，更新订单的物流单号
     *
     * @param feignVO 接口参数
     * @return 更新结果
     */
    @Override
    public WrapperResp<Void> updateOrderLogisticsNo(@Valid OrderUpdateLogisticsNoFeignReq feignVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    /**
     * 加工能力 - 更新订单商品加工数量
     *
     * @param feigns 接口参数
     * @return 更新结果
     */
    @Override
    public WrapperResp<Void> updateOrderProductEnhanceCount(@Valid List<OrderUpdateEnhanceFeignReq> feigns) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    /**
     * 结算服务 - 查询订单发票信息
     *
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<OrderSettleAccountInvoiceFeignDetailResp> findSettlementInvoiceDetail(@Valid OrderNoFeignReq feignVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    /**
     * 会员服务 - 订单评论完成后，更改订单评论状态
     *
     * @param feignVO 接口参数
     * @return 修改结果
     */
    @Override
    public WrapperResp<Void> updateOrderComment(@Valid OrderCommentFeignReq feignVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    /**
     * 支付服务 - 订单服务回调
     *
     * @param callbackFeignVO 回调参数
     * @return 操作结果
     */
    @Override
    public WrapperResp<Void> orderPayCallback(@Valid OrderPayCallbackFeignReq callbackFeignVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    /**
     * 售后服务 - 订单会员信息校验
     * @param checkVO 请求参数
     * @return 操作结果
     */
    @Override
    public WrapperResp<Boolean> checkAfterSaleOrderMember(CheckOrderMemberAfterSaleReq checkVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    /**
     * 结算服务 - 平台结算支付方式列表
     *
     * @return 平台结算支付方式列表
     */
    @Override
    public WrapperResp<List<PlatformSettlementTypeResp>> findSettlementType() {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    /**
     * 结算服务 - 平台结算渠道方式列表
     *
     * @param request 接口参数
     * @return 平台结算渠道方式列表
     */
    @Override
    public WrapperResp<List<MemberSettleChannelResp>> findSettlementChannel(@Valid MemberSettleChannelQueryReq request) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    /**
     * 所有服务通用 - 查询平台规则配置
     *
     * @param feignVO 查询条件
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<OrderRuleDetailResp>> findOrderRules(OrderRuleDetailFeignReq feignVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    /**
     * 营销服务 - 查询订单商品信息
     *
     * @param feignVO 查询条件
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<OrderMarketingDetailResp>> findOrderMarketingProducts(OrderMarketingFeignReq feignVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    /**
     * 会员服务 - 查询订单供应会员会员Logo、或店铺Logo、或自营商城Logo
     * @param feignVO 接口参数
     * @return 修改结果
     */
    @Override
    public WrapperResp<List<OrderVendorLogoFeignResp>> findOrderVendorLogo(OrderFeignIdsReq feignVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    /**
     * 结算能力 - 新增请款单-查询请款类型和付款方过滤采购订单数据
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<PageDataResp<OrderBuyerSettleQueryResp>> pageSettlementBuyerOrders(OrderBuyerSettlePageDataReq pageVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    /**
     * 结算服务 - 付款完成后修改订单收发货记录状态为已结算
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<Void> updateOrderDeliveries(OrderUpdateDeliveriesFeignReq feignVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    /**
     * 结算服务 - 协同对账 - 对账单生成请款单采购订单数据
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<OrderBuyerSettleReconciliationFeignResp>> settlementReconciliationBuyerOrders(OrderFeignIdsReq feignVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    @Override
    public WrapperResp<List<OrderQueryResp>> findOrdersByNos(@Valid OrderFeignNosReq feignVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    /**
     * “售后服务” - 查询订单商品使用“平台优惠券”后的优惠抵扣金额
     *
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<OrderProductCouponFeignResp>> findOrderProductCouponAmounts(@Valid OrderFeignIdsReq feignVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    /**
     * “搜索服务” - 批量查询会员满额包邮配置
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<OrderFreeExpressConfigResp>> getOrderFreeExpressConfigList(OrderFreeExpressFeignReq feignVO){
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    @Override
    public WrapperResp<List<OrderScoreConfigResp>> getOrderScoreConfigList(OrderScoreFeignReq feignVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    /**
     * 根据物料信息查询订单(商品能力- 物料价格库)
     * <AUTHOR>
     * @since 2022/3/25
     **/
    @Override
    public WrapperResp<OrderPriceQueryResp> materielPriceByMateriel(@Valid OrderPriceQueryReq request) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    @Override
    public WrapperResp<Void> updateOrderProductRequisitionedByAddRequisition(List<UpdateOrderProductRequisitionedFeignReq> feignVOS) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    @Override
    public WrapperResp<Void> updateOrderProductRequisitionedByUpdateRequisition(UpdateOrderProductRequisitionedByUpdateRequisitionFeignReq feignVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    @Override
    public WrapperResp<Boolean> checkUnclosedOrder(@Valid OrderFeignMemberIdReq feignVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }

    @Override
    public WrapperResp<OrderDepositResp> findOrderConfig() {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_ORDER_ERROR);
    }
}
