package com.ssy.lingxi.order.api.model.resp;

import java.io.Serializable;

/**
 * 请购单物料关联销售订单信息
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 22/05/26 16:45
 */
public class VendorOrderInfoResp implements Serializable {

    private static final long serialVersionUID = -8917030964002430439L;

    /**
     * 订单商品明细id
     */
    private Long orderProductId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 采购会员
     */
    private String buyerMemberName;

    /**
     * 下单时间
     */
    private String createTime;

    /**
     * 订单摘要
     */
    private String digest;

    /**
     * 商品skuId
     */
    private Long skuId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 单价
     */
    private String price;

    /**
     * 采购数量
     */
    private String quantity;

    /**
     * 金额
     */
    private String amount;

    /**
     * 外部状态
     */
    private String outerStatusName;

    public Long getOrderProductId() {
        return orderProductId;
    }

    public void setOrderProductId(Long orderProductId) {
        this.orderProductId = orderProductId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getBuyerMemberName() {
        return buyerMemberName;
    }

    public void setBuyerMemberName(String buyerMemberName) {
        this.buyerMemberName = buyerMemberName;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getDigest() {
        return digest;
    }

    public void setDigest(String digest) {
        this.digest = digest;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getQuantity() {
        return quantity;
    }

    public void setQuantity(String quantity) {
        this.quantity = quantity;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getOuterStatusName() {
        return outerStatusName;
    }

    public void setOuterStatusName(String outerStatusName) {
        this.outerStatusName = outerStatusName;
    }
}
