package com.ssy.lingxi.order.api.model.req;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 用户Id列表作为接口参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-12-31
 */
public class OrderFeignMemberIdReq implements Serializable {

    private static final long serialVersionUID = 9174434607645613721L;
    /**
     * 订单Id列表
     */
    @NotNull(message = "会员Id不能为空")
    private Long memberId;

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }
}
