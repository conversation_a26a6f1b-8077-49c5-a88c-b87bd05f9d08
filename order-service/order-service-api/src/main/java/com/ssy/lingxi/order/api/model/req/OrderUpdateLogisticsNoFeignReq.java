package com.ssy.lingxi.order.api.model.req;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 物流能力，确认物流单后，更新订单的物流单号
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-09-04
 */
public class OrderUpdateLogisticsNoFeignReq extends OrderIdFeignReq implements Serializable {
    private static final long serialVersionUID = 5741478632269634920L;

    /**
     * 物流单号
     */
    @NotBlank(message = "物流单号不能为空")
    @Size(max = 20, message = "物流单号最长20个字符")
    private String logisticsNo;

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }
}
