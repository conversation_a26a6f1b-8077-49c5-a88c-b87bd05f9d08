package com.ssy.lingxi.order.api.model.resp;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 结算服务-协同对账-对账单生成请款单查询订单返回VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-12-31
 */
public class OrderBuyerSettleReconciliationFeignResp implements Serializable {
    private static final long serialVersionUID = -442541596932475547L;
    /**
     * 订单Id
     */
    private Long orderId;

    /**
     * 是否含税
     */
    private Boolean tax;

    /**
     * 税率（百分比转换的小数）
     */
    private BigDecimal taxRate;

    /**
     * 订单商品金额
     */
    private BigDecimal productAmount;

    public OrderBuyerSettleReconciliationFeignResp() {
    }

    public OrderBuyerSettleReconciliationFeignResp(Long orderId, Boolean tax, BigDecimal taxRate, BigDecimal productAmount) {
        this.orderId = orderId;
        this.tax = tax;
        this.taxRate = taxRate;
        this.productAmount = productAmount;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Boolean getTax() {
        return tax;
    }

    public void setTax(Boolean tax) {
        this.tax = tax;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public BigDecimal getProductAmount() {
        return productAmount;
    }

    public void setProductAmount(BigDecimal productAmount) {
        this.productAmount = productAmount;
    }
}
