package com.ssy.lingxi.order.api.model.req;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.PositiveOrZero;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 售后服务 - 修改订单商品的换货、退货、维修数量、退款金额，商品列表
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-08-06
 */
public class OrderProductAfterSaleReq implements Serializable {
    private static final long serialVersionUID = -828327298738792475L;

    public OrderProductAfterSaleReq() {
        this.productId = 0L;
        this.exchangeCount = BigDecimal.ZERO;
        this.returnCount = BigDecimal.ZERO;
        this.maintainCount = BigDecimal.ZERO;
        this.returnAmount = BigDecimal.ZERO;
    }

    /**
     * 订单商品Id（订单服务生成的唯一Id）
     */
    @NotNull(message = "订单商品Id要大于0")
    @PositiveOrZero(message = "订单商品Id要大于0")
    private Long productId;

    /**
     * 换货数量
     */
    private BigDecimal exchangeCount;

    /**
     * 退货数量
     */
    private BigDecimal returnCount;

    /**
     * 维修数量
     */
    private BigDecimal maintainCount;

    /**
     * 退款金额
     */
    private BigDecimal returnAmount;

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public BigDecimal getExchangeCount() {
        return exchangeCount;
    }

    public void setExchangeCount(BigDecimal exchangeCount) {
        this.exchangeCount = exchangeCount;
    }

    public BigDecimal getReturnCount() {
        return returnCount;
    }

    public void setReturnCount(BigDecimal returnCount) {
        this.returnCount = returnCount;
    }

    public BigDecimal getMaintainCount() {
        return maintainCount;
    }

    public void setMaintainCount(BigDecimal maintainCount) {
        this.maintainCount = maintainCount;
    }

    public BigDecimal getReturnAmount() {
        return returnAmount;
    }

    public void setReturnAmount(BigDecimal returnAmount) {
        this.returnAmount = returnAmount;
    }
}
