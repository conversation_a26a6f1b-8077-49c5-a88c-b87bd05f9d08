package com.ssy.lingxi.component.xxlJob.common.enums;

/**
 * 调度类型枚举
 * <AUTHOR>
 * @version 2.0.0
 * @since 2023/9/6
 */
public enum ScheduleTypeEnum {

    NONE("NONE", "无"),
    CRON("CRON", "需要输入Cron表达式"),
    FIX_RATE("固定速度", "需要输入固定速度秒数");

    private final String code;
    private final String message;

    ScheduleTypeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
