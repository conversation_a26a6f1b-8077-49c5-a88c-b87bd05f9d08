package com.ssy.lingxi.component.rabbitMQ.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;

/**
 * 已售商品
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/10/22
 */
@Data
public class CommoditySoldDTO implements Serializable{
    private static final long serialVersionUID = 6448752534629435633L;

    /**
     * 商城id
     */
    private Long shopId;

    /**
     * 店铺id
     */
    private Long storeId;

    /**
     * 商品信息: key-商品skuId; value-该订单卖出的商品数量
     */
    private HashMap<Long, BigDecimal> commodityCountMap;

    /**
     * 会员id
     */
    private Long memberId;

    /**
     * 会员名称
     */
    private String memberName;

    /**
     * 会员角色id
     */
    private Long memberRoleId;

    /**
     * 会员角色名称
     */
    private String memberRoleName;
}
