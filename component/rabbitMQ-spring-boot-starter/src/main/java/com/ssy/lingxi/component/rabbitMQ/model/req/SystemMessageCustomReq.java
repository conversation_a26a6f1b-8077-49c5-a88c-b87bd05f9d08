package com.ssy.lingxi.component.rabbitMQ.model.req;

import java.io.Serializable;

/**
 * 系统自定义消息
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/11/30
 */
public class SystemMessageCustomReq implements Serializable {
    private static final long serialVersionUID = 6965770140466930623L;

    /**
     * 接收会员id
     */
    private Long memberId;

    /**
     * 接收角色id
     */
    private Long roleId;

    /**
     * 接收用户id
     */
    private Long userId;

    /**
     * 消息标题
     */
    private String title;

    /**
     * 消息内容
     */
    private String content;

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
