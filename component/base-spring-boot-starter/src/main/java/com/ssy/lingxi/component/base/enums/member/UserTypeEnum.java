package com.ssy.lingxi.component.base.enums.member;

import com.ssy.lingxi.component.base.language.LanguageHolder;
import lombok.Getter;

/**
 * 用户类型
 */
@Getter
public enum UserTypeEnum {

    /**
     * 超级管理员
     */
    ADMIN(1, "超级管理员"),

    /**
     * 普通用户
     */
    NORMAL(0, "普通用户");


    private final Integer code;
    private final String name;

    UserTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getName() {
        return LanguageHolder.getTranslation(this.getClass(), this.name, this.code);
    }
}
