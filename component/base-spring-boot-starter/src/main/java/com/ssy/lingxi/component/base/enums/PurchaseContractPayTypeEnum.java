package com.ssy.lingxi.component.base.enums;

import com.ssy.lingxi.component.base.language.LanguageHolder;

import java.util.Arrays;
import java.util.Objects;

/**
 * 采购合同付款方式枚举
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-08-10
 */
public enum PurchaseContractPayTypeEnum {
    /**
     * 现结 - 1
     */
    CASH(1, "现结"),

    /**
     * 账期(按月) - 2
     */
    PERIOD_MONTHLY(2, "账期(按月)"),

    /**
     * 账期(按天) - 3
     */
    PERIOD_DAILY(3, "账期(按天)"),

    /**
     * 月结 - 4
     */
    MONTHLY(4, "月结");

    PurchaseContractPayTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    private Integer code;
    private String name;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return LanguageHolder.getTranslation(this.getClass(), this.name, this.code);
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * 根据枚举值获得名称
     * @param code 枚举值
     * @return 名称
     */
    public static String getNameByCode(Integer code) {
        PurchaseContractPayTypeEnum purchaseContractPayTypeEnum = parse(code);
        return Objects.nonNull(purchaseContractPayTypeEnum) ? purchaseContractPayTypeEnum.getName() : null;
    }

    /**
     * 根据枚举值获得枚举
     * @param code 枚举值
     * @return 枚举
     */
    public static PurchaseContractPayTypeEnum parse(Integer code) {
        return Arrays.stream(PurchaseContractPayTypeEnum.values()).filter(e -> e.getCode().equals(code)).findFirst().orElse(null);
    }
}
