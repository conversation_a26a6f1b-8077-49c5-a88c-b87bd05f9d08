package com.ssy.lingxi.component.base.deserialize;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.deser.ContextualDeserializer;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.AesUtil;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * aes解析器（只适合用在基础类型）
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/9/6
 */

@AllArgsConstructor
@NoArgsConstructor
public class AesDecryptDeserialize extends JsonDeserializer<Object> implements ContextualDeserializer {
    /**
     * 用于缓存不同类型的反序列化器
     */
    private static final Map<Class<?>, AesDecryptDeserialize> deserializerCache = new ConcurrentHashMap<>();

    /**
     * 目标类型
     */
    private Class<?> targetType;

    @Override
    public Object deserialize(JsonParser p, DeserializationContext ctxt) {
        try {
            String decryptStr = AesUtil.decryptCBC(p.getValueAsString());

            if (targetType == String.class) {
                return decryptStr;
            } else if (targetType == Long.class) {
                return Long.parseLong(decryptStr);
            } else if (targetType == Integer.class) {
                return Integer.parseInt(decryptStr);
            } else if (targetType == Double.class) {
                return Double.parseDouble(decryptStr);
            } else if (targetType == BigDecimal.class) {
                return new BigDecimal(decryptStr);
            } else {
                return decryptStr;
            }
        } catch (IOException e) {
            throw new BusinessException(ResponseCodeEnum.AES_DECRYPT_ERROR);
        }
    }

    @Override
    public JsonDeserializer<?> createContextual(DeserializationContext ctxt, BeanProperty property) {
        if (property != null) {
            // 使用缓存获取或者创建新的反序列化器
            return deserializerCache.computeIfAbsent(property.getType().getRawClass(), AesDecryptDeserialize::new);
        } else if (ctxt.getContextualType() != null) {
            // 尝试从上下文中获取类型（如可能的根对象类型等）
            return deserializerCache.computeIfAbsent(ctxt.getContextualType().getRawClass(), AesDecryptDeserialize::new);
        }
        return this;
    }
}
