package com.ssy.lingxi.component.base.config;

import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * rabbitMq多消费者配置类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/10/12
 */
@Configuration
@ConditionalOnProperty("spring.rabbitmq.username")
public class RabbitMqConsumerConfig {

    //并发处理数量
    @Value("${rabbitMq.consumer.count:5}")
    private int defaultConsumers;

    /**
     * 定义监听工厂(因为base组件没有启动类，导致扫描不到包，idea报红，不影响使用)
     */
    @Bean("consumerFactoryConfig")
    public SimpleRabbitListenerContainerFactory containerFactory(ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setConcurrentConsumers(defaultConsumers);
        factory.setMaxConcurrentConsumers(defaultConsumers);
        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        factory.setPrefetchCount(1);
        return factory;
    }
}
