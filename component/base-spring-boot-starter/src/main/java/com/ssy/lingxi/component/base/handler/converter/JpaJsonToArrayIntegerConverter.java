package com.ssy.lingxi.component.base.handler.converter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import io.seata.common.util.StringUtils;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.Objects;

/**
 * Jpa 将 Json(或Jsonb)字段转换为Map<String, Object>
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/7/6
 */
@Converter(autoApply = true)
public class JpaJsonToArrayIntegerConverter implements AttributeConverter<Integer[], String> {
    private final static ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 将Map转换为数据库Json字段
     * @param meta 参数
     */
    @Override
    public String convertToDatabaseColumn(Integer[] meta) {
        try {
            if(Objects.isNull(meta)){
                return null;
            }
            return objectMapper.writeValueAsString(meta);
        } catch (JsonProcessingException ex) {
            throw new BusinessException(ResponseCodeEnum.BUSINESS_ERROR,"数据库Json字段写入错误");
        }
    }

    /**
     * 将数据库Json字段转换为Map
     * @param dbData 参数
     */
    @Override
    public Integer[] convertToEntityAttribute(String dbData) {
        try {
            if(StringUtils.isBlank(dbData)){
                return new Integer[]{};
            }
            return objectMapper.readValue(dbData, new TypeReference<Integer[]>() {});
        } catch (JsonProcessingException ex) {
            throw new BusinessException(ResponseCodeEnum.BUSINESS_ERROR,"数据库Json字段反序列化错误");
        }
    }
}
