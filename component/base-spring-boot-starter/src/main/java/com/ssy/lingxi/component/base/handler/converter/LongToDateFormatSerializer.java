package com.ssy.lingxi.component.base.handler.converter;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;

import java.io.IOException;

/**
 * 将Long类型的时间戳转换为日期格式的字符串
 *
 * <AUTHOR>
 */
public class LongToDateFormatSerializer extends JsonSerializer<Long> {

    @Override
    public void serialize(Long value, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        try {
            jsonGenerator.writeString(DateUtil.format(DateUtil.date(value), DatePattern.NORM_DATETIME_PATTERN));
        } catch (Exception e) {
            throw new BusinessException(ResponseCodeEnum.AES_ENCRYPTION_ERROR);
        }
    }

}
