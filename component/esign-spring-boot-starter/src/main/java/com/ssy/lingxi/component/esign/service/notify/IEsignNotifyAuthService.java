package com.ssy.lingxi.component.esign.service.notify;

import com.ssy.lingxi.component.esign.model.resp.v3.notify.NotifyAuthV3Resp;
import com.ssy.lingxi.component.esign.model.resp.v3.notify.NotifyRealNameV3Resp;

/**
 * 实名认证&授权回调通知接口
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/11/14
 */
public interface IEsignNotifyAuthService {

    /**
     * 实名认证通过回调
     * @param notifyRealNameV3Req  通知内容
     */
    boolean realNameNotify(NotifyRealNameV3Resp notifyRealNameV3Req);

    /**
     * 授权完成回调
     * @param notifyAuthV3Req  通知内容
     */
    boolean authNotify(NotifyAuthV3Resp notifyAuthV3Req);

}