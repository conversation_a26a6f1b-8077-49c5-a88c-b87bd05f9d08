package com.ssy.lingxi.component.esign.model.resp.v3.notify;

import com.ssy.lingxi.component.esign.common.enums.NotifyActionV3Enum;
import lombok.Data;

/**
 * e签宝回调基类
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/11/20
 */
@Data
public class NotifyBaseV3Resp {

    /**
     * 标记该通知的业务类型
     * @see NotifyActionV3Enum
     */
    private String action;

    /**
     * 回调通知触发时间，Unix时间戳格式，单位：毫秒
     */
    private Long timestamp;
}
