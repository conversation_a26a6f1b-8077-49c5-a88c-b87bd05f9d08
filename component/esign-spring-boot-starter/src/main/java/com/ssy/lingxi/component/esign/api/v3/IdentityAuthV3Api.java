package com.ssy.lingxi.component.esign.api.v3;

import cn.hutool.core.bean.BeanUtil;
import com.ssy.lingxi.component.esign.api.standard.AuthTokenApi;
import com.ssy.lingxi.component.esign.common.constant.ApiParamConstant;
import com.ssy.lingxi.component.esign.common.constant.ApiV3UrlConstant;
import com.ssy.lingxi.component.esign.common.enums.RequestMethodEnum;
import com.ssy.lingxi.component.esign.config.EsignConfig;
import com.ssy.lingxi.component.esign.exception.EsignException;
import com.ssy.lingxi.component.esign.model.req.v3.OrganizationAuthV3Req;
import com.ssy.lingxi.component.esign.model.req.v3.OrganizationRealNameStatusV3Req;
import com.ssy.lingxi.component.esign.model.req.v3.PersonalAuthV3Req;
import com.ssy.lingxi.component.esign.model.req.v3.PersonalRealNameStatusV3Req;
import com.ssy.lingxi.component.esign.model.resp.v3.*;
import com.ssy.lingxi.component.esign.util.EsignHttpUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * 认证授权API
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/11/14
 */
public class IdentityAuthV3Api {

    private final AuthTokenApi authTokenApi;
    private final EsignConfig properties;

    public IdentityAuthV3Api(AuthTokenApi authTokenApi, EsignConfig properties) {
        this.authTokenApi = authTokenApi;
        this.properties = properties;
    }

    /**
     * 查询个人认证状态
     */
    public PersonalRealNameStatusV3Resp getPersonalRealNameStatus(PersonalRealNameStatusV3Req personalRealNameStatusV3Req) throws EsignException {
        Map<String, Object> paramMap = BeanUtil.beanToMap(personalRealNameStatusV3Req);

        return EsignHttpUtil.execute(ApiV3UrlConstant.GET_PERSONAL_REAL_NAME_STATUS, RequestMethodEnum.GET, paramMap, authTokenApi, properties, PersonalRealNameStatusV3Resp.class);
    }

    /**
     * 查询个人授权信息
     * @param psnId 个人账号ID（或企业经办人账号ID）
     */
    public PersonalAuthorizedStatusV3Resp getPersonalAuthorizedStatus(String psnId) throws EsignException {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(ApiParamConstant.PSN_ID, psnId);

        return EsignHttpUtil.execute(ApiV3UrlConstant.GET_PERSONAL_AUTHORIZED_STATUS, RequestMethodEnum.GET, paramMap, authTokenApi, properties, PersonalAuthorizedStatusV3Resp.class);
    }

    /**
     * 获取个人认证或授权链接url
     */
    public AuthV3Resp getPersonalAuthUrl(PersonalAuthV3Req personalAuthV3Req) throws EsignException {
        Map<String, Object> paramMap = BeanUtil.beanToMap(personalAuthV3Req);

        return EsignHttpUtil.execute(ApiV3UrlConstant.GET_PERSONAL_AUTH_URL, RequestMethodEnum.POST, paramMap, authTokenApi, properties, AuthV3Resp.class);
    }

    /**
     * 查询机构认证状态
     */
    public OrganizationRealNameStatusV3Resp getOrganizationRealNameStatus(OrganizationRealNameStatusV3Req organizationRealNameStatusV3Req) throws EsignException {
        Map<String, Object> paramMap = BeanUtil.beanToMap(organizationRealNameStatusV3Req);

        return EsignHttpUtil.execute(ApiV3UrlConstant.GET_ORGANIZATION_REAL_NAME_STATUS, RequestMethodEnum.GET, paramMap, authTokenApi, properties, OrganizationRealNameStatusV3Resp.class);
    }

    /**
     * 查询机构授权状态
     * @param orgId 机构账号ID
     */
    public OrganizationAuthorizedStatusV3Resp getOrganizationAuthorizedStatus(String orgId) throws EsignException {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(ApiParamConstant.ORG_ID, orgId);

        return EsignHttpUtil.execute(ApiV3UrlConstant.GET_ORGANIZATION_AUTHORIZED_STATUS, RequestMethodEnum.GET, paramMap, authTokenApi, properties, OrganizationAuthorizedStatusV3Resp.class);
    }

    /**
     * 获取机构认证或授权链接url
     */
    public AuthV3Resp getOrganizationAuthUrl(OrganizationAuthV3Req organizationAuthV3Req) throws EsignException {
        Map<String, Object> paramMap = BeanUtil.beanToMap(organizationAuthV3Req);

        return EsignHttpUtil.execute(ApiV3UrlConstant.GET_ORGANIZATION_AUTH_URL, RequestMethodEnum.POST, paramMap, authTokenApi, properties, AuthV3Resp.class);
    }

    /**
     * 查询认证授权流程详情
     */
    public String getAuthFlowUrl(String authFlowId) throws EsignException {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(ApiParamConstant.AUTH_FLOW_ID, authFlowId);

        return EsignHttpUtil.execute(ApiV3UrlConstant.GET_AUTH_FLOW_URL, RequestMethodEnum.GET, paramMap, authTokenApi, properties);
    }
}
