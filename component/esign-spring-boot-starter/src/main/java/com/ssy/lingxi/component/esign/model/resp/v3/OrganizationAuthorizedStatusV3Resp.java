package com.ssy.lingxi.component.esign.model.resp.v3;

import com.ssy.lingxi.component.esign.common.enums.PersonalAuthorizedScopeEnum;
import lombok.Data;

import java.util.List;

/**
 * 查询机构授权信息响应实体
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/11/20
 */
@Data
public class OrganizationAuthorizedStatusV3Resp {

    /**
     * 用户授权信息
     */
    private List<AuthorizedInfo> authorizedInfo;

    /**
     * 用户授权信息
     */
    @Data
    public static class AuthorizedInfo{

        /**
         * 用户授权范围
         * @see PersonalAuthorizedScopeEnum
         */
        private String authorizedScope;

        /**
         * 授权生效时间（Unix时间戳格式，单位毫秒）
         */
        private String effectiveTime;

        /**
         * 授权到期时间（Unix时间戳格式，单位毫秒）
         */
        private String expireTime;
    }
}
