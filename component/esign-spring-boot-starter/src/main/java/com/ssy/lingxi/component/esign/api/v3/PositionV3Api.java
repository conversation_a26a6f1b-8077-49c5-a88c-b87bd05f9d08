package com.ssy.lingxi.component.esign.api.v3;

import cn.hutool.core.bean.BeanUtil;
import com.ssy.lingxi.component.esign.api.standard.AuthTokenApi;
import com.ssy.lingxi.component.esign.common.constant.ApiV3UrlConstant;
import com.ssy.lingxi.component.esign.common.enums.RequestMethodEnum;
import com.ssy.lingxi.component.esign.config.EsignConfig;
import com.ssy.lingxi.component.esign.exception.EsignException;
import com.ssy.lingxi.component.esign.model.req.v3.WordsPositionSearchV3Req;
import com.ssy.lingxi.component.esign.model.resp.v3.WordsPositionSearchV3Resp;
import com.ssy.lingxi.component.esign.util.EsignHttpUtil;

import java.util.Map;

/**
 * 文档位置API
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/11/14
 */
public class PositionV3Api {

    private final AuthTokenApi authTokenApi;
    private final EsignConfig properties;

    public PositionV3Api(AuthTokenApi authTokenApi, EsignConfig properties) {
        this.authTokenApi = authTokenApi;
        this.properties = properties;
    }

    /**
     * 搜索关键字坐标
     */
    public WordsPositionSearchV3Resp wordsPositionSearch(WordsPositionSearchV3Req wordsPositionSearchV3Req) throws EsignException {
        Map<String, Object> paramMap = BeanUtil.beanToMap(wordsPositionSearchV3Req);
        
        return EsignHttpUtil.execute(ApiV3UrlConstant.WORDS_POSITION_SEARCH, RequestMethodEnum.POST, paramMap, authTokenApi, properties, WordsPositionSearchV3Resp.class);
    }
}