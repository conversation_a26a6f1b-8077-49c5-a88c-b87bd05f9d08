package com.ssy.lingxi.component.esign.model.req.standard;

import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class FaceCheckAuthReq {
    /**
     * 必填: 是
     * 姓名
     */
    private String name;
    /**
     * 必填: 是
     * 身份证号
     */
    private String idNo;
    /**
     * 必填: 是
     * 人脸认证方式
     * TENCENT   腾讯微众银行认证
     * ZHIMACREDIT  支付宝芝麻信用认证
     * ESIGN  e签宝刷脸
     */
    private String faceauthMode;
    /**
     * 必填: 是
     * 认证结束后页面跳转地址，如果是在移动端app发起认证，则需在该url参数中以key为esignAppScheme设置APP Scheme参数，
     * 比如：
     *【h5地址】?esignAppScheme=【scheme地址】（注意不要做编码），
     * 如果用户选择的是微众或e签宝自研刷脸，就会跳转到h5地址；如果用户选择的是支付宝，就会跳转到scheme地址
     */
    private String callbackUrl;
    /**
     * 必填: 否
     * 对接方业务上下文id，将在异步通知及跳转时携带返回对接方，最大长度不能超过500
     */
    private String contextId;
    /**
     * 必填: 否
     * 认证结束后异步通知地址
     */
    private String notifyUrl;

}
