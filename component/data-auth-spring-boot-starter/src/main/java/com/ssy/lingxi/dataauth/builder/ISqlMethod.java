package com.ssy.lingxi.dataauth.builder;

import com.ssy.lingxi.dataauth.enums.DataAuthTypeEnum;

/**
 * Sql构造器的共有方法接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-04-08
 */
public interface ISqlMethod<T extends AbstractSqlBuilder<T>> {
    /**
     * 传入被拦截的sql语句
     * @param sql 被拦截的sql语句
     * @return 当前实例
     */
    T fromSql(String sql);

    /**
     * 指定拦截类型
     * @param dataAuthTypeEnum 拦截类型枚举
     * @return 当前实例
     */
    T specifyType(DataAuthTypeEnum dataAuthTypeEnum);

    /**
     * 指定要进行处理的表名
     * @param dbTableName 要添加数据权限的表名
     * @return 当前实例
     */
    T specifyTable(String dbTableName);

    /**
     * 指定要进行处理的Entity类
     * @param entityClass xxx.class
     * @return 当前实例
     */
    T specifyTable(Class<?> entityClass);

    /**
     * 返回要执行的sql语句
     * @return 要执行的sql语句
     */
    String toSql();
}
