package com.ssy.lingxi.component.datasheetFile.common.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 冻结带有设置自动筛选范围常量的行和列
 *
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum FreezePaneEnum {

    /**
     * TEST_ONE  export
     */
    TEST_ONE(2, 2, 0, 0, "A2:BH2"),
    /**
     * TEST_TWO  export
     */
    TEST_TWO(2, 2, 0, 0, "A2:AC2"),
    /**
     * TEST_THREE  export
     */
    TEST_THREE(2, 2, 0, 0, "A2:T2"),
    /**
     * 冻结首行表头
     */
    FIRST_ROW_HEAD(0, 1, 0, 0, null),

    ;

    /**
     * 水平分裂位置
     */
    private final Integer colSplit;
    /**
     * 垂直分裂位置
     */
    private final Integer rowSplit;
    /**
     * 左列在右窗格中可见
     */
    private final Integer leftmostColumn;
    /**
     * 顶部行在底部窗格中可见
     */
    private final Integer topRow;
    /**
     * 自动过滤范围
     */
    private final String autoFilterRange;

}