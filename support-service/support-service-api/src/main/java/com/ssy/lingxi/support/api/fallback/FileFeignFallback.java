package com.ssy.lingxi.support.api.fallback;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.support.api.feign.IFileFeign;
import com.ssy.lingxi.support.api.model.dto.FileDTO;
import com.ssy.lingxi.support.api.model.req.FileDownloadReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

/**
 * 短信服务器配置熔断服务实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/15
 */
@Slf4j
public class FileFeignFallback implements IFileFeign {

    private final Throwable throwable;

    public FileFeignFallback(Throwable cause) {
        this.throwable = cause;
    }

    @Override
    public WrapperResp<String> upload(MultipartFile file, Integer prefix) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_SUPPORT_ERROR);
    }

    @Override
    public WrapperResp<Void> delete(String fileUrl) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_SUPPORT_ERROR);
    }

    @Override
    public WrapperResp<FileDTO> download(FileDownloadReq fileDownloadReq) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_SUPPORT_ERROR);
    }
}
