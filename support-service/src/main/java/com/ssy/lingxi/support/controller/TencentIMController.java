package com.ssy.lingxi.support.controller;

import cn.hutool.json.JSONObject;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.support.model.req.im.IMGetSingleRecordMsgReq;
import com.ssy.lingxi.support.model.req.im.IMSendFaceMsgReq;
import com.ssy.lingxi.support.model.req.im.IMSendImageMsgReq;
import com.ssy.lingxi.support.model.req.im.IMSendTextMsgReq;
import com.ssy.lingxi.support.model.resp.im.IMCommonResp;
import com.ssy.lingxi.support.model.resp.im.IMUserSigResp;
import com.ssy.lingxi.support.model.resp.im.msg.IMSendMsgResp;
import com.ssy.lingxi.support.model.resp.im.session.IMSessionListResp;
import com.ssy.lingxi.support.service.ITencentIMService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 腾讯即时通讯IM控制器
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 20201/03/22
 */
@RestController
@RequestMapping(ServiceModuleConstant.SUPPORT_PATH_PREFIX + "/tim")
public class TencentIMController extends BaseController {

    @Resource
    private ITencentIMService tencentIMService;


    /**
     * 单聊单发文本消息
     *
     * @param sendTextMsgReq 发送文本 消息对象
     * @return
     */
    @PostMapping("/sendTextMsg")
    public WrapperResp sendTextMsg(@RequestBody IMSendTextMsgReq sendTextMsgReq) {
        IMSendMsgResp imSendMsgResp = tencentIMService.sendTextMsg(sendTextMsgReq);
        Integer errorCode = imSendMsgResp.getErrorCode();
        if (errorCode != 0) {
            return WrapperUtil.fail(errorCode, imSendMsgResp.getErrorInfo());
        }
        return WrapperUtil.success(imSendMsgResp);
    }

    /**
     * 单聊单发表情消息
     *
     * @param sendFaceMsgReq 发送表情消息对象
     * @return
     */
    @PostMapping("/sendFaceMsg")
    public WrapperResp sendFaceMsg(@RequestBody IMSendFaceMsgReq sendFaceMsgReq) {
        IMSendMsgResp imSendMsgResp = tencentIMService.sendFaceMsg(sendFaceMsgReq);
        Integer errorCode = imSendMsgResp.getErrorCode();
        if (errorCode != 0) {
            return WrapperUtil.fail(errorCode, imSendMsgResp.getErrorInfo());
        }
        return WrapperUtil.success(imSendMsgResp);
    }

    /**
     * 单聊单发图片消息，需要添加一个图片上传接口，同时生成原图、大图、缩略图，携带文件大小、长度、宽度参数信息，文件名用UUID生成
     *
     * @param sendImageMsgReq 发送图片消息对象
     * @return
     */
    @PostMapping("/sendImageMsg")
    public WrapperResp sendImageMsg(@RequestBody IMSendImageMsgReq sendImageMsgReq) {
        IMSendMsgResp imSendMsgResp = tencentIMService.sendImageMsg(sendImageMsgReq);
        Integer errorCode = imSendMsgResp.getErrorCode();
        if (errorCode != 0) {
            return WrapperUtil.fail(errorCode, imSendMsgResp.getErrorInfo());
        }
        return WrapperUtil.success(imSendMsgResp);
    }


    /**
     * 获取用户的会话列表
     *
     * @param userId 用户ID（mem_user主键 userID）
     * @return
     */
    @GetMapping("/getRecentSessionList")
    public WrapperResp getRecentSessionList(Long userId) {
        IMSessionListResp recentSessionList = tencentIMService.getRecentSessionList(userId);
        Integer errorCode = recentSessionList.getErrorCode();
        if (errorCode != 0) {
            return WrapperUtil.fail(errorCode, recentSessionList.getErrorInfo());
        }
        return WrapperUtil.success(recentSessionList);
    }

    /**
     * 查询两个用户的历史聊天记录
     *
     * @param getSingleRecordMsgReq 获取用户聊天记录请求对象
     * @return
     */
    @PostMapping("/getSigleRecordMsg")
    public WrapperResp getSigleRecordMsg(@RequestBody @Valid IMGetSingleRecordMsgReq getSingleRecordMsgReq) {
        JSONObject jsonObject = tencentIMService.getSigleRecordMsg(getSingleRecordMsgReq);
        IMCommonResp commonResp = jsonObject.toBean(IMCommonResp.class);
        Integer errorCode = commonResp.getErrorCode();
        if (errorCode != 0) {
            return WrapperUtil.fail(errorCode, commonResp.getErrorInfo());
        }
        return WrapperUtil.success(jsonObject);
    }

    /**
     * 通过平台登录用户去登录TIM平台返回 UserSig和TIM平台的userID
     */
    @GetMapping("/getUserSig")
    public WrapperResp<IMUserSigResp> getUserSig() {
        UserLoginCacheDTO sysUser = getSysUser();
        IMUserSigResp userSigResp = tencentIMService.getUserSig(sysUser);
        return WrapperUtil.success(userSigResp);
    }

    /**
     * 通过平台登录用户去获取总未读消息数量
     */
    @GetMapping("/getUnreadMsgNum")
    public WrapperResp<Integer> getUnreadMsgNum() {
        UserLoginCacheDTO sysUser = getSysUser();
        Integer unreadMsgNum = tencentIMService.getUnreadMsgNum(sysUser);
        return WrapperUtil.success(unreadMsgNum);
    }
}