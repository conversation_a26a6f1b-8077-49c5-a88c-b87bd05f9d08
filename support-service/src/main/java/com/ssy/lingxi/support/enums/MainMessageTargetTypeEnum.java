package com.ssy.lingxi.support.enums;

/**
 * 信息类型枚举
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/10
 */
public enum MainMessageTargetTypeEnum {

    /**
     * 角色id数组 - roleIds
     */
    ROLE_IDS("roleIds", "角色id数组");

    private final String code;
    private final String message;

    MainMessageTargetTypeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

}
