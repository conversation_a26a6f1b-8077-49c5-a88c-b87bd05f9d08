package com.ssy.lingxi.product.repository.freightSpace;

import com.ssy.lingxi.product.entity.do_.freightSpace.FreightSpaceInventoryRecordDO;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* 仓位扣减记录
* <AUTHOR>
* @since 2020/7/8
*/
@Repository
public interface FreightSpaceInventoryRecordRepository extends JpaRepository<FreightSpaceInventoryRecordDO, Long>, JpaSpecificationExecutor<FreightSpaceInventoryRecordDO> {
    List<FreightSpaceInventoryRecordDO> findByOrderId(Long orderId);

    List<FreightSpaceInventoryRecordDO> findByOrderIdInAndProductSkuIdIn(List<Long> orderIdList, List<Long> commoditySkuIdList);

    List<FreightSpaceInventoryRecordDO> findByOrderIdEqualsAndTypeEqualsAndStateEquals(Long orderId, int placeOrder, Integer code, Sort id);
}
