package com.ssy.lingxi.product.handler.converter;

import com.ssy.lingxi.common.util.JsonUtil;
import com.ssy.lingxi.product.api.model.dto.UnitConversionDTO;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.List;

/**
* list 转json
* <AUTHOR>
* @since 2022/04/21
*/
@Converter(autoApply = true)
public class JpaJsonToUnitConversionListConverter implements AttributeConverter<List<UnitConversionDTO>, String> {

    @Override
    public String convertToDatabaseColumn(List<UnitConversionDTO> meta) {
        return JsonUtil.toJson(meta);
    }

    @Override
    public List<UnitConversionDTO> convertToEntityAttribute(String dbData) {
        return JsonUtil.toList(dbData, UnitConversionDTO.class);
    }
}
