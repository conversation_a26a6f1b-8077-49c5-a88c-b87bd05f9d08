package com.ssy.lingxi.product;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 商品服务启动类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/19
 */
@EnableAsync
@EnableAspectJAutoProxy(exposeProxy = true)
@EnableFeignClients(basePackages = {"com.ssy.lingxi.**.api.feign"})
@ComponentScan(basePackages = {
        "com.ssy.lingxi.component",
        "com.ssy.lingxi.**.api.fallback",
        "com.ssy.lingxi." + ServiceModuleConstant.PRODUCT + ".config",
        "com.ssy.lingxi." + ServiceModuleConstant.PRODUCT + ".controller",
        "com.ssy.lingxi." + ServiceModuleConstant.PRODUCT + ".entity",
        "com.ssy.lingxi." + ServiceModuleConstant.PRODUCT + ".handler",
        "com.ssy.lingxi." + ServiceModuleConstant.PRODUCT + ".repository",
        "com.ssy.lingxi." + ServiceModuleConstant.PRODUCT + ".serviceImpl",
})
@EnableDiscoveryClient
@SpringBootApplication
public class ProductServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(ProductServiceApplication.class, args);
    }
}
