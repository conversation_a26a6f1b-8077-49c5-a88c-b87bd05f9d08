package com.ssy.lingxi.product.repository.freightSpace;

import com.ssy.lingxi.product.entity.do_.freightSpace.FreightSpaceShopDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 仓位适用商城持久化层
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/7/7
 */
@Repository
public interface FreightSpaceShopRepository extends JpaRepository<FreightSpaceShopDO, Long>, JpaSpecificationExecutor<FreightSpaceShopDO> {

    List<FreightSpaceShopDO> findByFreightSpaceIdEquals(Long freightSpaceId);

    void deleteByFreightSpaceIdEquals(Long freightSpaceId);

    List<FreightSpaceShopDO> findByFreightSpaceIdIn(List<Long> ids);

    List<FreightSpaceShopDO> findByShopId(Long shopId);

    List<FreightSpaceShopDO> findByShopIdIn(List<Long> shopIdList);
}
