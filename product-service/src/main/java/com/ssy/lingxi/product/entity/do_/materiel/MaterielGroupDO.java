package com.ssy.lingxi.product.entity.do_.materiel;

import com.ssy.lingxi.common.constant.TableNameConstant;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 物料组
 * <AUTHOR>
 * @version 2.0.0
 * @since 22/03/22 16:07
 */
@Getter
@Setter
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_PRODUCT_SERVICE + "material_group", indexes = {@Index(name = TableNameConstant.TABLE_PRE_PRODUCT_SERVICE + "material_group_member_id_member_role_id_idx", columnList = "memberId,memberRoleId"),
        @Index(name = TableNameConstant.TABLE_PRE_PRODUCT_SERVICE + "material_group_name_idx", columnList = "name"),
        @Index(name = TableNameConstant.TABLE_PRE_PRODUCT_SERVICE + "material_group_level_idx", columnList = "level"),
        @Index(name = TableNameConstant.TABLE_PRE_PRODUCT_SERVICE + "material_group_parent_id_idx", columnList = "parentId")})
public class MaterielGroupDO implements Serializable {

    private static final long serialVersionUID = -6554901224673451249L;
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 物料组名称
     */
    @Column(columnDefinition = "varchar(40)", nullable = false)
    private String name;

    /**
     * 物料组代码
     */
    @Column(columnDefinition = "varchar(12)",nullable = false)
    private String code;

    /**
     * 物料组描述
     */
    @Column(columnDefinition = "varchar(64)")
    private String description;


    /**
     * 级别
     */
    @Column(columnDefinition = "int", nullable = false)
    private Integer level;

    /**
     * 物料组父id
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long parentId;

    /**
     * 完整Id
     */
    @Column(columnDefinition = "varchar(100)", nullable = false)
    private String fullId;


    /**
     * 会员id
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long memberId;

    /**
     * 会员名称
     */
    @Column(columnDefinition = "varchar(50)", nullable = false)
    private String memberName;

    /**
     * 会员角色id
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long memberRoleId;

    /**
     * 会员角色名称
     */
    @Column(columnDefinition = "varchar(50)", nullable = false)
    private String memberRoleName;

    /**
     * 用户id
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long userId;

    /**
     * 用户名称
     */
    @Column(columnDefinition = "varchar(50)", nullable = false)
    private String userName;

    /**
     * 创建时间
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long createTime = System.currentTimeMillis();

}
