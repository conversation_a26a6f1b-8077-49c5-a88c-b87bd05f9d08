package com.ssy.lingxi.product.entity.do_.warehouse;

import com.ssy.lingxi.common.constant.TableNameConstant;
import com.ssy.lingxi.product.api.enums.DeliveryTypeEnum;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 仓库物流信息
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-24
 */
@Getter
@Setter
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_PRODUCT_SERVICE + "warehouse_logistics")
public class WarehouseLogisticsDO implements Serializable {
    private static final long serialVersionUID = 3263864847378917414L;

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 仓库组ID
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long warehouseId;

    /**
     * 配送方式: 1-物流（默认）, 2-自提, 3-无需配送, 4-物流+自提
     * @see DeliveryTypeEnum
     */
    @Column(columnDefinition = "int", nullable = false)
    private Integer deliveryType;

    /**
     * 运费方式: 1-卖家承担运费（默认）, 2-买家承担运费
     */
    @Column(columnDefinition = "int")
    private Integer carriageType;

    /**
     * 重量: 单位-KG（公斤）
     */
    @Column(columnDefinition = "decimal")
    private BigDecimal weight;

    /**
     * 发货地址id
     */
    @Column(columnDefinition = "int8")
    private Long sendAddressId;

    /**
     * 发货周期：下单后xx天发货
     */
    @Column(columnDefinition = "int")
    private Integer deliveryPeriod;

    /**
     * 物流公司id
     */
    @Column(columnDefinition = "int8")
    private Long companyId;

    /**
     * 是否使用运费模板
     */
    @Column(columnDefinition = "boolean")
    private Boolean useTemplate = false;

    /**
     * 运费模板id
     */
    @Column(columnDefinition = "int8")
    private Long templateId;

    /**
     * 创建时间
     */
    @Column(columnDefinition = "timestamp")
    private LocalDateTime createTime = LocalDateTime.now();

    /**
     * 更新时间
     */
    @Column(columnDefinition = "timestamp")
    private LocalDateTime updateTime = LocalDateTime.now();
}
