package com.ssy.lingxi.product.model.req.platform;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * 平台后台 - 物流流程规则适用会员参数
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/3/25 14:53
 */
public class PlatformMaterielProcessMemberReq implements Serializable {
    private static final long serialVersionUID = 2053612729582092752L;
    /**
     * 会员id
     */
    @NotNull(message = "会员id要大于0")
    @Positive(message = "会员id要大于0")
    private Long memberId;

    /**
     * 会员角色id
     */
    @NotNull(message = "会员角色id要大于0")
    @Positive(message = "会员角色id要大于0")
    private Long roleId;

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }
}
