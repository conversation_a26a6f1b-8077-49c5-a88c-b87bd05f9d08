package com.ssy.lingxi.product.handler.converter;

import com.ssy.lingxi.common.util.JsonUtil;
import com.ssy.lingxi.product.entity.bo.SampleDeliveryProductAttachmentBO;

import javax.persistence.AttributeConverter;

/**
 * 转换送样需求单明细附件
 * <AUTHOR>
 * @since 2022/07/07
 * @version 2.0.0
 */
public class JpaJsonToSampleDeliveryProductAttachmentBOConverter implements AttributeConverter<SampleDeliveryProductAttachmentBO, String> {

    @Override
    public String convertToDatabaseColumn(SampleDeliveryProductAttachmentBO meta) {
        return JsonUtil.toJson(meta);
    }

    @Override
    public SampleDeliveryProductAttachmentBO convertToEntityAttribute(String dbData) {
        return JsonUtil.toObj(dbData, SampleDeliveryProductAttachmentBO.class);
    }
}