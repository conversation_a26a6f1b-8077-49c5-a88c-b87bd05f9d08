package com.ssy.lingxi.product.entity.do_;

import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.ssy.lingxi.common.constant.TableNameConstant;
import com.ssy.lingxi.component.base.enums.product.CommoditySaleModeEnum;
import com.ssy.lingxi.product.enums.PurchaseCommodityTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Set;

/**
 * 进货单实体类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/9/4
 */
@Getter
@Setter
@Entity
@FieldNameConstants
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_PRODUCT_SERVICE + "shop_purchase", indexes = {@Index(name = TableNameConstant.TABLE_PRE_PRODUCT_SERVICE + "shop_purchase_m_id_m_r_id_u_id_idx", columnList = "memberId,memberRoleId,userId"),
        @Index(name = TableNameConstant.TABLE_PRE_PRODUCT_SERVICE + "shop_purchase_commodity_sku_id_idx", columnList = "commoditySkuId"),
        @Index(name = TableNameConstant.TABLE_PRE_PRODUCT_SERVICE + "shop_purchase_m_id_m_r_id_s_id_idx", columnList = "memberId,memberRoleId,shopId"),
        @Index(name = TableNameConstant.TABLE_PRE_PRODUCT_SERVICE + "shop_purchase_shop_id_idx", columnList = "shopId")})
public class PurchaseDO implements Serializable {
    private static final long serialVersionUID = -8605744169466466196L;
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 商品skuId
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long commoditySkuId;

    /**
     * 商品sku单件id
     */
    @Column(columnDefinition = "int8")
    private Long commoditySkuSingleId;

    /**
     * 商品类型：1-普通商品; 2-套餐商品; 3-秒杀商品; 4-换购商品;
     * @see PurchaseCommodityTypeEnum
     */
    @Column(columnDefinition = "int")
    private Integer purchaseCommodityType = 1;

    /**
     * 是否主商品
     */
    @Column(columnDefinition = "boolean")
    private Boolean isMain = true;

    /**
     * 套餐id
     */
    @Column(columnDefinition = "int8")
    private Long setMealId;

    /**
     * 套餐名称
     */
    @Column(columnDefinition = "varchar(50)")
    private String setMealName;

    /**
     * 子商品对应的主商品skuId(子商品必填)
     */
    @Column(columnDefinition = "int8")
    private Long parentSkuId;

    /**
     * 数量
     */
    @Column(columnDefinition = "decimal", nullable = false)
    private BigDecimal count;

    /**
     * 商城id
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long shopId;

    /**
     * 会员id
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long memberId;

    /**
     * 会员名称
     */
    @Column(columnDefinition = "varchar(50)", nullable = false)
    private String memberName;

    /**
     * 会员角色id
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long memberRoleId;

    /**
     * 会员角色名称
     */
    @Column(columnDefinition = "varchar(50)", nullable = false)
    private String memberRoleName;

    /**
     * 用户id
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long userId;

    /**
     * 用户名称
     */
    @Column(columnDefinition = "varchar(50)", nullable = false)
    private String userName;

    /**
     * 创建时间
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long createTime = System.currentTimeMillis();

    /**
     * 代客下单-客户会员id
     */
    @Column(columnDefinition = "int8")
    private Long customerMemberId;

    /**
     * 代客下单-客户会员角色id
     */
    @Column(columnDefinition = "int8")
    private Long customerMemberRoleId;

    /**
     * 代客下单-订单id
     */
    @Column(columnDefinition = "int8")
    private Long orderId;

    /**
     * 一对多双向关联商品sku下单仓位库存信息
     */
    @JsonManagedReference
    @OneToMany(mappedBy = "purchaseDO", cascade = {CascadeType.DETACH, CascadeType.REMOVE}, fetch = FetchType.LAZY)
    private Set<PurchaseProductPositionDO> purchaseProductPositionDOS;

    /**
     * 店铺id
     */
    @Column(columnDefinition = "int8")
    private Long branchId;

    /**
     * 销售方式，1-现货，2-订货
     * @see CommoditySaleModeEnum
     */
    @Column(columnDefinition = "int2")
    private Integer saleMode;

    /**
     * 商品码
     */
    @Column(columnDefinition = "varchar(50)")
    private String singleCode;

    /**
     * 商品单件id
     */
    @Column(columnDefinition = "int8")
    private Long commoditySingleId;
}
