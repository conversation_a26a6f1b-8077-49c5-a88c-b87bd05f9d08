package com.ssy.lingxi.product.serviceImpl.feign;

import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.marketing.api.feign.IActivityGoodsFeign;
import com.ssy.lingxi.marketing.api.model.request.ProductTagReq;
import com.ssy.lingxi.marketing.api.model.response.ProductTagResp;
import com.ssy.lingxi.product.service.feign.IMarketingFeignService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 营销服务Feign接口调用
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-03-28
 */
@Service
@RequiredArgsConstructor
public class MarketingFeignServiceImpl implements IMarketingFeignService {
    private final IActivityGoodsFeign activityGoodsFeign;

    @Override
    public List<ProductTagResp> listActivityGoodsProductTag(ProductTagReq productTagReq) {
        return WrapperUtil.getDataOrThrow(activityGoodsFeign.listActivityGoodsProductTag(productTagReq));
    }
}
