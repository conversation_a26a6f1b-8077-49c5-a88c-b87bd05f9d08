package com.ssy.lingxi.product.handler.init;

import com.ssy.lingxi.product.service.IProductDataInitService;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/3/25 17:15
 */
@Configuration
@DependsOn("languageHolder")
public class MaterielHandler {

    @Resource
    private IProductDataInitService productDataInitService;

    @PostConstruct
    public void initProductService() {
        // 初始商品服务数据
        productDataInitService.initProductServiceData();
    }
}
