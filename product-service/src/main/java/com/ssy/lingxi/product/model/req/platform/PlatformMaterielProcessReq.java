package com.ssy.lingxi.product.model.req.platform;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 平台后台 - 新增物流流程规则配置接口参数
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/3/25 14:45
 */
public class PlatformMaterielProcessReq implements Serializable {
    private static final long serialVersionUID = 7507477088142248147L;

    /**
     * 规则名称
     */
    @NotBlank(message = "平台物流流程规则名称不能为空")
    @Size(max = 200, message = "平台物流流程规则名称最长200个字")
    private String name;

    /**
     * 基础流程id
     */
    @NotNull(message = "请选择流程规则")
    @Positive(message = "请选择流程规则")
    private Long baseProcessId;

    /**
     * 是否适用所有会员
     * true - 是
     * false - 否
     */
    @NotNull(message = "是否适用所有会员属性不能为空")
    private Boolean allMembers;

    /**
     * 适用会员列表，如不适用所有会员，不能为空
     */
    @Valid
    private List<PlatformMaterielProcessMemberReq> members;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getBaseProcessId() {
        return baseProcessId;
    }

    public void setBaseProcessId(Long baseProcessId) {
        this.baseProcessId = baseProcessId;
    }

    public Boolean getAllMembers() {
        return allMembers;
    }

    public void setAllMembers(Boolean allMembers) {
        this.allMembers = allMembers;
    }

    public List<PlatformMaterielProcessMemberReq> getMembers() {
        return members;
    }

    public void setMembers(List<PlatformMaterielProcessMemberReq> members) {
        this.members = members;
    }
}


