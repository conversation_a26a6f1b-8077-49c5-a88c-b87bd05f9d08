package com.ssy.lingxi.product.repository.platform;

import com.ssy.lingxi.product.entity.do_.platform.CategoryDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * 品类持久化层
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/22
 */
public interface CategoryRepository extends JpaRepository<CategoryDO,Long>, JpaSpecificationExecutor<CategoryDO> {
    Page<CategoryDO> findByNameContaining(String name, Pageable page);

    List<CategoryDO> findByNameContainingOrderByIdDesc(String name);

    @Query("select max(c.level) from CategoryDO c")
    Integer findMaxLevel();

    boolean existsByLevelAndNameAndParentIdAndIdNot(Integer level, String name, Long parentId, Long id);

    boolean existsByLevelAndName(Integer level, String name);

    List<CategoryDO> findByParentId(Long parentId);

    List<CategoryDO> findByFullIdStartingWith(String categoryIdKey);

    List<CategoryDO>  findByIdIn(List<Long> categoryIdList);

    List<CategoryDO> findByAttributeListId(Long id);
}
