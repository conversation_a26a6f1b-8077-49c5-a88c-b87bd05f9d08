package com.ssy.lingxi.product.repository.customer;

import com.ssy.lingxi.product.entity.do_.customer.CustomerAttributeDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * 属性持久化层
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/22
 */
public interface CustomerAttributeRepository extends JpaRepository<CustomerAttributeDO,Long>, JpaSpecificationExecutor<CustomerAttributeDO> {
    Page<CustomerAttributeDO> findByMemberIdAndMemberRoleIdAndNameContainingOrderByIdDesc(Long memberId, Long memberRoleId, String name, Pageable page);

    Page<CustomerAttributeDO> findByNameContainingOrderByIdDesc(String name, Pageable page);

    Page<CustomerAttributeDO> findByMemberIdAndMemberRoleIdAndNameContainingAndCustomerCategoryListId(Long memberId, Long memberRoleId, String name, Long categoryId, Pageable page);

    List<CustomerAttributeDO> findByNameContainingAndCustomerCategoryListId(String name, Long categoryId);

    List<CustomerAttributeDO> findByMemberIdAndMemberRoleIdAndNameAndIdNot(Long memberId, Long memberRoleId, String name, Long id);

    List<CustomerAttributeDO> findByMemberIdAndMemberRoleIdAndName(Long memberId, Long memberRoleId, String name);

    List<CustomerAttributeDO> findByMemberIdAndMemberRoleIdAndIsEnable(Long memberId, Long memberRoleId, Boolean isEnable);

    List<CustomerAttributeDO> findByIsEnable(Boolean isEnable);

    List<CustomerAttributeDO> findByMemberIdAndMemberRoleIdAndIsEnableAndTypeNot(Long memberId, Long memberRoleId, Boolean isEnable, Integer type);

    List<CustomerAttributeDO> findByIsEnableAndTypeIn(Boolean isEnable, List<Integer> types);

    List<CustomerAttributeDO> findByMemberIdAndMemberRoleIdAndIdIn(Long memberId, Long memberRoleId, List<Long> attributeIdList);

    List<CustomerAttributeDO> findByAttributeId(Long attributeId);

    List<CustomerAttributeDO> findByIdIn(List<Long> customerAttributeIdList);

    List<CustomerAttributeDO> findByMemberIdAndMemberRoleIdAndNameIn(Long memberId, Long memberRoleId, List<String> attributeNameList);

    CustomerAttributeDO findFirstByCode(String code);

    boolean existsByIdInAndIsEnable(List<Long> idList, Boolean isEnable);

    CustomerAttributeDO findFirstByDataWarehousePkId(String dataWarehousePkId);

    CustomerAttributeDO findFirstByName(String name);
}
