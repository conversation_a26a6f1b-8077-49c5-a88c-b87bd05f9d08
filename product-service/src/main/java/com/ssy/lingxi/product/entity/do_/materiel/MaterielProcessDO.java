package com.ssy.lingxi.product.entity.do_.materiel;

import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.ssy.lingxi.common.constant.TableNameConstant;
import lombok.Getter;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * 物料流程规则配置
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/3/25 10:24
 */
@Getter
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA,
        name = TableNameConstant.TABLE_PRE_PRODUCT_SERVICE + "material_process",
        indexes = {
        @Index(name = TableNameConstant.TABLE_PRE_PRODUCT_SERVICE + "material_process_member_id_idx", columnList = "memberId"),
        @Index(name = TableNameConstant.TABLE_PRE_PRODUCT_SERVICE + "material_process_role_id_idx", columnList = "roleId"),
        @Index(name = TableNameConstant.TABLE_PRE_PRODUCT_SERVICE + "material_process_name_idx", columnList = "name"),
        @Index(name = TableNameConstant.TABLE_PRE_PRODUCT_SERVICE + "material_process_create_time_idx", columnList = "createTime"),
        @Index(name = TableNameConstant.TABLE_PRE_PRODUCT_SERVICE + "material_process_process_type_idx", columnList = "processType"),
        @Index(name = TableNameConstant.TABLE_PRE_PRODUCT_SERVICE + "material_process_status_idx", columnList = "status"),
        @Index(name = TableNameConstant.TABLE_PRE_PRODUCT_SERVICE + "material_process_suitable_material_type_idx", columnList = "suitableMaterialType")
        }
)
public class MaterielProcessDO implements Serializable {
    private static final long serialVersionUID = -8557406912537496812L;

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 创建时间
     */
    @Column(columnDefinition = "timestamp")
    private LocalDateTime createTime;

    /**
     * 会员id
     */
    @Column
    private Long memberId;

    /**
     * 会员角色id
     */
    @Column
    private Long roleId;

    /**
     * 规则名称
     */
    @Column(columnDefinition = "varchar(100)")
    private String name;

    /**
     * 流程类型，
     * 定义在： MaterialProcessTypeEnum
     */
    @Column
    private Integer processType;

    /**
     * 流程key
     */
    @Column
    private String processKey;

    /**
     * 适用物料类型
     * 定义在： MaterialProcessSuitableTypeEnum
     */
    @Column
    private Integer suitableMaterialType;

    /**
     * 状态
     * 定义在： MaterialProcessStatusEnum
     */
    @Column
    private Integer status;

    @ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(name = "base_material_process_id", referencedColumnName = "id", foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    private BaseMaterielProcessDO baseMaterialProcess;

    /**
     * 多对多双向关联物料分组
     * 选中部分物料分组
     */
    @JsonManagedReference
    @ManyToMany
    @JoinTable(name = TableNameConstant.TABLE_PRE_PRODUCT_SERVICE + "material_process_rel_material_group",
            joinColumns = {@JoinColumn(name = "material_process_id", referencedColumnName = "id")},
            inverseJoinColumns = {@JoinColumn(name = "material_group_id", referencedColumnName = "id")},
            foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT),
            inverseForeignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)
    )
    private Set<MaterielGroupDO> materialGroups;

    /**
     * 多对多双向关联物料
     * 选中部分物料
     */
    @JsonManagedReference
    @ManyToMany
    @JoinTable(name = TableNameConstant.TABLE_PRE_PRODUCT_SERVICE + "material_process_rel_material",
            joinColumns = {@JoinColumn(name = "material_process_id", referencedColumnName = "id")},
            inverseJoinColumns = {@JoinColumn(name = "material_id", referencedColumnName = "id")},
            foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT),
            inverseForeignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)
    )
    private Set<MaterielDO> materials;
    /**
     * 来源 0.系统 1.Paas
     */
    @Column(columnDefinition = "int")
    private Integer source;

    /**
     * 是否默认流程 0.非默认 1.默认
     */
    @Column(columnDefinition = "int")
    private Integer isDefault;

    public void setId(Long id) {
        this.id = id;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setProcessType(Integer processType) {
        this.processType = processType;
    }

    public void setProcessKey(String processKey) {
        this.processKey = processKey;
    }

    public void setSuitableMaterialType(Integer suitableMaterialType) {
        this.suitableMaterialType = suitableMaterialType;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public void setBaseMaterialProcess(BaseMaterielProcessDO baseMaterialProcess) {
        this.baseMaterialProcess = baseMaterialProcess;
    }

    public void setMaterialGroups(Set<MaterielGroupDO> materialGroups) {
        this.materialGroups = materialGroups;
    }

    public void setMaterials(Set<MaterielDO> materials) {
        this.materials = materials;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }
}