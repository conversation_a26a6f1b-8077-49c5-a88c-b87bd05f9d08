package com.ssy.lingxi.product.model.resp.platform;

import java.io.Serializable;
import java.util.Objects;

/**
 * 平台后台-物料流程规则查询返回
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/3/25 14:37
 */
public class PlatformBaseMaterielProcessResp implements Serializable {
    private static final long serialVersionUID = 5918330109241542899L;

    /**
     * 流程规则id
     */
    private Long baseProcessId;

    /**
     * 流程规则名称
     */
    private String processName;

    /**
     * 流程规则类型: 1.新增物料 2.变更物料
     */
    private Integer processType;

    /**
     * 流程类型名称
     */
    private String processTypeName;

    /**
     * 流程说明
     */
    private String description;

    /**
     * 流程图片
     */
    private String processImage;

    /**
     * 引擎ID
     */
    private Long engineId;

    public Long getBaseProcessId() {
        return baseProcessId;
    }

    public void setBaseProcessId(Long baseProcessId) {
        this.baseProcessId = baseProcessId;
    }

    public String getProcessName() {
        return processName;
    }

    public void setProcessName(String processName) {
        this.processName = processName;
    }

    public Integer getProcessType() {
        return processType;
    }

    public void setProcessType(Integer processType) {
        this.processType = processType;
    }

    public String getProcessTypeName() {
        return processTypeName;
    }

    public void setProcessTypeName(String processTypeName) {
        this.processTypeName = processTypeName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getProcessImage() {
        return processImage;
    }

    public void setProcessImage(String processImage) {
        this.processImage = processImage;
    }

    public Long getEngineId() {
        return engineId;
    }

    public void setEngineId(Long engineId) {
        this.engineId = engineId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PlatformBaseMaterielProcessResp that = (PlatformBaseMaterielProcessResp) o;
        return Objects.equals(baseProcessId, that.baseProcessId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(baseProcessId);
    }
}
