package com.ssy.lingxi.product.enums;

/**
 * 是否所有会员共享枚举
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/29
 */
public enum IsAllMemberShareEnum {

    YES(1, "是"),
    NOT(0, "否");

    private final Integer code;
    private final String name;

    IsAllMemberShareEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
