package com.ssy.lingxi.product.controller.mobile;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.warehouse.WarehouseAddOrUpdateReq;
import com.ssy.lingxi.product.api.model.req.warehouse.WarehouseReq;
import com.ssy.lingxi.product.api.model.req.warehouse.WarehouseStartRoStopReq;
import com.ssy.lingxi.product.api.model.resp.warehouse.WarehouseResp;
import com.ssy.lingxi.product.service.warehouse.IWarehouseService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * APP-进销存-仓库管理
 * <AUTHOR>
 * @since 2020/7/20
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/mobile")
public class WarehouseModbileController extends BaseController {
    private final IWarehouseService warehouseService;

    /**
     * 移动端-查询仓库列表
     * @param request 参数
     */
    @GetMapping("/warehouse/list")
    public WrapperResp<PageDataResp<WarehouseResp>> warehouseList(WarehouseReq request) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(warehouseService.warehouseList(request, sysUser));
    }

    /**
     * 移动端-查询仓库详情
     * @param id 仓库id
     */
    @GetMapping("/warehouse/details")
    public WrapperResp<WarehouseResp> warehouseDetails(Long id) {
        return WrapperUtil.success(warehouseService.warehouseDetails(id));
    }

    /**
     * 移动端-查询全部仓库
     * @param httpServletRequest 参数
     */
    @GetMapping("/warehouse/all")
    public WrapperResp<List<WarehouseResp>> warehouseAll(HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        return WrapperUtil.success(warehouseService.warehouseAll(sysUser));
    }

    /**
     * 移动端-添加仓库/修改仓库
     * @param request 参数
     */
    @PostMapping("/warehouse/addOrUpdate")
    public WrapperResp<Void> warehouseAddOrUpdate(@RequestBody WarehouseAddOrUpdateReq request) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(warehouseService.warehouseAddOrUpdate(request, sysUser));
    }

    /**
     * 移动端-删除仓库
     * @param id 仓库id
     */
    @GetMapping("/warehouse/delete")
    public WrapperResp<Void> warehouseDelete(Long id) {
        return WrapperUtil.success(warehouseService.warehouseDelete(id));
    }

    /**
     * 移动端-启动停用仓库
     * @param request 参数
     */
    @PostMapping("/warehouse/startOrStop")
    public WrapperResp<Void> warehouseStartOrStop(@RequestBody WarehouseStartRoStopReq request) {
        return WrapperUtil.success(warehouseService.warehouseStartOrStop(request));
    }
}
