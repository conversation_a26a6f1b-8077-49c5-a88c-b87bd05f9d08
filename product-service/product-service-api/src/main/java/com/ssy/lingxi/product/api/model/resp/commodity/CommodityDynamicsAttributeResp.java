package com.ssy.lingxi.product.api.model.resp.commodity;

import lombok.Getter;
import lombok.Setter;

/**
 * 商品动态属性
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-14
 */
@Getter
@Setter
public class CommodityDynamicsAttributeResp {

    /**
     * 商品描述属性ID
     * CommodityDescribeAttributeDO
     */
    private Long describeAttributeId;

    /**
     * 商品描述属性名称
     */
    private String describeAttributeName;

    /**
     * 字段类型
     */
    private Integer fieldType;

    /**
     * 商品描述属性值（非输入）
     * CommodityDescribeAttributeValueDO
     */
    private Long[] describeAttributeValueIds;

    /**
     * 属性值（输入）
     */
    private String describeAttributeValue;
}
