package com.ssy.lingxi.product.api.model.req.commodity;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 平台
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/28
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CommonCommodityPlatformReq extends PageDataReq implements Serializable {
    private static final long serialVersionUID = 653183034031191946L;

    /**
     * 排除的id集合
     */
    private List<Long> idNotInList;

    /**
     * 商城id
     */
    private Long shopId;

    /**
     * 产品定价: 1-现货价格, 2-价格需要询价, 3-积分兑换商品, 4-赠品
     */
    private List<Integer> priceTypeList;

    /**
     * 商品编号/商品名称
     */
    private String name;

    /**
     * 平台后台分类id
     */
    private Long categoryId;

    /**
     * 品牌id
     */
    private Long brandId;

    /**
     * 供应会员
     */
    private String memberName;

    /**
     * 是否包括阶梯价格(不传代表包含)
     */
    private Boolean isMorePrice;
}
