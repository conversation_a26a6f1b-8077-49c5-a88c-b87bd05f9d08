package com.ssy.lingxi.workflow.api.fallback.factory;

import com.ssy.lingxi.workflow.api.fallback.ProcessFeignFallback;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 任务执行、流程步骤查询接口Fallback
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-04-30
 */
@Component
public class ProcessFeignFallbackFactory implements FallbackFactory<ProcessFeignFallback> {

    @Override
    public ProcessFeignFallback create(Throwable cause) {
        return new ProcessFeignFallback(cause);
    }

}
