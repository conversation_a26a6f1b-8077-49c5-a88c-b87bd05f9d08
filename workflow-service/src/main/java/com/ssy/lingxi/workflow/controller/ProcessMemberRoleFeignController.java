package com.ssy.lingxi.workflow.controller;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.workflow.api.feign.IProcessMemberRoleFeign;
import com.ssy.lingxi.workflow.api.model.req.ProcessMemberRoleReq;
import com.ssy.lingxi.workflow.api.model.req.ProcessRemoveMemberRoleReq;
import com.ssy.lingxi.workflow.service.IProcessRoleService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 流程步骤关联的会员自定义角色相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-04-28
 * @ignore 不需要提交到Yapi
 */
@RestController
public class ProcessMemberRoleFeignController implements IProcessMemberRoleFeign {
    @Resource
    private IProcessRoleService processRoleService;

    /**
     * 查询流程定义，关联流程步骤与会员自定义角色
     * @param processMemberRoleReq 接口参数
     * @return 操作结果
     */
    @Override
    public WrapperResp<Void> updateInsertProcessAsync(@RequestBody @Valid ProcessMemberRoleReq processMemberRoleReq) {
        synchronized (ProcessMemberRoleFeignController.class) {
            processRoleService.updateInsertMemberRoleAuth(processMemberRoleReq);
            return WrapperUtil.success();
        }
    }

    /**
     * 从关联的流程步骤中，移除会员自定义角色
     * @param processRemoveMemberRoleReq 接口参数
     * @return 异步操作结果
     */
    @Override
    public WrapperResp<Void> removeMemberRoleAsync(@RequestBody @Valid ProcessRemoveMemberRoleReq processRemoveMemberRoleReq) {
        synchronized (ProcessMemberRoleFeignController.class) {
            processRoleService.removeMemberRole(processRemoveMemberRoleReq);
            return WrapperUtil.success();
        }
    }
}
