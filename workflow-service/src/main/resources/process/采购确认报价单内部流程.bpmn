<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_0borj72" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.1.1">
  <bpmn:process id="purchase_confirm_offer_inner_verify" name="采购确认报价内部流程" isExecutable="true" camunda:versionTag="2.0.0">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="status" value="21" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_130hjf1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_130hjf1" sourceRef="StartEvent_1" targetRef="Activity_1bygqs6" />
    <bpmn:sequenceFlow id="Flow_17ckiri" sourceRef="Activity_1bygqs6" targetRef="Activity_0shos3y" />
    <bpmn:exclusiveGateway id="Gateway_1fljf0q">
      <bpmn:incoming>Flow_0ou2ntv</bpmn:incoming>
      <bpmn:outgoing>Flow_1jdnfe2</bpmn:outgoing>
      <bpmn:outgoing>Flow_04kjjl8</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0ou2ntv" sourceRef="Activity_0shos3y" targetRef="Gateway_1fljf0q" />
    <bpmn:sequenceFlow id="Flow_1jdnfe2" sourceRef="Gateway_1fljf0q" targetRef="Activity_177si2n">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="status" value="23" />
          <camunda:property name="oper" value="审核授标结果(一级)" />
          <camunda:property name="desc" value="待审核授标结果(二级)" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">#{agree == 1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_04kjjl8" name="审核授标不通过（一级)" sourceRef="Gateway_1fljf0q" targetRef="Activity_1bygqs6">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="status" value="25" />
          <camunda:property name="oper" value="审核授标结果(一级)" />
          <camunda:property name="desc" value="审核授标结果不通过(一级)" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">#{agree == 0}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0eyigh7">
      <bpmn:incoming>Flow_1us4ozy</bpmn:incoming>
      <bpmn:outgoing>Flow_0y5apfq</bpmn:outgoing>
      <bpmn:outgoing>Flow_0mjqrju</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1us4ozy" sourceRef="Activity_177si2n" targetRef="Gateway_0eyigh7" />
    <bpmn:sequenceFlow id="Flow_0y5apfq" name="审核授标不通过（二级）" sourceRef="Gateway_0eyigh7" targetRef="Activity_1bygqs6">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="status" value="26" />
          <camunda:property name="oper" value="审核授标结果(二级)" />
          <camunda:property name="desc" value="审核授标不通过(二级)" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">#{agree == 0}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_18ccfti" name="完成 99">
      <bpmn:incoming>Flow_0909ipv</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0909ipv" sourceRef="Activity_01ojvya" targetRef="Event_18ccfti" />
    <bpmn:userTask id="Activity_1bygqs6" name="待比价" camunda:assignee="pcofferiv1">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="step" value="21" />
          <camunda:property name="status" value="22" />
          <camunda:property name="desc" value="待审核授标结果(一级)" />
          <camunda:property name="oper" value="比价" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_130hjf1</bpmn:incoming>
      <bpmn:incoming>Flow_04kjjl8</bpmn:incoming>
      <bpmn:incoming>Flow_0y5apfq</bpmn:incoming>
      <bpmn:outgoing>Flow_17ckiri</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_0shos3y" name="待审核授标结果(一级)" camunda:assignee="pcofferiv2">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="step" value="22" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_17ckiri</bpmn:incoming>
      <bpmn:outgoing>Flow_0ou2ntv</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_177si2n" name="待审核授标结果(二级）" camunda:assignee="pcofferiv3">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="step" value="23" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1jdnfe2</bpmn:incoming>
      <bpmn:outgoing>Flow_1us4ozy</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_01ojvya" name="待确认授标结果" camunda:assignee="pcofferiv4">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="step" value="24" />
          <camunda:property name="status" value="99" />
          <camunda:property name="desc" value="已完成" />
          <camunda:property name="oper" value="确认授标结果" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0mjqrju</bpmn:incoming>
      <bpmn:outgoing>Flow_0909ipv</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0mjqrju" sourceRef="Gateway_0eyigh7" targetRef="Activity_01ojvya">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="status" value="24" />
          <camunda:property name="oper" value="审核授标结果(二级)" />
          <camunda:property name="desc" value="待确认授标结果" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">#{agree == 1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="purchase_confirm_offer_inner_verify">
      <bpmndi:BPMNEdge id="Flow_0909ipv_di" bpmnElement="Flow_0909ipv">
        <di:waypoint x="1090" y="247" />
        <di:waypoint x="1162" y="247" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0y5apfq_di" bpmnElement="Flow_0y5apfq">
        <di:waypoint x="900" y="222" />
        <di:waypoint x="900" y="130" />
        <di:waypoint x="320" y="130" />
        <di:waypoint x="320" y="207" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="571" y="86" width="78" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0mjqrju_di" bpmnElement="Flow_0mjqrju">
        <di:waypoint x="925" y="247" />
        <di:waypoint x="990" y="247" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1us4ozy_di" bpmnElement="Flow_1us4ozy">
        <di:waypoint x="810" y="247" />
        <di:waypoint x="875" y="247" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04kjjl8_di" bpmnElement="Flow_04kjjl8">
        <di:waypoint x="620" y="272" />
        <di:waypoint x="620" y="380" />
        <di:waypoint x="320" y="380" />
        <di:waypoint x="320" y="287" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="426" y="346" width="88" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jdnfe2_di" bpmnElement="Flow_1jdnfe2">
        <di:waypoint x="645" y="247" />
        <di:waypoint x="710" y="247" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ou2ntv_di" bpmnElement="Flow_0ou2ntv">
        <di:waypoint x="530" y="247" />
        <di:waypoint x="595" y="247" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_17ckiri_di" bpmnElement="Flow_17ckiri">
        <di:waypoint x="370" y="247" />
        <di:waypoint x="430" y="247" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_130hjf1_di" bpmnElement="Flow_130hjf1">
        <di:waypoint x="215" y="247" />
        <di:waypoint x="270" y="247" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="179" y="229" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1fljf0q_di" bpmnElement="Gateway_1fljf0q" isMarkerVisible="true">
        <dc:Bounds x="595" y="222" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0eyigh7_di" bpmnElement="Gateway_0eyigh7" isMarkerVisible="true">
        <dc:Bounds x="875" y="222" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_18ccfti_di" bpmnElement="Event_18ccfti">
        <dc:Bounds x="1162" y="229" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1162" y="272" width="37" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_06nqaa6_di" bpmnElement="Activity_1bygqs6">
        <dc:Bounds x="270" y="207" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1chd1sl_di" bpmnElement="Activity_0shos3y">
        <dc:Bounds x="430" y="207" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_07yehpk_di" bpmnElement="Activity_177si2n">
        <dc:Bounds x="710" y="207" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_06x9uw6_di" bpmnElement="Activity_01ojvya">
        <dc:Bounds x="990" y="207" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
