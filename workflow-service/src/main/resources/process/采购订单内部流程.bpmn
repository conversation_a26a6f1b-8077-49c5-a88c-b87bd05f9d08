<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_1h3ih66" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.1.1">
  <bpmn:process id="purchase_order_inner_verify" name="采购订单新增订单订单内部流转" isExecutable="true" camunda:versionTag="2.0.0">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="status" value="1" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_0l01w3i</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0l01w3i" sourceRef="StartEvent_1" targetRef="Activity_1adi0hx" />
    <bpmn:sequenceFlow id="Flow_0owekgg" sourceRef="Activity_1adi0hx" targetRef="Activity_1hfgqtg" />
    <bpmn:exclusiveGateway id="Gateway_1i7u2o8">
      <bpmn:incoming>Flow_1guoag3</bpmn:incoming>
      <bpmn:outgoing>Flow_0r691k6</bpmn:outgoing>
      <bpmn:outgoing>Flow_0v94yah</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1guoag3" sourceRef="Activity_1hfgqtg" targetRef="Gateway_1i7u2o8" />
    <bpmn:sequenceFlow id="Flow_0r691k6" sourceRef="Gateway_1i7u2o8" targetRef="Activity_0queq23">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="status" value="3" />
          <camunda:property name="desc" value="待审核订单(二级)" />
          <camunda:property name="oper" value="审核订单" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">#{agree ==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1nx805b">
      <bpmn:incoming>Flow_0ppos6v</bpmn:incoming>
      <bpmn:outgoing>Flow_1b7i57q</bpmn:outgoing>
      <bpmn:outgoing>Flow_1t7e7nf</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0ppos6v" sourceRef="Activity_0queq23" targetRef="Gateway_1nx805b" />
    <bpmn:sequenceFlow id="Flow_1b7i57q" sourceRef="Gateway_1nx805b" targetRef="Activity_12rvi2p">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="status" value="4" />
          <camunda:property name="desc" value="待确认订单" />
          <camunda:property name="oper" value="审核订单" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">#{agree ==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_1xp323s" name="5">
      <bpmn:incoming>Flow_0zpy3gx</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0zpy3gx" sourceRef="Activity_12rvi2p" targetRef="Event_1xp323s" />
    <bpmn:sequenceFlow id="Flow_0v94yah" name="审核不通过" sourceRef="Gateway_1i7u2o8" targetRef="Activity_1adi0hx">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="status" value="6" />
          <camunda:property name="desc" value="审核不通过(一级)" />
          <camunda:property name="oper" value="审核订单" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">#{agree ==0}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1t7e7nf" name="审核不通过" sourceRef="Gateway_1nx805b" targetRef="Activity_1adi0hx">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="status" value="7" />
          <camunda:property name="desc" value="审核不通过(二级)" />
          <camunda:property name="oper" value="审核订单" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">#{agree ==0}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_1adi0hx" name="新增订单" camunda:assignee="poiv1">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="step" value="1" />
          <camunda:property name="status" value="2" />
          <camunda:property name="desc" value="待审核订单(一级)" />
          <camunda:property name="oper" value="新增订单" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0l01w3i</bpmn:incoming>
      <bpmn:incoming>Flow_0v94yah</bpmn:incoming>
      <bpmn:incoming>Flow_1t7e7nf</bpmn:incoming>
      <bpmn:outgoing>Flow_0owekgg</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_1hfgqtg" name="审核订单(一级)" camunda:assignee="poiv2">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="step" value="2" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0owekgg</bpmn:incoming>
      <bpmn:outgoing>Flow_1guoag3</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_0queq23" name="审核订单(二级)" camunda:assignee="poiv3">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="step" value="3" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0r691k6</bpmn:incoming>
      <bpmn:outgoing>Flow_0ppos6v</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_12rvi2p" name="提交订单" camunda:assignee="poiv4">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="step" value="4" />
          <camunda:property name="desc" value="已完成" />
          <camunda:property name="status" value="5" />
          <camunda:property name="oper" value="确认订单" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1b7i57q</bpmn:incoming>
      <bpmn:outgoing>Flow_0zpy3gx</bpmn:outgoing>
    </bpmn:userTask>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="purchase_order_inner_verify">
      <bpmndi:BPMNEdge id="Flow_1t7e7nf_di" bpmnElement="Flow_1t7e7nf">
        <di:waypoint x="900" y="152" />
        <di:waypoint x="900" y="80" />
        <di:waypoint x="320" y="80" />
        <di:waypoint x="320" y="137" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="583" y="62" width="56" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0v94yah_di" bpmnElement="Flow_0v94yah">
        <di:waypoint x="620" y="202" />
        <di:waypoint x="620" y="300" />
        <di:waypoint x="320" y="300" />
        <di:waypoint x="320" y="217" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="443" y="282" width="56" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0zpy3gx_di" bpmnElement="Flow_0zpy3gx">
        <di:waypoint x="1090" y="177" />
        <di:waypoint x="1162" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1b7i57q_di" bpmnElement="Flow_1b7i57q">
        <di:waypoint x="925" y="177" />
        <di:waypoint x="990" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ppos6v_di" bpmnElement="Flow_0ppos6v">
        <di:waypoint x="810" y="177" />
        <di:waypoint x="875" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0r691k6_di" bpmnElement="Flow_0r691k6">
        <di:waypoint x="645" y="177" />
        <di:waypoint x="710" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1guoag3_di" bpmnElement="Flow_1guoag3">
        <di:waypoint x="530" y="177" />
        <di:waypoint x="595" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0owekgg_di" bpmnElement="Flow_0owekgg">
        <di:waypoint x="370" y="177" />
        <di:waypoint x="430" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0l01w3i_di" bpmnElement="Flow_0l01w3i">
        <di:waypoint x="215" y="177" />
        <di:waypoint x="270" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="179" y="159" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1i7u2o8_di" bpmnElement="Gateway_1i7u2o8" isMarkerVisible="true">
        <dc:Bounds x="595" y="152" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1nx805b_di" bpmnElement="Gateway_1nx805b" isMarkerVisible="true">
        <dc:Bounds x="875" y="152" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1xp323s_di" bpmnElement="Event_1xp323s">
        <dc:Bounds x="1162" y="159" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1177" y="202" width="7" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0pkrign_di" bpmnElement="Activity_1adi0hx">
        <dc:Bounds x="270" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_16b3zx1_di" bpmnElement="Activity_1hfgqtg">
        <dc:Bounds x="430" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0jm0jzu_di" bpmnElement="Activity_0queq23">
        <dc:Bounds x="710" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1ljll66_di" bpmnElement="Activity_12rvi2p">
        <dc:Bounds x="990" y="137" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
