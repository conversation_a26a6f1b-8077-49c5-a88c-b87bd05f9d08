<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_087lrd6" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.6.0">
  <bpmn:process id="after_sale_replace_goods_outer_verify_offline_logistics_ext" name="售后换货外部审核流转" isExecutable="true" camunda:versionTag="2.0.0">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="status" value="1" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_1m6d5fj</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Activity_18ozati" name="1：采购商提交换货申请单" camunda:assignee="asrrgov1">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="step" value="1" />
          <camunda:property name="roletype" value="2" />
          <camunda:property name="status" value="2" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1m6d5fj</bpmn:incoming>
      <bpmn:outgoing>Flow_0qgejgj</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_1hdtq90" name="2：供应商确认换货申请单" camunda:assignee="asrrgov2">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="step" value="2" />
          <camunda:property name="roletype" value="1" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0qgejgj</bpmn:incoming>
      <bpmn:outgoing>Flow_055umhg</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_1itv4uk" name="3：采购商新增退货发货单" camunda:assignee="asrrgov3">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="step" value="3" />
          <camunda:property name="roletype" value="2" />
          <camunda:property name="status" value="7" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1as1fsq</bpmn:incoming>
      <bpmn:incoming>Flow_0kmdtk7</bpmn:incoming>
      <bpmn:outgoing>Flow_1dfob8p</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_0u28mp9" name="5：采购商退货发货" camunda:assignee="asrrgov5">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="step" value="5" />
          <camunda:property name="roletype" value="2" />
          <camunda:property name="status" value="8" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1dfob8p</bpmn:incoming>
      <bpmn:outgoing>Flow_0ez2fep</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_09ubbhs" name="6：供应商新增退货收货单" camunda:assignee="asrrgov6">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="step" value="6" />
          <camunda:property name="roletype" value="1" />
          <camunda:property name="status" value="9" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0ez2fep</bpmn:incoming>
      <bpmn:outgoing>Flow_0xhsje4</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_1ezot4t" name="7：供应商退货收货" camunda:assignee="asrrgov7">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="step" value="7" />
          <camunda:property name="roletype" value="1" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0xhsje4</bpmn:incoming>
      <bpmn:outgoing>Flow_0ntbci2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_102cqr9" name="9：供应商新增换货发货单" camunda:assignee="asrrgov9">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="step" value="9" />
          <camunda:property name="roletype" value="1" />
          <camunda:property name="status" value="13" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0lv99hk</bpmn:incoming>
      <bpmn:incoming>Flow_1gfsouc</bpmn:incoming>
      <bpmn:incoming>Flow_1pawle0</bpmn:incoming>
      <bpmn:outgoing>Flow_0i9wbnm</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_18sszu3" name="11：供应商换货发货" camunda:assignee="asrrgov11">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="step" value="11" />
          <camunda:property name="roletype" value="1" />
          <camunda:property name="status" value="14" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0i9wbnm</bpmn:incoming>
      <bpmn:outgoing>Flow_0hwjyxi</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_186608g" name="12：采购商新增换货收货单" camunda:assignee="asrrgov12">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="step" value="12" />
          <camunda:property name="roletype" value="2" />
          <camunda:property name="status" value="15" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0hwjyxi</bpmn:incoming>
      <bpmn:outgoing>Flow_0jkbm7k</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_0ue7vhu" name="13：采购商换货收货" camunda:assignee="asrrgov13">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="step" value="13" />
          <camunda:property name="roletype" value="2" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0jkbm7k</bpmn:incoming>
      <bpmn:outgoing>Flow_0qe457s</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_1ou2yh2" name="15：采购商确认售后完成" camunda:assignee="asrrgov15">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="step" value="15" />
          <camunda:property name="roletype" value="2" />
          <camunda:property name="status" value="18" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1r2jknb</bpmn:incoming>
      <bpmn:outgoing>Flow_0k0yxl0</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="Event_0nl85lh" name="售后完成-18">
      <bpmn:incoming>Flow_0k0yxl0</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0ez2fep" name="退货发货-8" sourceRef="Activity_0u28mp9" targetRef="Activity_09ubbhs" />
    <bpmn:sequenceFlow id="Flow_0xhsje4" name="新增换货发货单-9" sourceRef="Activity_09ubbhs" targetRef="Activity_1ezot4t" />
    <bpmn:sequenceFlow id="Flow_0ntbci2" sourceRef="Activity_1ezot4t" targetRef="Gateway_0mr168z" />
    <bpmn:exclusiveGateway id="Gateway_0mr168z">
      <bpmn:incoming>Flow_0ntbci2</bpmn:incoming>
      <bpmn:outgoing>Flow_0lv99hk</bpmn:outgoing>
      <bpmn:outgoing>Flow_1as1fsq</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0lv99hk" name="全部已退货-11" sourceRef="Gateway_0mr168z" targetRef="Activity_102cqr9">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="status" value="11" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">#{agree ==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1as1fsq" name="部分已退货-5" sourceRef="Gateway_0mr168z" targetRef="Activity_1itv4uk">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="status" value="5" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">#{agree ==0}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0hwjyxi" name="换货发货-14" sourceRef="Activity_18sszu3" targetRef="Activity_186608g" />
    <bpmn:sequenceFlow id="Flow_0jkbm7k" name="新增换货入库单-15" sourceRef="Activity_186608g" targetRef="Activity_0ue7vhu" />
    <bpmn:sequenceFlow id="Flow_0qe457s" sourceRef="Activity_0ue7vhu" targetRef="Gateway_1f7s0na" />
    <bpmn:exclusiveGateway id="Gateway_1f7s0na">
      <bpmn:incoming>Flow_0qe457s</bpmn:incoming>
      <bpmn:outgoing>Flow_1gfsouc</bpmn:outgoing>
      <bpmn:outgoing>Flow_1r2jknb</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1gfsouc" name="部分已换货-11" sourceRef="Gateway_1f7s0na" targetRef="Activity_102cqr9">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="status" value="11" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">#{agree ==0}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1r2jknb" name="全部已换货-17" sourceRef="Gateway_1f7s0na" targetRef="Activity_1ou2yh2">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="status" value="17" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">#{agree ==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0k0yxl0" sourceRef="Activity_1ou2yh2" targetRef="Event_0nl85lh" />
    <bpmn:sequenceFlow id="Flow_1m6d5fj" sourceRef="StartEvent_1" targetRef="Activity_18ozati" />
    <bpmn:exclusiveGateway id="Gateway_0fqu5om">
      <bpmn:incoming>Flow_055umhg</bpmn:incoming>
      <bpmn:outgoing>Flow_0kmdtk7</bpmn:outgoing>
      <bpmn:outgoing>Flow_1pawle0</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0kmdtk7" name="需要退货-5" sourceRef="Gateway_0fqu5om" targetRef="Activity_1itv4uk">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="status" value="5" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">#{agree ==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1pawle0" name="不需要退货-11" sourceRef="Gateway_0fqu5om" targetRef="Activity_102cqr9">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="status" value="11" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">#{agree ==0}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0i9wbnm" name="线下物流-13" sourceRef="Activity_102cqr9" targetRef="Activity_18sszu3" />
    <bpmn:sequenceFlow id="Flow_055umhg" sourceRef="Activity_1hdtq90" targetRef="Gateway_0fqu5om" />
    <bpmn:sequenceFlow id="Flow_0qgejgj" sourceRef="Activity_18ozati" targetRef="Activity_1hdtq90" />
    <bpmn:sequenceFlow id="Flow_1dfob8p" name="待退货发货-7" sourceRef="Activity_1itv4uk" targetRef="Activity_0u28mp9" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="after_sale_replace_goods_outer_verify_offline_logistics_ext">
      <bpmndi:BPMNEdge id="Flow_0qgejgj_di" bpmnElement="Flow_0qgejgj">
        <di:waypoint x="377" y="120" />
        <di:waypoint x="440" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_055umhg_di" bpmnElement="Flow_055umhg">
        <di:waypoint x="540" y="120" />
        <di:waypoint x="585" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0i9wbnm_di" bpmnElement="Flow_0i9wbnm">
        <di:waypoint x="1400" y="446" />
        <di:waypoint x="1199" y="446" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1274" y="425" width="60" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1pawle0_di" bpmnElement="Flow_1pawle0">
        <di:waypoint x="610" y="145" />
        <di:waypoint x="610" y="320" />
        <di:waypoint x="1450" y="320" />
        <di:waypoint x="1450" y="406" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="966" y="303" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0kmdtk7_di" bpmnElement="Flow_0kmdtk7">
        <di:waypoint x="635" y="120" />
        <di:waypoint x="727" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="654" y="102" width="55" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1m6d5fj_di" bpmnElement="Flow_1m6d5fj">
        <di:waypoint x="196" y="120" />
        <di:waypoint x="277" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0hwjyxi_di" bpmnElement="Flow_0hwjyxi">
        <di:waypoint x="1099" y="446" />
        <di:waypoint x="910" y="446" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="965" y="423" width="60" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1as1fsq_di" bpmnElement="Flow_1as1fsq">
        <di:waypoint x="1515" y="240" />
        <di:waypoint x="777" y="240" />
        <di:waypoint x="777" y="160" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1124" y="222" width="65" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lv99hk_di" bpmnElement="Flow_0lv99hk">
        <di:waypoint x="1540" y="265" />
        <di:waypoint x="1540" y="446" />
        <di:waypoint x="1500" y="446" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1545" y="349" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ntbci2_di" bpmnElement="Flow_0ntbci2">
        <di:waypoint x="1467" y="120" />
        <di:waypoint x="1540" y="120" />
        <di:waypoint x="1540" y="215" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1754" y="102" width="60" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1dfob8p_di" bpmnElement="Flow_1dfob8p">
        <di:waypoint x="827" y="120" />
        <di:waypoint x="967" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="865" y="102" width="65" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ez2fep_di" bpmnElement="Flow_0ez2fep">
        <di:waypoint x="1067" y="120" />
        <di:waypoint x="1152" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1083" y="102" width="54" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xhsje4_di" bpmnElement="Flow_0xhsje4">
        <di:waypoint x="1252" y="120" />
        <di:waypoint x="1367" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1267" y="102" width="87" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0k0yxl0_di" bpmnElement="Flow_0k0yxl0">
        <di:waypoint x="440" y="732" />
        <di:waypoint x="562" y="732" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="650" y="842" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1r2jknb_di" bpmnElement="Flow_1r2jknb">
        <di:waypoint x="390" y="607" />
        <di:waypoint x="390" y="692" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="395" y="648" width="71" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0jkbm7k_di" bpmnElement="Flow_0jkbm7k">
        <di:waypoint x="810" y="446" />
        <di:waypoint x="675" y="446" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="703" y="421" width="81" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0qe457s_di" bpmnElement="Flow_0qe457s">
        <di:waypoint x="575" y="446" />
        <di:waypoint x="390" y="446" />
        <di:waypoint x="390" y="557" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="483" y="476" width="50" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1gfsouc_di" bpmnElement="Flow_1gfsouc">
        <di:waypoint x="415" y="582" />
        <di:waypoint x="1450" y="582" />
        <di:waypoint x="1450" y="486" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="944" y="554" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="160" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1oguduk_di" bpmnElement="Activity_18ozati">
        <dc:Bounds x="277" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1hdtq90_di" bpmnElement="Activity_1hdtq90">
        <dc:Bounds x="440" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1itv4uk_di" bpmnElement="Activity_1itv4uk">
        <dc:Bounds x="727" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0fqu5om_di" bpmnElement="Gateway_0fqu5om" isMarkerVisible="true">
        <dc:Bounds x="585" y="95" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0u28mp9_di" bpmnElement="Activity_0u28mp9">
        <dc:Bounds x="967" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_09ubbhs_di" bpmnElement="Activity_09ubbhs">
        <dc:Bounds x="1152" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1ezot4t_di" bpmnElement="Activity_1ezot4t">
        <dc:Bounds x="1367" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0mr168z_di" bpmnElement="Gateway_0mr168z" isMarkerVisible="true">
        <dc:Bounds x="1515" y="215" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0nl85lh_di" bpmnElement="Event_0nl85lh">
        <dc:Bounds x="562" y="714" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="551" y="757" width="60" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1ou2yh2_di" bpmnElement="Activity_1ou2yh2">
        <dc:Bounds x="340" y="692" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ue7vhu_di" bpmnElement="Activity_0ue7vhu">
        <dc:Bounds x="575" y="406" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_186608g_di" bpmnElement="Activity_186608g">
        <dc:Bounds x="810" y="406" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_102cqr9_di" bpmnElement="Activity_102cqr9">
        <dc:Bounds x="1400" y="406" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1f7s0na_di" bpmnElement="Gateway_1f7s0na" isMarkerVisible="true">
        <dc:Bounds x="365" y="557" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_18sszu3_di" bpmnElement="Activity_18sszu3">
        <dc:Bounds x="1099" y="406" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
