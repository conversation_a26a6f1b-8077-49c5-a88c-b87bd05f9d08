<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_0a3lgql" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.11.1">
  <bpmn:process id="quality_manage_pca_verify_1" name="质量管理PCA审核流程1级" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="status" value="16" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_0vukd2x</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="Event_1bn3ce1">
      <bpmn:incoming>Flow_19cp0lu</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0vukd2x" sourceRef="StartEvent_1" targetRef="Activity_0z3vi9z" />
    <bpmn:sequenceFlow id="Flow_19cp0lu" sourceRef="Activity_0z3vi9z" targetRef="Event_1bn3ce1" />
    <bpmn:userTask id="Activity_0z3vi9z" name="确认PCA反馈">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="step" value="4" />
          <camunda:property name="status" value="99" />
          <camunda:property name="desc" value="已通过" />
          <camunda:property name="oper" value="确认PCA反馈" />
          <camunda:property name="roletype" value="2" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0vukd2x</bpmn:incoming>
      <bpmn:outgoing>Flow_19cp0lu</bpmn:outgoing>
    </bpmn:userTask>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="quality_manage_pca_verify_1">
      <bpmndi:BPMNEdge id="Flow_19cp0lu_di" bpmnElement="Flow_19cp0lu">
        <di:waypoint x="410" y="117" />
        <di:waypoint x="522" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vukd2x_di" bpmnElement="Flow_0vukd2x">
        <di:waypoint x="215" y="117" />
        <di:waypoint x="310" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="179" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1bn3ce1_di" bpmnElement="Event_1bn3ce1">
        <dc:Bounds x="522" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_09wvcag_di" bpmnElement="Activity_0z3vi9z">
        <dc:Bounds x="310" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
