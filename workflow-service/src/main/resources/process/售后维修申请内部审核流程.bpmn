<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1ev5zoo" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.6.0">
  <bpmn:process id="after_sale_repair_inner_verify" name="售后能力维修申请内部审核流转" isExecutable="true" camunda:versionTag="2.0.0">
    <bpmn:startEvent id="Event_0d40qnk">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="status" value="1" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_16gd147</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Ac" name="提交审核维修申请单" camunda:assignee="asriv1">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="step" value="1" />
          <camunda:property name="url" value="/memberCenter/afterService/repairManage/repairPrSubmit" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_16gd147</bpmn:incoming>
      <bpmn:outgoing>Flow_1me4zm7</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="Gateway_0yi8zwu" name="审核结果">
      <bpmn:incoming>Flow_1me4zm7</bpmn:incoming>
      <bpmn:outgoing>Flow_1nt092m</bpmn:outgoing>
      <bpmn:outgoing>Flow_1td3v5q</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:userTask id="Activity_0txmsmw" name="审核维修申请单(一级)" camunda:assignee="asriv2">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="step" value="2" />
          <camunda:property name="url" value="/memberCenter/afterService/repairManage/repairPr1" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1nt092m</bpmn:incoming>
      <bpmn:outgoing>Flow_1axwphb</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="Gateway_087nuba">
      <bpmn:incoming>Flow_1axwphb</bpmn:incoming>
      <bpmn:outgoing>Flow_0kctgdc</bpmn:outgoing>
      <bpmn:outgoing>Flow_0jw67yx</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:userTask id="Activity_000jbet" name="审核维修申请单(二级)" camunda:assignee="asriv3">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="step" value="3" />
          <camunda:property name="url" value="/memberCenter/afterService/repairManage/repairPr2" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0kctgdc</bpmn:incoming>
      <bpmn:outgoing>Flow_19na4u7</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_1jp50m1" name="确认维修申请单" camunda:assignee="asriv4">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="step" value="4" />
          <camunda:property name="url" value="/memberCenter/afterService/repairManage/repairPrConfirm" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1td3v5q</bpmn:incoming>
      <bpmn:incoming>Flow_0jw67yx</bpmn:incoming>
      <bpmn:incoming>Flow_1oy9eg1</bpmn:incoming>
      <bpmn:incoming>Flow_0svffwd</bpmn:incoming>
      <bpmn:outgoing>Flow_1bdmwmg</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="Gateway_14knpvz">
      <bpmn:incoming>Flow_1bdmwmg</bpmn:incoming>
      <bpmn:outgoing>Flow_1qmpsr7</bpmn:outgoing>
      <bpmn:outgoing>Flow_13ilyji</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0y36xmz">
      <bpmn:incoming>Flow_1qmpsr7</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:endEvent id="Event_1x49chq">
      <bpmn:incoming>Flow_13ilyji</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_16gd147" sourceRef="Event_0d40qnk" targetRef="Ac" />
    <bpmn:sequenceFlow id="Flow_1me4zm7" sourceRef="Ac" targetRef="Gateway_0yi8zwu" />
    <bpmn:sequenceFlow id="Flow_1nt092m" name="通过-2" sourceRef="Gateway_0yi8zwu" targetRef="Activity_0txmsmw">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="status" value="2" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">#{agree ==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1td3v5q" name="不通过-9" sourceRef="Gateway_0yi8zwu" targetRef="Activity_1jp50m1">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="status" value="9" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">#{agree ==0}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1axwphb" sourceRef="Activity_0txmsmw" targetRef="Gateway_087nuba" />
    <bpmn:sequenceFlow id="Flow_0kctgdc" name="通过3" sourceRef="Gateway_087nuba" targetRef="Activity_000jbet">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="status" value="3" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">#{agree ==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0jw67yx" name="不通过-10" sourceRef="Gateway_087nuba" targetRef="Activity_1jp50m1">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="status" value="10" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">#{agree ==0}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1bdmwmg" sourceRef="Activity_1jp50m1" targetRef="Gateway_14knpvz" />
    <bpmn:sequenceFlow id="Flow_1qmpsr7" name="审核通过-6" sourceRef="Gateway_14knpvz" targetRef="Event_0y36xmz">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="status" value="6" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">#{agree ==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_13ilyji" name="审核不通过-7" sourceRef="Gateway_14knpvz" targetRef="Event_1x49chq">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="status" value="7" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">#{agree ==0}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0466ogv">
      <bpmn:incoming>Flow_19na4u7</bpmn:incoming>
      <bpmn:outgoing>Flow_1oy9eg1</bpmn:outgoing>
      <bpmn:outgoing>Flow_0svffwd</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1oy9eg1" name="不通过-5" sourceRef="Gateway_0466ogv" targetRef="Activity_1jp50m1">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="status" value="5" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">#{agree ==0}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0svffwd" name="通过-4" sourceRef="Gateway_0466ogv" targetRef="Activity_1jp50m1">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="status" value="4" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">#{agree ==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_19na4u7" sourceRef="Activity_000jbet" targetRef="Gateway_0466ogv" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="after_sale_repair_inner_verify">
      <bpmndi:BPMNEdge id="Flow_19na4u7_di" bpmnElement="Flow_19na4u7">
        <di:waypoint x="930" y="220" />
        <di:waypoint x="1005" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0svffwd_di" bpmnElement="Flow_0svffwd">
        <di:waypoint x="1055" y="220" />
        <di:waypoint x="1179" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1101" y="202" width="32" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1oy9eg1_di" bpmnElement="Flow_1oy9eg1">
        <di:waypoint x="1030" y="245" />
        <di:waypoint x="1030" y="290" />
        <di:waypoint x="1209" y="290" />
        <di:waypoint x="1209" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1099" y="272" width="42" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13ilyji_di" bpmnElement="Flow_13ilyji">
        <di:waypoint x="1380" y="245" />
        <di:waypoint x="1380" y="290" />
        <di:waypoint x="1502" y="290" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1410" y="301" width="65" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1qmpsr7_di" bpmnElement="Flow_1qmpsr7">
        <di:waypoint x="1380" y="195" />
        <di:waypoint x="1380" y="170" />
        <di:waypoint x="1502" y="170" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1425" y="150" width="54" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1bdmwmg_di" bpmnElement="Flow_1bdmwmg">
        <di:waypoint x="1279" y="220" />
        <di:waypoint x="1355" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0jw67yx_di" bpmnElement="Flow_0jw67yx">
        <di:waypoint x="726" y="195" />
        <di:waypoint x="726" y="100" />
        <di:waypoint x="1229" y="100" />
        <di:waypoint x="1229" y="180" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="968" y="82" width="48" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0kctgdc_di" bpmnElement="Flow_0kctgdc">
        <di:waypoint x="751" y="220" />
        <di:waypoint x="830" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="772" y="202" width="28" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1axwphb_di" bpmnElement="Flow_1axwphb">
        <di:waypoint x="644" y="220" />
        <di:waypoint x="701" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1td3v5q_di" bpmnElement="Flow_1td3v5q">
        <di:waypoint x="447" y="245" />
        <di:waypoint x="447" y="360" />
        <di:waypoint x="1229" y="360" />
        <di:waypoint x="1229" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="818" y="342" width="42" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1nt092m_di" bpmnElement="Flow_1nt092m">
        <di:waypoint x="472" y="220" />
        <di:waypoint x="544" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="494" y="202" width="32" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1me4zm7_di" bpmnElement="Flow_1me4zm7">
        <di:waypoint x="370" y="220" />
        <di:waypoint x="422" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_16gd147_di" bpmnElement="Flow_16gd147">
        <di:waypoint x="188" y="220" />
        <di:waypoint x="270" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_0d40qnk_di" bpmnElement="Event_0d40qnk">
        <dc:Bounds x="152" y="202" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0r366dy_di" bpmnElement="Ac">
        <dc:Bounds x="270" y="180" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0yi8zwu_di" bpmnElement="Gateway_0yi8zwu" isMarkerVisible="true">
        <dc:Bounds x="422" y="195" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="424" y="171" width="45" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0txmsmw_di" bpmnElement="Activity_0txmsmw">
        <dc:Bounds x="544" y="180" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_087nuba_di" bpmnElement="Gateway_087nuba" isMarkerVisible="true">
        <dc:Bounds x="701" y="195" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_000jbet_di" bpmnElement="Activity_000jbet">
        <dc:Bounds x="830" y="180" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1jp50m1_di" bpmnElement="Activity_1jp50m1">
        <dc:Bounds x="1179" y="180" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_14knpvz_di" bpmnElement="Gateway_14knpvz" isMarkerVisible="true">
        <dc:Bounds x="1355" y="195" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1415" y="213" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0y36xmz_di" bpmnElement="Event_0y36xmz">
        <dc:Bounds x="1502" y="152" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1x49chq_di" bpmnElement="Event_1x49chq">
        <dc:Bounds x="1502" y="272" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0466ogv_di" bpmnElement="Gateway_0466ogv" isMarkerVisible="true">
        <dc:Bounds x="1005" y="195" width="50" height="50" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
