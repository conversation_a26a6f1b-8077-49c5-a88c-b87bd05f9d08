package com.ssy.lingxi.aftersales.api.model.req;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * 允许支付VO
 * <AUTHOR>
 * @since 2021/1/28
 * @version 2.0.0
 */
public class AllowPayReq implements Serializable {

    private static final long serialVersionUID = 2710658145572347363L;

    /**
     * 订单id
     */
    @NotNull(message = "订单id要大于0")
    @Positive(message = "订单id要大于0")
    private Long orderId;

    /**
     * 支付id
     */
    @NotNull(message = "支付id要大于0")
    @Positive(message = "支付id要大于0")
    private Long payId;

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getPayId() {
        return payId;
    }

    public void setPayId(Long payId) {
        this.payId = payId;
    }
}
