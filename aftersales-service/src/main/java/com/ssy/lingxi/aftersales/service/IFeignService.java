package com.ssy.lingxi.aftersales.service;

import com.ssy.lingxi.aftersales.entity.do_.RepairGoodsDO;
import com.ssy.lingxi.aftersales.entity.do_.RepairGoodsDetailDO;
import com.ssy.lingxi.aftersales.model.dto.MemberCategoryDTO;
import com.ssy.lingxi.aftersales.model.dto.OrderProductCategoryDTO;
import com.ssy.lingxi.aftersales.model.req.ReplaceGoodsSaveReq;
import com.ssy.lingxi.aftersales.model.req.ReturnGoodsSaveReq;
import com.ssy.lingxi.common.model.dto.MemberAndRoleIdDTO;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.model.resp.engine.RuleEngineConfigResp;
import com.ssy.lingxi.component.base.enums.order.OrderTradeProcessTypeEnum;
import com.ssy.lingxi.contract.api.model.req.ContractExecuteInfoAddReq;
import com.ssy.lingxi.engine.api.model.req.RuleEngineByPredicateReq;
import com.ssy.lingxi.logistics.api.model.resp.LogisticsOrderFeignResp;
import com.ssy.lingxi.marketing.api.model.request.GoodsSalesReq;
import com.ssy.lingxi.member.api.model.req.MemberFeignAfterSaleCommentReq;
import com.ssy.lingxi.order.api.model.req.*;
import com.ssy.lingxi.order.api.model.resp.*;
import com.ssy.lingxi.pay.api.model.req.assetAccount.BalanceRefundReq;
import com.ssy.lingxi.pay.api.model.req.ccb.B2bRefundReq;
import com.ssy.lingxi.pay.api.model.req.ccb.DigitalRefundReq;
import com.ssy.lingxi.pay.api.model.req.eAccount.EAccountRefundReq;
import com.ssy.lingxi.pay.api.model.resp.allInPay.RefundResp;
import com.ssy.lingxi.settlement.api.model.req.SettlementOrderReq;
import org.springframework.scheduling.annotation.Async;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 内部接口服务
 * <AUTHOR>
 * @since 2020/11/21
 * @version 2.0.0
 */
public interface IFeignService {
    /**
     * 发送系统通知
     * @param memberId 会员id
     * @param roleId 角色id
     * @param messageNotice 留言通知
     * @param param 参数
     */
    @Async
    void sendSystemMessage(Long memberId,Long roleId,String messageNotice,String... param);

    /**
     * 更新订单商品明细
     * <AUTHOR>
     * @since 2020/12/11
     * @param afterSales: 请求参数
     * @return : 操作结果
     **/
    WrapperResp<?> orderProductDetailsUpdate(List<OrderAfterSaleReq> afterSales);

    /**
     * 新增会员评价
     * @param commentVO 会员售后评价VO
     */
    void addMemberEvaluate(MemberFeignAfterSaleCommentReq commentVO);

    /**
     * 根据订单id与商品id，查询订单商品相关信息
     * @param orderProductIds 根据订单id与商品id，
     * @return 订单商品相关信息
     */
    WrapperResp<List<OrderAfterSaleProductFeignDetailResp>> afterSalesListById(List<OrderProductIdFeignReq> orderProductIds);

    /**
     * 根据订单ids，查询订单支付记录
     * @param orderIds 订单ids
     * @return 订单支付记录
     */
    WrapperResp<List<OrderAfterSalePaymentFeignDetailResp>> getOrderPayInfo(List<Long> orderIds);

    /**
     * 查询采购流程规则
     * @param feignVO 查询采购流程配置接口参数VO
     * @return 采购流程规则
     */
    WrapperResp<OrderPurchaseProcessFeignDetailResp> getPurchaseProcess(OrderPurchaseProcessFeignReq feignVO);

    /**
     * 查询交易流程规则
     * @param feignVO 查询交易流程配置接口参数VO
     * @return 交易流程规则
     */
    WrapperResp<OrderTradeProcessFeignDetailResp> getTradeProcess(OrderTradeProcessFeignReq feignVO);

    /**
     * 校验交易工作流
     * @param memberId 会员id
     * @param roleId 角色id
     * @param tradeProcessTypeEnum 交易流程类型枚举
     * @param skuIds skuIdS
     * @param shopId 店铺编号
     */
    void checkTradeProcess(Long memberId, Long roleId, OrderTradeProcessTypeEnum tradeProcessTypeEnum, List<Long> skuIds, Long shopId);

    /**
     * 批量查询物流单信息
     * @param logisticsIds 物流编号集合
     * @return Map 物流单信息
     */
    Map<Long, LogisticsOrderFeignResp> listLogisticsOrderMap(List<Long> logisticsIds);

    /**
     * 查询订单是否需要开票.
     * @param orderNo 订单编码
     * @return 订单是否需要开票
     */
    WrapperResp<OrderSettleAccountInvoiceFeignDetailResp> findSettleAccountInvoiceDetail(String orderNo);

    /**
     * 更新活动商品销量与会员已购数.
     * @param req 活动商品销量更新
     * @return 更新活动商品销量与会员已购数
     */
    WrapperResp<?> updateActivitySkuSales(GoodsSalesReq req);

    /**
     * 支付宝退款
     * @param memberId 会员id
     * @param roleId 角色id
     * @param transactionPayId 交易支付 ID
     * @param orderNo 订单编码
     * @param refundNo 退款编码
     * @param payRuleId 支付角色id
     * @param refundAmount 退款金额
     * @return 支付宝退款结果
     */
    WrapperResp<Object> refundByAliPay(Long memberId, Long roleId, String transactionPayId, String orderNo, String refundNo, Integer payRuleId, BigDecimal refundAmount);

    /**
     * 微信退款
     * @param memberId 会员id
     * @param roleId 角色id
     * @param transactionPayId 交易支付 ID
     * @param orderNo 订单编码
     * @param refundNo 退款编码
     * @param payAmount 支付金额
     * @param payRuleId 支付角色id
     * @param refundAmount 退款金额
     * @return 微信退款结果
     */
    WrapperResp<Object> refundByWeChatPay(Long memberId, Long roleId, String transactionPayId, String orderNo, String refundNo, BigDecimal payAmount, Integer payRuleId, BigDecimal refundAmount);

    /**
     * 批量添加合同执行信息
     * @param contractExecuteList 合同执行情况新增VO集合
     * @return 批量添加合同执行信息结果
     */
    WrapperResp<?> addContractExecuteInfoList(List<ContractExecuteInfoAddReq> contractExecuteList);

    /**
     * 通联支付退款-退款申请(1-会员直接到账; 2-平台代收并且平台还没结算给商家)
     * @param request 退款请求数据
     * @return 退款结果
     */
    WrapperResp<RefundResp> refundByDirectAccount(EAccountRefundReq request);

    /**
     * 售后服务 - 订单使用平台优惠券信息查询
     * @param request 请求
     * @return 查询结果
     */
    WrapperResp<List<OrderProductCouponFeignResp>> findOrderProductCouponAmounts(OrderFeignIdsReq request);

    /**
     * 建行支付退款
     * @param vo 操作对象
     * @return 操作结果
     */
    WrapperResp<Boolean> refundByCcbPay(B2bRefundReq vo);

    /**
     * 建行数字货币退款
     * @param vo 操作对象
     * @return 操作结果
     */
    WrapperResp<Boolean> refundByCcbDigitalPay(DigitalRefundReq vo);

    /**
     * 余额退款
     * @param refundRequest 操作数据
     * @return 操作结果
     */
    WrapperResp<Boolean> balanceRefund(BalanceRefundReq refundRequest);

    /**
     * srm订单退货，通知结算服务（mq）
     * @param orderVO 订单数据
     */
    void notifySrmReturnSettlement(SettlementOrderReq orderVO);

    /**
     * 使用平台优惠券的订单退货，通知结算服务
     * @param type 类型
     * @param message 消息
     */
    void sendMsgToSettleAccountMQ(Integer type, String message);

    /**
     * 校验质检单
     */
    void checkQualityOrder(Long id, MemberAndRoleIdDTO dto);

    /**
     * 更新质检单状态
     */
    WrapperResp<Void> updateQualityOrderStatus(QualityOrderUpdateStatusFeignReq vo);

    /**
     * 查询售后维修是否可以跳过下个节点
     * @param entityId 实体id
     * @param taskId 流程id
     * @param innerTaskStep 流程步骤
     * @param user 操作用户
     * @return 是否可以跳过
     */
    WrapperResp<Boolean> processPolicy(Long entityId, String taskId, Integer innerTaskStep, UserLoginCacheDTO user);

    /**
     * 获取售后换货外部流程code
     * @param user 操作用户
     * @param saveVO 换货数据
     * @return 外部流程code
     */
    Integer getReplaceGoodsOuterTaskType(UserLoginCacheDTO user, ReplaceGoodsSaveReq saveVO);

    /**
     * 获取售后退货外部流程code
     * @param user 操作用户
     * @param saveVO 退货数据
     * @return 外部流程code
     */
    Integer getReturnGoodsOuterTaskType(UserLoginCacheDTO user, ReturnGoodsSaveReq saveVO, BigDecimal amount);

    /**
     * 查询售后换货内部流程code
     * @param user 操作用户
     * @param saveVO 换货实体
     * @return 内部流程code
     */
    Integer getReplaceGoodsInnerTaskType(UserLoginCacheDTO user, ReplaceGoodsSaveReq saveVO);

    /**
     * 查询售后退货内部流程code
     * @param user 操作用户
     * @param saveVO 退货实体
     * @return 内部流程code
     */
    Integer getReturnGoodsInnerTaskType(UserLoginCacheDTO user, ReturnGoodsSaveReq saveVO);

    /**
     * 查询售后维修内部流程
     * @param repairGoodsDO 售后维修实体
     * @param detailEntities 售后商品明细
     * @return 内部流程code
     */
    Integer getRepairGoodsInnerTaskType(RepairGoodsDO repairGoodsDO, List<RepairGoodsDetailDO> detailEntities);

    /**
     * 查询规则引擎配置的条件
     * @param request 参数
     * @return Predicate jpa查询条件
     */
    WrapperResp<List<RuleEngineConfigResp>> getRuleEngineCondition(RuleEngineByPredicateReq request);

    /**
     * SRM订单 - 查询会员入库分类 - 主营品类及结算方式信息
     * @param buyerMemberId 采购会员Id
     * @param buyerRoleId   采购会员角色Id
     * @param vendorMemberId 供应会员Id
     * @param vendorRoleId   供应会员角色Id
     * @return 查询结果
     */
    List<MemberCategoryDTO> findMemberBusinessCategories(Long buyerMemberId, Long buyerRoleId, Long vendorMemberId, Long vendorRoleId);

    /**
     * 查询Srm订单物料 品类
     * @param productIds 物料商品Id列表
     * @return 查询结果
     */
    List<OrderProductCategoryDTO> findProductCategories(List<String> productIds);

}
