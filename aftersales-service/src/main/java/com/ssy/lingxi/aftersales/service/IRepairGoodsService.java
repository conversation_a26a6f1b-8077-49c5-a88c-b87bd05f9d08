package com.ssy.lingxi.aftersales.service;

import com.ssy.lingxi.aftersales.api.model.req.VerifyReq;
import com.ssy.lingxi.aftersales.model.req.*;
import com.ssy.lingxi.aftersales.model.resp.*;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;

/**
 * 维修服务接口
 * <AUTHOR>
 * @since 2020/9/1
 * @version 2.0.0
 */
public interface IRepairGoodsService {

    /**
     * 消费者分页查询维修
     * <AUTHOR>
     * @since 2020/9/1
     * @param user: 
     * @param pageByConsumeVO:
     * @return com.ssy.lingxi.common.response.Wrapper<com.ssy.lingxi.common.response.PageData < com.ssy.lingxi.after.sale.model.vo.repair.response.RepairApplyQueryVO>>
     **/
    PageDataResp<RepairGoodsQueryResp> pageByConsumer(UserLoginCacheDTO user, RepairGoodsPageByConsumerDataReq pageByConsumeVO);

    /**
     * 消费者分页查询维修
     * <AUTHOR>
     * @since 2020/4/22
     * @param user:
     * @param pageByConsumeVO:
     * @return com.ssy.lingxi.common.response.Wrapper<com.ssy.lingxi.common.response.PageData < com.ssy.lingxi.after.sale.model.vo.repair.response.RepairApplyQueryVO>>
     **/
    PageDataResp<AfterSaleGoodsIMQueryResp> pageByIM(UserLoginCacheDTO user, AfterSaleGoodsIMPageDataReq pageByConsumeVO);

    /**
     * 消费者分页查询等待提交的维修
     * <AUTHOR>
     * @since 2020/9/1
     * @param user: 
     * @param pageByConsumeVO: 
     * @return com.ssy.lingxi.common.response.Wrapper<com.ssy.lingxi.common.response.PageData < com.ssy.lingxi.after.sale.model.vo.repair.response.RepairApplyQueryVO>>
     **/
    PageDataResp<RepairGoodsQueryResp> pageToBeSubmitByConsumer(UserLoginCacheDTO user, RepairGoodsPageByConsumerDataReq pageByConsumeVO);

    /**
     * 消费者分页查询等待确认完成的维修
     * <AUTHOR>
     * @since 2020/9/1
     * @param user:
     * @param pageByConsumeVO:
     * @return com.ssy.lingxi.common.response.Wrapper<com.ssy.lingxi.common.response.PageData < com.ssy.lingxi.after.sale.model.vo.repair.response.RepairApplyQueryVO>>
     **/
    PageDataResp<RepairGoodsQueryResp> pageToBeConfirmComplete(UserLoginCacheDTO user, RepairGoodsPageByConsumerDataReq pageByConsumeVO);

    /**
     * 保存维修
     * <AUTHOR>
     * @since 2020/9/1
     * @param user:
     * @param saveVO:
     * @return com.ssy.lingxi.common.response.Wrapper<java.lang.Long>
     **/
    Long save(UserLoginCacheDTO user, RepairGoodsSaveReq saveVO);

    /**
     * 删除维修
     * <AUTHOR>
     * @since 2020/9/1
     * @param user:
     * @param repairId:
     * @return com.ssy.lingxi.common.response.Wrapper
     **/
    void delete(UserLoginCacheDTO user, Long repairId);

    /**
     * 提交维修
     * <AUTHOR>
     * @since 2020/9/1
     * @param user:
     * @param repairId:
     * @return com.ssy.lingxi.common.response.Wrapper
     **/
    void submit(UserLoginCacheDTO user, Long repairId);

    /**
     * 确认完成维修
     * <AUTHOR>
     * @since 2020/9/2
     * @param user:
     * @param completeVO:
     * @return com.ssy.lingxi.common.response.Wrapper
     **/
    void confirmComplete(UserLoginCacheDTO user, RepairGoodsConfirmCompleteReq completeVO);

    /**
     * 供应商分页查询维修
     * <AUTHOR>
     * @since 2020/9/2
     * @param user:
     * @param pageBySupplierVO:
     * @return com.ssy.lingxi.common.response.Wrapper<com.ssy.lingxi.common.response.PageData < com.ssy.lingxi.after.sale.model.vo.repair.response.RepairApplyQueryVO>>
     **/
    PageDataResp<RepairGoodsQueryResp> pageBySupplier(UserLoginCacheDTO user, RepairGoodsPageBySupplierDataReq pageBySupplierVO);

    /**
     * 供应商分页查询待提交维修
     * <AUTHOR>
     * @since 2020/9/2
     * @param user:
     * @param pageBySupplierVO:
     * @return com.ssy.lingxi.common.response.Wrapper<com.ssy.lingxi.common.response.PageData < com.ssy.lingxi.after.sale.model.vo.repair.response.RepairApplyQueryVO>>
     **/
    PageDataResp<RepairGoodsQueryResp> pageToBeSubmitBySupplier(UserLoginCacheDTO user, RepairGoodsPageBySupplierDataReq pageBySupplierVO);

    /**
     * 供应商分页查询待审核一级维修
     * <AUTHOR>
     * @since 2020/9/2
     * @param user: 
     * @param pageBySupplierVO: 
     * @return com.ssy.lingxi.common.response.Wrapper<com.ssy.lingxi.common.response.PageData < com.ssy.lingxi.after.sale.model.vo.repair.response.RepairApplyQueryVO>>
     **/
    PageDataResp<RepairGoodsQueryResp> pageToBeVerifyStepOne(UserLoginCacheDTO user, RepairGoodsPageBySupplierDataReq pageBySupplierVO);

    /**
     * 一级审核
     * <AUTHOR>
     * @since 2020/10/17
     * @param user:
     * @param verifyReq:
     * @return com.ssy.lingxi.common.response.Wrapper
     **/
    void verifyStepOne(UserLoginCacheDTO user, VerifyReq verifyReq);

    /**
     * 供应商分页查询待审核二级维修
     * <AUTHOR>
     * @since 2020/9/2
     * @param user:
     * @param pageBySupplierVO:
     * @return com.ssy.lingxi.common.response.Wrapper<com.ssy.lingxi.common.response.PageData < com.ssy.lingxi.after.sale.model.vo.repair.response.RepairApplyQueryVO>>
     **/
    PageDataResp<RepairGoodsQueryResp> pageToBeVerifyStepTwo(UserLoginCacheDTO user, RepairGoodsPageBySupplierDataReq pageBySupplierVO);

    /**
     * 二级审核
     * <AUTHOR>
     * @since 2020/10/17
     * @param user:
     * @param verifyReq:
     * @return com.ssy.lingxi.common.response.Wrapper
     **/
    void verifyStepTwo(UserLoginCacheDTO user, VerifyReq verifyReq);

    /**
     * 供应商分页查询等待确认的维修
     * <AUTHOR>
     * @since 2020/9/2
     * @param user:
     * @param pageVO:
     * @return com.ssy.lingxi.common.response.Wrapper<com.ssy.lingxi.common.response.PageData < com.ssy.lingxi.after.sale.model.vo.repair.response.RepairApplyQueryVO>>
     **/
    PageDataResp<RepairGoodsQueryResp> pageToBeConfirm(UserLoginCacheDTO user, RepairGoodsPageBySupplierDataReq pageVO);

    /**
     * 提交审核
     * <AUTHOR>
     * @since 2020/9/2
     * @param user:
     * @param verifyReq:
     * @return com.ssy.lingxi.common.response.Wrapper
     **/
    void submitVerify(UserLoginCacheDTO user, VerifyReq verifyReq);

    /**
     * 确认审核
     * <AUTHOR>
     * @since 2020/9/2
     * @param user:
     * @param verifyReq:
     * @return com.ssy.lingxi.common.response.Wrapper
     **/
    void confirmVerify(UserLoginCacheDTO user, VerifyReq verifyReq);

    /**
     * 消费者获取维修详情
     * <AUTHOR>
     * @since 2020/9/2
     * @param user:
     * @param repairId:
     * @return com.ssy.lingxi.common.response.Wrapper<com.ssy.lingxi.after.sale.model.vo.repair.response.RepairApplyDetailVO>
     **/
    RepairGoodsDetailResp getDetailByConsumer(UserLoginCacheDTO user, Long repairId);

    /**
     * 供应商获取维修详情
     * <AUTHOR>
     * @since 2020/9/2
     * @param user:
     * @param repairId:
     * @return com.ssy.lingxi.common.response.Wrapper<com.ssy.lingxi.after.sale.model.vo.repair.response.RepairApplyDetailVO>
     **/
    RepairGoodsDetailResp getDetailBySupplier(UserLoginCacheDTO user, Long repairId);

    /**
     * 分页查询列表页面搜索条件内容
     * <AUTHOR>
     * @since 2020/10/17
     * @param user:
     * @return com.ssy.lingxi.common.response.Wrapper<com.ssy.lingxi.after.sale.model.vo.response.RepairGoodsPageItemsVO>
     **/
    RepairGoodsPageItemsResp pageItems(UserLoginCacheDTO user);

    /**
     * 平台分页查询维修申请
     * <AUTHOR>
     * @since 2020/12/7
     * @param user:
     * @param pageVO:
     * @return:
     **/
    PageDataResp<RepairGoodsQueryResp> pageByPlatform(UserLoginCacheDTO user, RepairGoodsPageByPlatformDataReq pageVO);

    /**
     * 平台查询维修申请详情
     * <AUTHOR>
     * @since 2020/12/7
     * @param user:
     * @param repairId:
     * @return:
     **/
    RepairGoodsDetailResp getDetailByPlatform(UserLoginCacheDTO user, Long repairId);

    /**
     * 分页查询维修列表-采购商-app
     * @param sysUser
     * @param pageVO
     * @return
     */
    PageDataResp<RepairGoodsQueryAppResp> pageAppByConsumer(UserLoginCacheDTO sysUser, RepairGoodsPageAppByConsumerDataReq pageVO);
}
