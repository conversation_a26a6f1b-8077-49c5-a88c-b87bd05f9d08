package com.ssy.lingxi.aftersales.model.req;

import lombok.Data;

import java.math.BigDecimal;

/**
* 修改订单商品详情
* <AUTHOR>
* @since 2020/11/9
*/
@Data
public class OrderProductUpdateFeignReq {
    /**
     * 订单项ID
     */
    private  Long  id;

    /**
     * 订单ID
     */
    private  Long  orderId;

    /**
    * 退货数据
    */
    private BigDecimal returnCount;

    /**
    * 换货数量
    */
    private  BigDecimal  replaceCount;

    /**
    * 维修数量
    */
    private  BigDecimal  repairCount;



    /**
     * 一加工数量
     */
    private  BigDecimal  processNum;

    /**
     *已退款金额
     */
    private BigDecimal refundAmount = BigDecimal.ZERO;
}
