package com.ssy.lingxi.aftersales.model.resp;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *  收发货商品明细
 * <AUTHOR>
 * @since 2021/8/13
 * @version 2.0.0
 */
public class ReturnGoodsBatchDetailProductResp implements Serializable {

    private static final long serialVersionUID = 910160649344822048L;

    /**
     * 退货商品id
     */
    private Long returnGoodsId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 商品id/物料编号
     */
    private String productId;

    /**
     * 商品名称/物料名称
     */
    private String productName;

    /**
     * 品类
     */
    private String category;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 单位
     */
    private String unit;

    /**
     * sku图片(App售后显示)
     */
    private String skuPic;

    /**
     * 采购单价
     */
    private BigDecimal purchasePrice;

    /**
     * 退货数量
     */
    private BigDecimal returnCount;

    /**
     * 退货发货数量
     */
    private BigDecimal returnDeliveryCount;

    /**
     * 入库数量
     */
    private BigDecimal storageCount;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 是否需要退货：0.否 1.是
     */
    private Integer isNeedReturn;

    /**
     * 是否需要退货
     */
    private String needReturnName;

    /**
     * 商品规格
     */
    private String type;

    /**
     * 退货单详情.
     */
    private Long returnDetailId;

    public Long getReturnGoodsId() {
        return returnGoodsId;
    }

    public void setReturnGoodsId(Long returnGoodsId) {
        this.returnGoodsId = returnGoodsId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getSkuPic() {
        return skuPic;
    }

    public void setSkuPic(String skuPic) {
        this.skuPic = skuPic;
    }

    public BigDecimal getPurchasePrice() {
        return purchasePrice;
    }

    public void setPurchasePrice(BigDecimal purchasePrice) {
        this.purchasePrice = purchasePrice;
    }

    public BigDecimal getReturnCount() {
        return returnCount;
    }

    public void setReturnCount(BigDecimal returnCount) {
        this.returnCount = returnCount;
    }

    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    public Integer getIsNeedReturn() {
        return isNeedReturn;
    }

    public void setIsNeedReturn(Integer isNeedReturn) {
        this.isNeedReturn = isNeedReturn;
    }

    public String getNeedReturnName() {
        return needReturnName;
    }

    public void setNeedReturnName(String needReturnName) {
        this.needReturnName = needReturnName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public BigDecimal getReturnDeliveryCount() {
        return returnDeliveryCount;
    }

    public void setReturnDeliveryCount(BigDecimal returnDeliveryCount) {
        this.returnDeliveryCount = returnDeliveryCount;
    }

    public BigDecimal getStorageCount() {
        return storageCount;
    }

    public void setStorageCount(BigDecimal storageCount) {
        this.storageCount = storageCount;
    }

    public Long getReturnDetailId() {
        return returnDetailId;
    }

    public void setReturnDetailId(Long returnDetailId) {
        this.returnDetailId = returnDetailId;
    }
}
