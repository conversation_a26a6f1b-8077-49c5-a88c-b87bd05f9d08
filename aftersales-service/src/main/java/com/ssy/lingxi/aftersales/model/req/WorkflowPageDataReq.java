package com.ssy.lingxi.aftersales.model.req;

import com.ssy.lingxi.common.model.req.PageDataReq;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

/**
 * 工作流流转分页查询VO
 * <AUTHOR>
 * @since 2020/10/17
 * @version 2.0.0
 */
public class WorkflowPageDataReq extends PageDataReq {

    private static final long serialVersionUID = -5066816726358894242L;

    /**
     * 数据id
     */
    @NotNull(message = "数据id要大于0")
    @Positive(message = "数据id要大于0")
    private Long dataId;

    public Long getDataId() {
        return dataId;
    }

    public void setDataId(Long dataId) {
        this.dataId = dataId;
    }
}
