package com.ssy.lingxi.aftersales.model.dto;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 操作日志
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/6/28
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OperateLogInputDTO extends PageDataReq implements Serializable {

    private static final long serialVersionUID = -4182510868150291495L;

    /**
     * 售后类型 1-换货 2-退货 3-维修
     */
    private Integer afterSaleType;

    /**
     * 申请单号
     */
    private String applyNo;

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 结束时间
     */
    private Long endTime;
}
