package com.ssy.lingxi.aftersales.model.req;

import com.ssy.lingxi.aftersales.enums.ListTypeEnum;
import com.ssy.lingxi.common.model.req.PageDataReq;

import java.util.List;

public class RepairGoodsPageByCommonDataReq extends PageDataReq {

    private static final long serialVersionUID = -5750223273314297634L;

    /**
     * 申请单号
     */
    private String applyNo;

    /**
     * 申请摘要
     */
    private String applyAbstract;

    /**
     * 开始时间 （yyyy-MM-dd）
     */
    private String startTime;

    /**
     * 结束时间 （yyyy-MM-dd）
     */
    private String endTime;

    /**
     * 内部状态 0或null-所有，1-待提交维修，2-提交审核，3-一级审核通过，4-二级审核通过，5-不接受申请，6-确认接受申请，7-确认不接受申请，8-确认售后完成
     */
    private Integer innerStatus;

    /**
     * 外部状态 0或null-所有，1-待提交申请单，2-待确认申请单，3-不接受申请，4-接受申请，5-售后完成
     */
    private Integer outerStatus;

    /**
     * 采购会员名称
     */
    private String consumerName;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 是否供应商查询
     */
    private Boolean isSupplierSel;

    /**
     * 会员id
     */
    private Long memberId;

    /**
     * 会员角色id
     */
    private Long roleId;

    /**
     * 父级会员id
     */
    private Long parentMemberId;

    /**
     * 父级会员角色id
     */
    private Long parentMemberRoleId;

    /**
     * 以下字段前端不用传
     * 列表查询类型 1-提交 2-待审核一级 3-待审核二级 4-待确认
     * ListTypeEnum 定义
     * @see ListTypeEnum
     */
    private Integer listType;

    /**
     * 用户所属的角色Id（会员自定义角色）列表
     */
    private List<Long> userRoleIds;

    /**
     * 用户在所属商户的权限类型枚举，用于标识该用户是否所属会员的管理员
     * 定义在MemberUserAuthTypeEnum中
     */
    private Integer userType;

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public Long getParentMemberRoleId() {
        return parentMemberRoleId;
    }

    public void setParentMemberRoleId(Long parentMemberRoleId) {
        this.parentMemberRoleId = parentMemberRoleId;
    }

    public String getApplyNo() {
        return applyNo;
    }

    public void setApplyNo(String applyNo) {
        this.applyNo = applyNo;
    }

    public String getApplyAbstract() {
        return applyAbstract;
    }

    public void setApplyAbstract(String applyAbstract) {
        this.applyAbstract = applyAbstract;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Integer getInnerStatus() {
        return innerStatus;
    }

    public void setInnerStatus(Integer innerStatus) {
        this.innerStatus = innerStatus;
    }

    public Integer getOuterStatus() {
        return outerStatus;
    }

    public void setOuterStatus(Integer outerStatus) {
        this.outerStatus = outerStatus;
    }

    public String getConsumerName() {
        return consumerName;
    }

    public void setConsumerName(String consumerName) {
        this.consumerName = consumerName;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public Boolean getSupplierSel() {
        return isSupplierSel;
    }

    public void setSupplierSel(Boolean supplierSel) {
        isSupplierSel = supplierSel;
    }

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public Long getParentMemberId() {
        return parentMemberId;
    }

    public void setParentMemberId(Long parentMemberId) {
        this.parentMemberId = parentMemberId;
    }

    public Integer getListType() {
        return listType;
    }

    public void setListType(Integer listType) {
        this.listType = listType;
    }

    public List<Long> getUserRoleIds() {
        return userRoleIds;
    }

    public void setUserRoleIds(List<Long> userRoleIds) {
        this.userRoleIds = userRoleIds;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }
}
