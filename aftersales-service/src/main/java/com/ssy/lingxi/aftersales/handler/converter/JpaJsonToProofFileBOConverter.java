package com.ssy.lingxi.aftersales.handler.converter;

import com.ssy.lingxi.aftersales.entity.bo.ProofFileBO;
import com.ssy.lingxi.common.util.JsonUtil;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.List;

/**
 * 将自定义的证明文件Entity，与换货、退货、维修申请表中的Jsonb字段进行转换
 * <AUTHOR>
 * @since 2020/9/1
 * @version 2.0.0
 */
@Converter(autoApply = true)
public class JpaJsonToProofFileBOConverter implements AttributeConverter<List<ProofFileBO>, String> {

    @Override
    public String convertToDatabaseColumn(List<ProofFileBO> meta) {
        return JsonUtil.toJson(meta);
    }

    @Override
    public List<ProofFileBO> convertToEntityAttribute(String dbData) {
        return JsonUtil.toList(dbData, ProofFileBO.class);
    }
}