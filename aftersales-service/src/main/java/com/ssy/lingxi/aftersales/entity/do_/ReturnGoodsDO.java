package com.ssy.lingxi.aftersales.entity.do_;

import com.ssy.lingxi.aftersales.api.enums.ReturnGoodsOuterStatusEnum;
import com.ssy.lingxi.aftersales.entity.bo.ProofFileBO;
import com.ssy.lingxi.aftersales.entity.bo.ReceiveGoodsBO;
import com.ssy.lingxi.aftersales.handler.converter.JpaJsonToIntegerConverter;
import com.ssy.lingxi.aftersales.handler.converter.JpaJsonToProofFileBOConverter;
import com.ssy.lingxi.aftersales.handler.converter.JpaJsonToReceiveGoodsBOConverter;
import com.ssy.lingxi.common.constant.TableNameConstant;
import com.ssy.lingxi.component.base.enums.order.OrderTypeEnum;
import lombok.experimental.FieldNameConstants;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 退货表
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/9/4
 */
@Entity
@FieldNameConstants
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_AFTERSALES_SERVICE + "return_goods",
        indexes = {@Index(name = TableNameConstant.TABLE_PRE_AFTERSALES_SERVICE + "return_goods_member_id_role_id_idx", columnList = ReturnGoodsDO.Fields.memberId + "," + ReturnGoodsDO.Fields.roleId),
                @Index(name = TableNameConstant.TABLE_PRE_AFTERSALES_SERVICE + "return_goods_parent_member_id_role_id_idx", columnList = ReturnGoodsDO.Fields.parentMemberId + "," + ReturnGoodsDO.Fields.parentMemberRoleId),
                @Index(name = TableNameConstant.TABLE_PRE_AFTERSALES_SERVICE + "return_goods_inner_status_idx", columnList = ReturnGoodsDO.Fields.innerStatus),
                @Index(name = TableNameConstant.TABLE_PRE_AFTERSALES_SERVICE + "return_goods_outer_status_idx", columnList = ReturnGoodsDO.Fields.outerStatus),
                @Index(name = TableNameConstant.TABLE_PRE_AFTERSALES_SERVICE + "return_goods_apply_time_idx", columnList = ReturnGoodsDO.Fields.applyTime)})
public class ReturnGoodsDO implements Serializable {

    private static final long serialVersionUID = 4276364166360811648L;

    /**
     * id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 申请单号
     */
    @Column(columnDefinition = "varchar(20)")
    private String applyNo;

    /**
     * 会员id
     */
    @Column(columnDefinition = "int8")
    private Long memberId;

    /**
     * 会员角色id
     */
    @Column(columnDefinition = "int8")
    private Long roleId;

    /**
     * 采购会员名称
     */
    @Column(columnDefinition = "varchar(50)")
    private String consumerName;

    /**
     * 父级会员id
     */
    @Column(columnDefinition = "int8")
    private Long parentMemberId;

    /**
     * 父级会员角色id
     */
    @Column(columnDefinition = "int8")
    private Long parentMemberRoleId;

    /**
     * 供应商名称
     */
    @Column(columnDefinition = "varchar(50)")
    private String supplierName;

    /**
     * 店铺id
     */
    @Column(columnDefinition = "int8")
    private Long shopId;

    /**
     * 店铺logo
     */
    @Column(columnDefinition = "varchar(200)")
    private String shopLogo;

    /**
     * 店铺名称
     */
    @Column(columnDefinition = "varchar(50)")
    private String shopName;

    /**
     * 内部任务id
     */
    @Column(columnDefinition = "varchar(50)")
    private String innerTaskId;

    /**
     * 内部工作流当前步骤
     */
    @Column(columnDefinition = "int default 0")
    private Integer innerTaskStep = 0;

    /**
     * 内部工作流步骤记录
     */
    @Convert(converter = JpaJsonToIntegerConverter.class)
    @Column(columnDefinition = "jsonb")
    private List<Integer> innerTaskStepRecord = new ArrayList<>();

    /**
     * 外部任务id
     */
    @Column(columnDefinition = "varchar(50)")
    private String outerTaskId;

    /**
     * 外部工作流当前步骤
     */
    @Column(columnDefinition = "int default 0")
    private Integer outerTaskStep = 0;

    /**
     * 外部工作流步骤记录
     */
    @Convert(converter = JpaJsonToIntegerConverter.class)
    @Column(columnDefinition = "jsonb")
    private List<Integer> outerTaskStepRecord = new ArrayList<>();

    /**
     * 内部状态
     * 定义枚举： ReturnGoodsOuterStatusEnum
     * com.ssy.lingxi.after.sale.model.constant.ReturnGoodsOuterStatusEnum
     */
    @Column(columnDefinition = "int")
    private Integer innerStatus;

    /**
     * 外部状态
     * {{@link ReturnGoodsOuterStatusEnum}}
     */
    @Column(columnDefinition = "int")
    private Integer outerStatus;

    /**
     * 退款金额
     */
    @Column(columnDefinition = "decimal")
    private BigDecimal refundAmount;

    /**
     * 已退款
     */
    @Column(columnDefinition = "decimal")
    private BigDecimal refunded = BigDecimal.ZERO;

    /**
     * 申请摘要
     */
    @Column(columnDefinition = "varchar(60)")
    private String applyAbstract;

    /**
     * 退货收货地址
     */
    @Convert(converter = JpaJsonToReceiveGoodsBOConverter.class)
    @Column(columnDefinition = "jsonb")
    private ReceiveGoodsBO returnGoodsAddress;

    /**
     * 申请时间
     */
    @Column(columnDefinition = "int8")
    private Long applyTime;

    /**
     * 创建时间
     */
    @Column(columnDefinition = "int8")
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(columnDefinition = "int8")
    private Long updateTime;

    /**
     * 证明文件
     */
    @Convert(converter = JpaJsonToProofFileBOConverter.class)
    @Column(columnDefinition = "jsonb")
    private List<ProofFileBO> proofFileList;

    /**
     * 外部工作流类型（取商品）
     */
    @Column(columnDefinition = "int")
    private Integer outerTaskType;

    /**
     * 内部部工作流类型
     */
    @Column(columnDefinition = "int")
    private Integer innerTaskType;

    /**
     * 退货批次
     */
    @Column(columnDefinition = "int")
    private Integer returnBatch;

    /**
     * 退货发货单号
     */
    @Column(columnDefinition = "int8")
    private Long returnDeliveryId;

    /**
     * 退货发货单号
     */
    @Column(columnDefinition = "varchar(20)")
    private String returnDeliveryNo;

    /**
     * 退货入库单Id
     */
    @Column(columnDefinition = "int8")
    private Long returnStorageId;

    /**
     * 退货入库单号
     */
    @Column(columnDefinition = "varchar(20)")
    private String returnStorageNo;

    /**
     * 退货物流单Id
     */
    @Column(columnDefinition = "int8")
    private Long returnLogisticsId;

    /**
     * 退货物流单号
     */
    @Column(columnDefinition = "varchar(20)")
    private String returnLogisticsNo;

    /**
     * 是否需要发票：0-否，1-是
     */
    @Column(columnDefinition = "int")
    private Integer needInvoice;

    /**
     * 订单类型
     *
     * @see OrderTypeEnum
     */
    @Column(columnDefinition = "int")
    private Integer orderType;

    /**
     * 退货原因
     */
    @Column(columnDefinition = "varchar(200)")
    private String returnReason;

    /**
     * AgentFlagEnum
     * 代客标识: 0 - 非代客； 1 - 代客
     */
    @Column(columnDefinition = "int")
    private Integer agentFlag;


    /**
     * 来源类型 0.默认 1.质检单
     */
    @Column(columnDefinition = "int")
    private Integer sourceType = 0;

    /**
     * 来源id
     * sourceType=1,质检单id
     */
    @Column
    private Long sourceId = 0L;

    /**
     * 单向一对多 明细
     */
    @OneToMany(cascade = {CascadeType.DETACH}, fetch = FetchType.LAZY)
    @JoinColumn(name = "returnId", referencedColumnName = "id")
    private List<ReturnGoodsDetailDO> detailList;

    public Integer getNeedInvoice() {
        return needInvoice;
    }

    public void setNeedInvoice(Integer needInvoice) {
        this.needInvoice = needInvoice;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Long getReturnLogisticsId() {
        return returnLogisticsId;
    }

    public void setReturnLogisticsId(Long returnLogisticsId) {
        this.returnLogisticsId = returnLogisticsId;
    }

    public String getReturnLogisticsNo() {
        return returnLogisticsNo;
    }

    public void setReturnLogisticsNo(String returnLogisticsNo) {
        this.returnLogisticsNo = returnLogisticsNo;
    }

    public Long getReturnDeliveryId() {
        return returnDeliveryId;
    }

    public void setReturnDeliveryId(Long returnDeliveryId) {
        this.returnDeliveryId = returnDeliveryId;
    }

    public Long getReturnStorageId() {
        return returnStorageId;
    }

    public void setReturnStorageId(Long returnStorageId) {
        this.returnStorageId = returnStorageId;
    }

    public BigDecimal getRefunded() {
        return refunded;
    }

    public void setRefunded(BigDecimal refunded) {
        this.refunded = refunded;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getApplyNo() {
        return applyNo;
    }

    public void setApplyNo(String applyNo) {
        this.applyNo = applyNo;
    }

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public String getConsumerName() {
        return consumerName;
    }

    public void setConsumerName(String consumerName) {
        this.consumerName = consumerName;
    }

    public Long getParentMemberId() {
        return parentMemberId;
    }

    public void setParentMemberId(Long parentMemberId) {
        this.parentMemberId = parentMemberId;
    }

    public Long getParentMemberRoleId() {
        return parentMemberRoleId;
    }

    public void setParentMemberRoleId(Long parentMemberRoleId) {
        this.parentMemberRoleId = parentMemberRoleId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getShopLogo() {
        return shopLogo;
    }

    public void setShopLogo(String shopLogo) {
        this.shopLogo = shopLogo;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getInnerTaskId() {
        return innerTaskId;
    }

    public void setInnerTaskId(String innerTaskId) {
        this.innerTaskId = innerTaskId;
    }

    public String getOuterTaskId() {
        return outerTaskId;
    }

    public void setOuterTaskId(String outerTaskId) {
        this.outerTaskId = outerTaskId;
    }

    public Integer getInnerStatus() {
        return innerStatus;
    }

    public void setInnerStatus(Integer innerStatus) {
        this.innerStatus = innerStatus;
    }

    public Integer getOuterStatus() {
        return outerStatus;
    }

    public void setOuterStatus(Integer outerStatus) {
        this.outerStatus = outerStatus;
    }

    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    public String getApplyAbstract() {
        return applyAbstract;
    }

    public void setApplyAbstract(String applyAbstract) {
        this.applyAbstract = applyAbstract;
    }

    public ReceiveGoodsBO getReturnGoodsAddress() {
        return returnGoodsAddress;
    }

    public void setReturnGoodsAddress(ReceiveGoodsBO returnGoodsAddress) {
        this.returnGoodsAddress = returnGoodsAddress;
    }

    public Long getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(Long applyTime) {
        this.applyTime = applyTime;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public List<ProofFileBO> getProofFileList() {
        return proofFileList;
    }

    public void setProofFileList(List<ProofFileBO> proofFileList) {
        this.proofFileList = proofFileList;
    }

    public Integer getOuterTaskType() {
        return outerTaskType;
    }

    public void setOuterTaskType(Integer outerTaskType) {
        this.outerTaskType = outerTaskType;
    }

    public Integer getInnerTaskType() {
        return innerTaskType;
    }

    public void setInnerTaskType(Integer innerTaskType) {
        this.innerTaskType = innerTaskType;
    }

    public Integer getReturnBatch() {
        return returnBatch;
    }

    public void setReturnBatch(Integer returnBatch) {
        this.returnBatch = returnBatch;
    }

    public String getReturnDeliveryNo() {
        return returnDeliveryNo;
    }

    public void setReturnDeliveryNo(String returnDeliveryNo) {
        this.returnDeliveryNo = returnDeliveryNo;
    }

    public String getReturnStorageNo() {
        return returnStorageNo;
    }

    public void setReturnStorageNo(String returnStorageNo) {
        this.returnStorageNo = returnStorageNo;
    }

    public Integer getInnerTaskStep() {
        return innerTaskStep;
    }

    public void setInnerTaskStep(Integer innerTaskStep) {
        this.innerTaskStep = innerTaskStep;
    }

    public List<Integer> getInnerTaskStepRecord() {
        return innerTaskStepRecord;
    }

    public void setInnerTaskStepRecord(List<Integer> innerTaskStepRecord) {
        this.innerTaskStepRecord = innerTaskStepRecord;
    }

    public Integer getOuterTaskStep() {
        return outerTaskStep;
    }

    public void setOuterTaskStep(Integer outerTaskStep) {
        this.outerTaskStep = outerTaskStep;
    }

    public List<Integer> getOuterTaskStepRecord() {
        return outerTaskStepRecord;
    }

    public void setOuterTaskStepRecord(List<Integer> outerTaskStepRecord) {
        this.outerTaskStepRecord = outerTaskStepRecord;
    }

    public String getReturnReason() {
        return returnReason;
    }

    public void setReturnReason(String returnReason) {
        this.returnReason = returnReason;
    }

    public Integer getAgentFlag() {
        return agentFlag;
    }

    public void setAgentFlag(Integer agentFlag) {
        this.agentFlag = agentFlag;
    }

    public Integer getSourceType() {
        return sourceType;
    }

    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }

    public Long getSourceId() {
        return sourceId;
    }

    public void setSourceId(Long sourceId) {
        this.sourceId = sourceId;
    }

    public List<ReturnGoodsDetailDO> getDetailList() {
        return detailList;
    }

    public void setDetailList(List<ReturnGoodsDetailDO> detailList) {
        this.detailList = detailList;
    }
}
