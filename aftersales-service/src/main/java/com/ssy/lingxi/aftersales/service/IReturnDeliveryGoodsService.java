package com.ssy.lingxi.aftersales.service;

import com.ssy.lingxi.aftersales.model.req.CommodityPageDataReq;
import com.ssy.lingxi.aftersales.model.req.DeliveryGoodsPageDataReq;
import com.ssy.lingxi.aftersales.model.req.ReceiveGoodsPageDataReq;
import com.ssy.lingxi.aftersales.model.resp.CommodityQueryResp;
import com.ssy.lingxi.aftersales.model.resp.DeliveryGoodsResp;
import com.ssy.lingxi.aftersales.model.resp.ReceiveGoodsResp;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;

import java.util.List;

/**
 * 退货发货接口
 * <AUTHOR>
 * @since 2020/10/19
 **/
public interface IReturnDeliveryGoodsService {

    /**
     * 分页查询发货
     * <AUTHOR>
     * @since 2020/10/19
     * @param pageVO:
     * @return com.ssy.lingxi.common.response.Wrapper<com.ssy.lingxi.common.response.PageData < com.ssy.lingxi.after.sale.model.vo.response.DeliveryGoodsVO>>
     **/
    PageDataResp<DeliveryGoodsResp> pageDeliveryGoods(UserLoginCacheDTO user, DeliveryGoodsPageDataReq pageVO);

    /**
     * 分页查询收货
     * <AUTHOR>
     * @since 2020/10/19
     * @param pageVO:
     * @return com.ssy.lingxi.common.response.Wrapper<com.ssy.lingxi.common.response.PageData < com.ssy.lingxi.after.sale.model.vo.response.ReceiveGoodsVO>>
     **/
    PageDataResp<ReceiveGoodsResp> pageReceiveGoods(UserLoginCacheDTO user, ReceiveGoodsPageDataReq pageVO);

    /**
     * 获取发货列表
     * @param returnId 换货id
     * @return
     */
    List<DeliveryGoodsResp> getDeliveryList(Long returnId, Boolean isSupplier);

    /**
     * 物流服务获取退货商品
     * <AUTHOR>
     * @since 2020/12/8
     * @param pageVO:
     * @return:
     **/
    PageDataResp<CommodityQueryResp> pageCommodityByLogistics(CommodityPageDataReq pageVO);
}
