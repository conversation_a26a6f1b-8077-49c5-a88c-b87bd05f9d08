package com.ssy.lingxi.aftersales.model.req;

import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;


/**
 * 售后换货(供应商)新增入库单VO
 * <AUTHOR>
 * @since 2020/9/18
 * @version 2.0.0
 */
public class ReplaceReturnReceiveAddReq implements Serializable {

    private static final long serialVersionUID = -4635922130402817857L;

    /**
     * 换货id
     */
    @NotNull(message = "换货Id要大于0")
    @Positive(message = "换货Id要大于0")
    private Long replaceId;

    /**
     * 入库时间
     */
    @NotNull(message = "入库时间不能为空")
    private Long storageTime;

    /**
     * 单据摘要
     */
    @Length(max = 60,message = "单据摘要最大字符长度30")
    @NotBlank(message = "单据摘要不能为空")
    private String orderAbstract;

    /**
     * 备注
     */
    @Length(max = 100,message = "备注最大字符长度30")
    private String remark;

    /**
     * 对应仓库
     */
    @Length(max = 30,message = "对应仓库最大字符长度30")
    private String inventoryName;

    /**
     * 对应仓库人员
     */
    @Length(max = 30,message = "对应仓库人员最大字符长度12")
    private String inventoryRole;

    /**
     * 商品列表
     */
    @Valid
    @NotNull(message = "商品不能为空")
    @Size(min = 1, message = "至少有一个商品")
    private List<ReplaceDeliveryGoodsAddProductReq> detailList;

    public Long getReplaceId() {
        return replaceId;
    }

    public void setReplaceId(Long replaceId) {
        this.replaceId = replaceId;
    }

    public Long getStorageTime() {
        return storageTime;
    }

    public void setStorageTime(Long storageTime) {
        this.storageTime = storageTime;
    }

    public String getOrderAbstract() {
        return orderAbstract;
    }

    public void setOrderAbstract(String orderAbstract) {
        this.orderAbstract = orderAbstract;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getInventoryName() {
        return inventoryName;
    }

    public void setInventoryName(String inventoryName) {
        this.inventoryName = inventoryName;
    }

    public String getInventoryRole() {
        return inventoryRole;
    }

    public void setInventoryRole(String inventoryRole) {
        this.inventoryRole = inventoryRole;
    }

    public List<ReplaceDeliveryGoodsAddProductReq> getDetailList() {
        return detailList;
    }

    public void setDetailList(List<ReplaceDeliveryGoodsAddProductReq> detailList) {
        this.detailList = detailList;
    }
}
