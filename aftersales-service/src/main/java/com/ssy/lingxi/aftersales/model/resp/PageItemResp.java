package com.ssy.lingxi.aftersales.model.resp;

import java.io.Serializable;

/**
 * 列表页面搜索条件内容VO
 * <AUTHOR>
 * @since 2020/10/17
 * @version 2.0.0
 */
public class PageItemResp implements Serializable {

    private static final long serialVersionUID = -8419718455226843776L;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 名称
     */
    private String name;

    public PageItemResp() {
    }

    public PageItemResp(Integer status, String name) {
        this.status = status;
        this.name = name;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
