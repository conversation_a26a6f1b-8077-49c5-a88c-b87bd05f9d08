package com.ssy.lingxi.aftersales.model.req;

import com.ssy.lingxi.common.model.req.PageDataReq;

/**
 * 供应商分页查询维修VO
 * <AUTHOR>
 * @since 2020/9/1
 * @version 2.0.0
 */
public class RepairGoodsPageBySupplierDataReq extends PageDataReq {

    private static final long serialVersionUID = 8857579623878291407L;

    /**
     * 申请单号
     */
    private String applyNo;

    /**
     * 申请摘要
     */
    private String applyAbstract;

    /**
     * 开始时间 （yyyy-MM-dd）
     */
    private String startTime;

    /**
     * 结束时间 （yyyy-MM-dd）
     */
    private String endTime;

    /**
     * 内部状态 0或null-所有，1-待提交维修，2-提交审核，3-一级审核通过，4-二级审核通过，5-不接受申请，6-确认接受申请，7-确认不接受申请，8-确认售后完成
     */
    private Integer innerStatus;

    /**
     * 外部状态 0或null-所有，1-待提交申请单，2-待确认申请单，3-不接受申请，4-接受申请，5-售后完成
     */
    private Integer outerStatus;

    /**
     * 采购会员名称
     */
    private String consumerName;

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    /**
     * 会员id
     */
    private Long memberId;

    public String getApplyNo() {
        return applyNo;
    }

    public void setApplyNo(String applyNo) {
        this.applyNo = applyNo;
    }

    public String getApplyAbstract() {
        return applyAbstract;
    }

    public void setApplyAbstract(String applyAbstract) {
        this.applyAbstract = applyAbstract;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Integer getInnerStatus() {
        return innerStatus;
    }

    public void setInnerStatus(Integer innerStatus) {
        this.innerStatus = innerStatus;
    }

    public Integer getOuterStatus() {
        return outerStatus;
    }

    public void setOuterStatus(Integer outerStatus) {
        this.outerStatus = outerStatus;
    }

    public String getConsumerName() {
        return consumerName;
    }

    public void setConsumerName(String consumerName) {
        this.consumerName = consumerName;
    }
}
