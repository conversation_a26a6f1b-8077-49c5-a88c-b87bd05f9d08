package com.ssy.lingxi.api.model.req;

import com.ssy.lingxi.component.base.annotation.AesDecryptAnnotation;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 用户实体类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/10/12
 */
@Getter
@Setter
public class UserReq implements Serializable {
    private static final long serialVersionUID = -3797317112012274822L;

    /**
     * 账号
     */
    @NotNull(message = "账号不能为空")
    private String account;

    /**
     * 密码
     */
    @AesDecryptAnnotation
    @NotBlank(message = "密码不能为空")
    private String password;
}
