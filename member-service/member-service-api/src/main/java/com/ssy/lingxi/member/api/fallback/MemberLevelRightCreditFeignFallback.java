package com.ssy.lingxi.member.api.fallback;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.api.feign.IMemberLevelRightCreditFeign;
import com.ssy.lingxi.member.api.model.req.*;
import com.ssy.lingxi.member.api.model.resp.*;
import lombok.extern.slf4j.Slf4j;

import javax.validation.Valid;
import java.util.List;

/**
 * 会员等级（Level）、权益（Right）、信用（Credit）内部Feign服务降级通知
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-12-16
 */
@Slf4j
public class MemberLevelRightCreditFeignFallback implements IMemberLevelRightCreditFeign {

    private final Throwable throwable;

    public MemberLevelRightCreditFeignFallback(Throwable cause) {
        this.throwable = cause;
    }

    /**
     * 查询平台会员的等级、权益、信用积分（多个）
     * @param memberIdList 接口参数
     * @return 平台会员的等级、权益、信用积分
     */
    @Override
    public WrapperResp<List<MemberFeignLrcResp>> getPlatformMemberLrcBatch(@Valid List<Long> memberIdList) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_MEMBER_ERROR);
    }

    /**
     * 查询平台会员的等级、权益、信用积分
     * @param feignVO 接口参数
     * @return 平台会员的等级、权益、信用积分
     */
    @Override
    public WrapperResp<MemberFeignLrcResp> getPlatformMemberLrc(@Valid MemberFeignReq feignVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_MEMBER_ERROR);
    }

    /**
     * 查询下级会员的等级、权益、信用积分
     * <p> 注意：如果不是上下级会员关系，返回Null</p>
     *
     * @param relationVO 接口参数
     * @return 下级会员在上级会员下的等级、权益、信用积分
     */
    @Override
    public WrapperResp<MemberFeignLrcResp> getMemberLrc(@Valid MemberFeignRelationReq relationVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_MEMBER_ERROR);
    }

    /**
     * 查询下级会员的价格权益设置
     *
     * @param relationVO 接口参数
     * @return 下级会员在上级会员下的价格权益
     */
    @Override
    public WrapperResp<MemberFeignRightResp> getMemberPriceRight(@Valid MemberFeignRelationReq relationVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_MEMBER_ERROR);
    }

    /**
     * 批量查询下级会员的可用积分
     *
     * @param batchVO 接口参数
     * @return 下级会员在上级会员下的价格权益
     */
    @Override
    public WrapperResp<List<MemberFeignRightByOrderResp>> batchMemberPriceRightForOrder(@Valid MemberFeignBatchReq batchVO){
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_MEMBER_ERROR);
    }

    /**
     * 批量查询下级会员的价格权益
     *
     * @param batchVO 接口参数
     * @return 下级会员在上级会员下的价格权益
     */
    @Override
    public WrapperResp<List<MemberFeignRightDetailResp>> batchMemberPriceRight(@Valid MemberFeignBatchReq batchVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_MEMBER_ERROR);
    }

    /**
     * 批量查询下级会员的价格权益
     *
     * @param feignRelations 接口参数
     * @return 下级会员在上级会员下的价格权益
     */
    @Override
    public WrapperResp<List<MemberFeignRelationRightDetailResp>> batchMemberPriceRight(@Valid List<MemberFeignRelationReq> feignRelations) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_MEMBER_ERROR);
    }

    /**
     * 查询下级会员的返现权益设置
     *
     * @param relationVO 接口参数
     * @return 下级会员在上级会员下的返现权益
     */
    @Override
    public WrapperResp<MemberFeignRightResp> getMemberReturnRight(@Valid MemberFeignRelationReq relationVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_MEMBER_ERROR);
    }

    /**
     * （批量）查询下级会员在平台后台、上级会员下的返现权益设置
     *
     * @param relationVO 接口参数
     * @return 下级会员在上级会员下的返现权益
     */
    @Override
    public WrapperResp<List<MemberFeignRightDetailResp>> findMemberReturnRight(@Valid MemberFeignRelationReq relationVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_MEMBER_ERROR);
    }

    /**
     * 订单完成后，计算等级、权益、信用等信息
     *
     * @param orderVO 接口参数
     * @return 计算结果
     */
    @Override
    public WrapperResp<MemberFeignReturnRightResp> calculateMemberLrcByOrder(@Valid MemberFeignOrderReq orderVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_MEMBER_ERROR);
    }

    /**
     * 积分支付订单，校验可用信用积分、支付密码，再异步计算下级会员的权益积分
     *
     * @param spendVO 接口参数
     * @return 操作结果
     */
    @Override
    public WrapperResp<Void> calculateMemberUsedRightPoint(@Valid MemberFeignRightSpendReq spendVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_MEMBER_ERROR);
    }

    /**
     * 售后评论成功后，计算会员信用积分
     *
     * @param commentVO 接口参数
     * @return 操作结果
     */
    @Override
    public WrapperResp<Void> calculateMemberAfterSaleCreditPoint(@Valid MemberFeignAfterSaleCommentReq commentVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_MEMBER_ERROR);
    }

    /**
     * 根据memberLevelConfigId查询会员等级配置信息
     * @param memberLevelConfigId 接口参数
     * @return 返回结果
     */
    @Override
    public WrapperResp<List<MemberFeignLevelConfigResp>> getMemberLevelConfigBatch(List<Long> memberLevelConfigId) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_MEMBER_ERROR);
    }

    /**
     * 积分抵扣订单金额，校验可用信用积分、支付密码，扣除抵扣的积分
     * @param spendVO 接口参数
     * @return 返回结果
     */
    @Override
    public WrapperResp<Void> calculateMemberDeductionRightPoint(@Valid MemberFeignRightDeductionReq spendVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_MEMBER_ERROR);
    }
    /**
     * 返还-抵扣订单金额的积分
     * @param returnVO 接口参数
     * @return 返回结果
     */
    @Override
    public WrapperResp<Void> returnMemberRightPoint(@Valid MemberFeignRightReturnReq returnVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_MEMBER_ERROR);
    }

    /**
     * 批量查询下级会员在上级会员下的等级
     *
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<MemberFeignLevelDetailResp>> findSubMemberLevels(@Valid MemberFeignReq feignVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_MEMBER_ERROR);
    }

    /**
     * 查询会员等级配置
     *
     * @param req 接口参数
     * @return 等级配置列表
     */
    @Override
    public WrapperResp<List<MemberFeignLevelResp>> findMemberLevelConfigs(MemberFeignLevelReq req) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_MEMBER_ERROR);
    }

    /**
     * V3 - 订单服务查询供应商信息
     *
     * @param req 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<MemberFeignCalcResp>> findVendors(MemberFeignCalcReq req) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_MEMBER_ERROR);
    }

    /**
     * 根据上级会员Id和上级会员角色Id，以及当前用户，查询价格权益参数设置
     *
     * @param upperVO 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<MemberFeignManageMemberCreditParameterResp> getUpperMemberCreditParameter(MemberFeignManageUpperMemberAndRoleReq upperVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_MEMBER_ERROR);
    }
}
