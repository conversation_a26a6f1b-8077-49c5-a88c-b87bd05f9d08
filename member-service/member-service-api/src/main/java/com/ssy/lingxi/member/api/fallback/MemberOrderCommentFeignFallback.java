package com.ssy.lingxi.member.api.fallback;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.api.feign.IMemberOrderCommentFeign;
import com.ssy.lingxi.member.api.model.req.MemberOrderCommentReportReq;
import com.ssy.lingxi.member.api.model.req.MemberOrderCommentReq;
import lombok.extern.slf4j.Slf4j;

import javax.validation.Valid;

/**
 * 会员评价内部feign降级
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/1/6
 */
@Slf4j
public class MemberOrderCommentFeignFallback implements IMemberOrderCommentFeign {

    private final Throwable throwable;

    public MemberOrderCommentFeignFallback(Throwable cause) {
        this.throwable = cause;
    }

    /**
     * 保存订单数据
     * @param memberOrderCommentReq 接口参数
     * @return 返回结果
     */
    @Override
    public WrapperResp<Void> saveMemberOrderComment(@Valid MemberOrderCommentReq memberOrderCommentReq) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_MEMBER_ERROR);
    }

    /**
     * 根据订单id集合查询待评价订单
     * @param reportVo 订单id集合
     * @return 返货结果
     */
    @Override
    public WrapperResp<Integer> findWaitCommentOrder(MemberOrderCommentReportReq reportVo) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_MEMBER_ERROR);
    }
}
