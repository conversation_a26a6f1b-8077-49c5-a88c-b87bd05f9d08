package com.ssy.lingxi.member.api.model.resp;

import lombok.Data;

import java.io.Serializable;

/**
 * 商城能力 - 店铺 - 会员搜索结果VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-09-23
 */
@Data
public class MemberFeignShopQueryResp implements Serializable {
    private static final long serialVersionUID = 8515817966784365798L;

    /**
     * 会员Id
     */
    private Long memberId;

    /**
     * 会员名称
     */
    private String name;

    /**
     * 会员角色Id
     */
    private Long roleId;

    /**
     * 会员角色名称
     */
    private String roleName;

    /**
     * 会员等级
     */
    private Integer level;

    /**
     * 会员等级名称
     */
    private String levelTag;

    /**
     * 会员状态 1-正常，2-冻结，定义在MemberStatusEnum中
     */
    private Integer status;

    /**
     * 角色状态，0-禁用，1-启用
     */
    private Integer roleStatus;

    /**
     * 外部状态：0-待提交审核，1-待审核，2-审核不通过，3-审核通过
     */
    private Integer outerStatus;

    /**
     * 外部状态名称
     */
    private String outerStatusName;

    /**
     * 注册年数
     */
    private Integer registerYears;

    /**
     * 信用积分
     */
    private Integer creditPoint;

    /**
     * 平均交易评价星级（总体满意度）
     */
    private Integer avgTradeCommentStar;
}
