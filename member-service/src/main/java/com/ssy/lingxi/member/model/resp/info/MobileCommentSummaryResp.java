package com.ssy.lingxi.member.model.resp.info;

import lombok.Data;

import java.io.Serializable;

/**
 * App - 会员评价（交易、售后）汇总VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-12-08
 */
@Data
public class MobileCommentSummaryResp implements Serializable {
    private static final long serialVersionUID = 7003838295337171079L;

    public MobileCommentSummaryResp() {
        this.total = 0L;
        this.good = 0;
        this.medium = 0;
        this.bad = 0;
    }

    /**
     * 评论数
     */
    private Long total;

    /**
     * 好评率，整数
     */
    private Integer good;

    /**
     * 中评率，整数
     */
    private Integer medium;

    /**
     * 差评率，整数
     */
    private Integer bad;
}
