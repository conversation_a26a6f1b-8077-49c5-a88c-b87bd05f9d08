package com.ssy.lingxi.member.model.resp.commission;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 佣金明细响应
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-19
 */
@Getter
@Setter
public class CommissionDetailResp implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 分佣账户id
     */
    private Long commissionAccountId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 佣金收益流水号
     */
    private String tradeCode;

    /**
     * 交易类型：1-邀请注册奖励，2-邀请认证奖励，3-邀请首单奖励，4-下单分佣
     */
    private Integer tradeType;

    /**
     * 交易类型名称
     */
    private String tradeTypeName;

    /**
     * 原始金额
     */
    private BigDecimal originalAmount;

    /**
     * 变更金额
     */
    private BigDecimal changeAmount;

    /**
     * 现有金额
     */
    private BigDecimal currentAmount;

    /**
     * 交易时间
     */
    private String tradeTime;

    /**
     * 关联订单号
     */
    private String relatedOrderNo;

    /**
     * 被邀请人用户id
     */
    private Long invitedUserId;

    /**
     * 被邀请人姓名
     */
    private String invitedUserName;

    /**
     * 备注
     */
    private String remark;
}
