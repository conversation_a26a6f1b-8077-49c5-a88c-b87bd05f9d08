package com.ssy.lingxi.member.entity.do_.basic;

import com.ssy.lingxi.common.constant.TableNameConstant;
import com.ssy.lingxi.component.base.handler.converter.JpaJsonToMapStringObjectConverter;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 企业认证
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2025/6/4
 */
@Getter
@Setter
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_MEMBER_SERVICE + "enterprise_certification",
        indexes = {@Index(name = TableNameConstant.TABLE_PRE_MEMBER_SERVICE + "member_id_idx", columnList = "memberId")})
public class EnterpriseCertificationDraftDO implements Serializable {
    private static final long serialVersionUID = 3346961678284566094L;

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 会员id
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long memberId;

    /**
     * 草稿
     */
    @Convert(converter = JpaJsonToMapStringObjectConverter.class)
    @Column(columnDefinition = "jsonb", nullable = false)
    private Map<String, Object> draft;

    /**
     * 创建时间
     */
    @Column(columnDefinition = "timestamp", nullable = false)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Column(columnDefinition = "timestamp", nullable = false)
    private LocalDateTime updateTime;

    /**
     * 保存到数据库之前自动触发
     */
    @PrePersist
    void prePersist() {
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }

}
