package com.ssy.lingxi.member.model.resp.maintenance;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 平台后台 - 会员维护 - 会员详情 - 信用信息 - 交易评价记录VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-16
 */
@Data
public class MemberDetailCreditTradeHistoryResp implements Serializable {
    private static final long serialVersionUID = -3996678309044374444L;

    /**
     * 记录id
     */
    private Long id;

    /**
     * 评价时间
     */
    private String createTime;

    /**
     * 评价星级（1-5）
     */
    private Integer star;

    /**
     * 评价心得
     */
    private String comment;

    /**
     * 交易商品
     */
    private String product;

    /**
     * 评价方（会员名称）
     */
    private String byMemberName;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 交易时间
     */
    private String dealTime;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 交易数量
     */
    private BigDecimal purchaseCount;

    /**
     * 订单id
     */
    private Long orderId;
}
