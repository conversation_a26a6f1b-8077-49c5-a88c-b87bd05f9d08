package com.ssy.lingxi.member.enums;

import com.ssy.lingxi.common.model.resp.select.SelectIntegerResp;
import com.ssy.lingxi.component.base.language.LanguageHolder;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 会员注销审核枚举
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/9/2
 */
@Getter
@AllArgsConstructor
public enum MemberCancellationEnum {
    /**
     * 0-全部
     */
    ALL(0, "全部"),

    /**
     * 1-待审核注销
     */
    WAIT_CANCELLATION(1, "待审核注销"),

    /**
     * 2-注销审核通过
     */
    CANCELLATION_SUCCESS(2, "注销审核通过"),

    /**
     * 3-注销审核不通过
     */
    CANCELLATION_FAIL(3, "注销审核不通过");

    private final Integer code;
    private final String message;

    public String getMessage() {
        return LanguageHolder.getTranslation(this.getClass(), this.message, this.code);
    }

    public static String getCodeMsg(Integer code) {
        MemberCancellationEnum buyerInnerStatusEnum = Arrays.stream(MemberCancellationEnum.values()).filter(f -> Objects.equals(f.getCode(), code)).findFirst().orElse(null);
        return Objects.nonNull(buyerInnerStatusEnum) ? buyerInnerStatusEnum.getMessage() : null;
    }

    public static List<Integer> getCodeList() {
        return Arrays.stream(MemberCancellationEnum.values()).map(MemberCancellationEnum::getCode).collect(Collectors.toList());
    }

    public static List<SelectIntegerResp> getDropdownItemResp() {
        return Arrays.stream(MemberCancellationEnum.values()).map(e -> new SelectIntegerResp(e.getCode(), e.getMessage())).collect(Collectors.toList());
    }
}
