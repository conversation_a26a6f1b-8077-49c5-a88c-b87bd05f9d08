package com.ssy.lingxi.member.model.resp.basic;

import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.member.MemberRelationTypeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberStringEnum;
import com.ssy.lingxi.member.enums.MemberOuterStatusEnum;
import com.ssy.lingxi.member.enums.MemberValidateHistoryOperationEnum;
import lombok.Data;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 日志中心 - 查询内部历史记录返回
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-12-03
 */
@Data
public class MemberInnerHistoryDetailResp implements Serializable {
    private static final long serialVersionUID = 8748784747212748256L;

    public MemberInnerHistoryDetailResp(Long memberId, Long subMemberId, Integer relType, LocalDateTime createTime, String operator, String organizationName, String jobTitle, Integer outerStatus, Integer operateCode, String operation, String remark) {
        this.memberId = relType == null ? subMemberId : (relType.equals(MemberRelationTypeEnum.PLATFORM.getCode()) ? subMemberId : memberId);
        this.createTime = createTime == null ? "" : createTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
        this.operateType = relType == null ? "" : (relType.equals(MemberRelationTypeEnum.PLATFORM.getCode()) ? MemberStringEnum.PLATFORM_BACKGROUND.getName() : MemberStringEnum.MEMBER_CENTRE.getName());
        this.operator = operator;
        this.organizationName = organizationName;
        this.jobTitle = jobTitle;
        this.statusName = MemberOuterStatusEnum.getCodeMsg(outerStatus);
        this.operation = NumberUtil.isNullOrZero(operateCode) ? operation : MemberValidateHistoryOperationEnum.getMsgByCode(operateCode);
        this.remark = StringUtils.hasLength(remark) ? remark : "";
    }

    /**
     * 会员Id
     */
    private Long memberId;

    /**
     * 操作时间，格式为yyyy-MM-dd HH:mm
     */
    private String createTime;

    /**
     * 操作类型
     */
    private String operateType;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 组织机构名称
     */
    private String organizationName;

    /**
     * 职位
     */
    private String jobTitle;

    /**
     * 状态
     */
    private String statusName;

    /**
     * 操作
     */
    private String operation;

    /**
     * 备注说明
     */
    private String remark;
}
