package com.ssy.lingxi.member.entity.do_.lifecycle;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.ssy.lingxi.common.constant.TableNameConstant;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Set;

/**
 * 评分模板实体类
 *
 * <AUTHOR>
 * @since 2022/6/24 18:10
 * @version: 1.0
 */
@Getter
@Setter
@JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler", "fieldHandler"})
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_MEMBER_SERVICE + "member_scoring_template",
        indexes = {@Index(name = TableNameConstant.TABLE_PRE_MEMBER_SERVICE + "member_scoring_template_member_id_role_id_role_tag_idx", columnList = "memberId,roleId,roleTag")})
public class MemberScoringTemplateDO implements Serializable {

    private static final long serialVersionUID = -5125629135452124419L;

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 会员id
     */
    @Column
    private Long memberId;

    /**
     * 角色id
     */
    @Column
    private Long roleId;

    /**
     * 角色标签
     */
    @Column
    private Integer roleTag;

    /**
     * 评分模板名称
     */
    @Column(columnDefinition = "varchar(40)")
    private String templateName;

    /**
     * 评分模板类型 ScoringTemplateTypeEnum
     */
    @Column
    private Integer templateType;

    /**
     * 评分模板说明
     */
    @Column(columnDefinition = "varchar(100)")
    private String templateDescribe;

    /**
     * 状态: 0-停用 1-启用
     */
    @Column
    private Integer state;

    /**
     * 创建时间
     */
    @Column(columnDefinition = "int8")
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(columnDefinition = "int8")
    private Long updateTime;

    /**
     * 一对多双向关联模板标准
     */
    @JsonBackReference
    @OneToMany(mappedBy = "scoringTemplate", cascade = {CascadeType.DETACH, CascadeType.REMOVE}, fetch = FetchType.LAZY)
    private Set<MemberScoringTemplateIndicatorDO> templateIndicators;
}
