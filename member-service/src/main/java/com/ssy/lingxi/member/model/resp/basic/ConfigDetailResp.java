package com.ssy.lingxi.member.model.resp.basic;


import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 会员注册资料配置内容
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-14
 */
@Data
public class ConfigDetailResp implements Serializable {
    private static final long serialVersionUID = 8137182679194893370L;

    /**
     * 注册资料id
     */
    private Long id;

    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * 中文名称
     */
    private String fieldLocalName;

    /**
     * 字段类型
     */
    private String fieldType;

    /**
     * 字段类型附加属性
     */
    private Map<String, Object> attr;

    /**
     * 字段长度
     */
    private Integer fieldLength;

    /**
     * 是否可为空 0-不能为空 1-可以为空
     */
    private Integer fieldEmpty;

    /**
     * 字段顺序
     */
    private Integer fieldOrder;

    /**
     * 帮助信息
     */
    private String fieldRemark;

    /**
     * 枚举标签列表
     */
    private List<ConfigDetailLabelResp> fieldEnum;

    /**
     * 字段校验规则枚举：0-无校验规则，1-邮箱规则，2-手机号码规则，3-身份证规则，4-电话号码规则
     */
    private Integer ruleEnum;

    /**
     * 校验规则的正则表达式
     */
    private String pattern;

    /**
     * 校验错误的提示语
     */
    private String msg;

    /**
     * 会员注册资料配置子字段
     */
    private List<ConfigDetailResp> configs;
}
