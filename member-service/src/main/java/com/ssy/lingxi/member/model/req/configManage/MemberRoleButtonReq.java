package com.ssy.lingxi.member.model.req.configManage;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * 平台后台 - 会员角色权限 - 查询角色菜单关联按钮列表VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-04-15
 */
@Data
public class MemberRoleButtonReq implements Serializable {
    private static final long serialVersionUID = 3057185467980219335L;

    /**
     * 角色Id
     */
    @NotNull(message = "角色Id要大于0")
    @Positive(message = "角色Id要大于0")
    private Long id;

    /**
     * 菜单Id
     */
    @NotNull(message = "菜单Id要大于0")
    @Positive(message = "菜单Id要大于0")
    private Long menuId;
}
