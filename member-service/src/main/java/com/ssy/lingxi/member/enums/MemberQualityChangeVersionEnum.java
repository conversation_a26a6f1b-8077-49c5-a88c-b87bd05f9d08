package com.ssy.lingxi.member.enums;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/7/6
 */
public enum MemberQualityChangeVersionEnum {

    /**
     * 变更前的资质图片版本
     */
    USED(0, "变更前的版本"),

    /**
     * 变更后的资质图片版本
     */
    USING(1, "正在使用的版本");

    MemberQualityChangeVersionEnum(Integer code, String msg) {
        this.code = code;
    }

    private final Integer code;

    public Integer getCode() {
        return code;
    }
}
