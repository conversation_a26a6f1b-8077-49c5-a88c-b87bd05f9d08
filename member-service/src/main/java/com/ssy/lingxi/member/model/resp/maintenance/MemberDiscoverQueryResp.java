package com.ssy.lingxi.member.model.resp.maintenance;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 会员发现查询VO
 * <AUTHOR>
 * @version 1.0
 * @since 2022/7/6 15:38
 */
@Setter
@Getter
@ToString
public class MemberDiscoverQueryResp implements Serializable {
    private static final long serialVersionUID = 4608557254696007899L;

    /**
     * 会员名称
     */
    private String memberName;

    /**
     * 会员id
     */
    private Long memberId;

    /**
     * 角色id
     */
    private Long roleId;

    /**
     * 主营业务
     */
    private String mainBusiness;

    /**
     * 注册资本
     */
    private String registeredCapital;

    /**
     * 行业
     */
    private String business;

    /**
     * 所在地区
     */
    private String registerArea;

    /**
     * 会员头像
     */
    private String logo;
}
