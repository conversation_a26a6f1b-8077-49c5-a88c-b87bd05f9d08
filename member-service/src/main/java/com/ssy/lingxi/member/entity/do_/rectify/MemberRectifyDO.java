package com.ssy.lingxi.member.entity.do_.rectify;

import com.ssy.lingxi.common.constant.TableNameConstant;
import com.ssy.lingxi.member.entity.bo.FileBO;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.handler.converter.JpaJsonToFileListConverter;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 会员整改
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/5/17
 */
@Getter
@Setter
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_MEMBER_SERVICE + "member_rectify")
public class MemberRectifyDO implements Serializable {
    private static final long serialVersionUID = 2569952306326167701L;

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 整改单号
     */
    @Column(columnDefinition = "varchar(20)")
    private String rectifyNo;

    /**
     * 外部状态 1-待发送整改通知 2-待进行整改 3-待确认整改结果 4-确认整改结果不通过 5-确认整改结果通过
     */
    @Column
    private Integer outerStatus;

    /**
     * 内部状态
     */
    @Column
    private Integer innerStatus;

    /**
     * 工作流任务Id
     */
    @Column
    private String taskId;

    /**
     * 整改主题
     */
    @Column(columnDefinition = "varchar(50)")
    private String subject;

    /**
     * 多对一单向关联上級级会员
     */
    @ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(name = "member_id", referencedColumnName = "id")
    private MemberDO member;

    /**
     * 上级角色
     */
    @ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(name = "role_id", referencedColumnName = "id")
    private MemberRoleDO role;

    /**
     * 多对一单向关联下级会员
     */
    @ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(name = "sub_member_id", referencedColumnName = "id")
    private MemberDO subMember;

    /**
     * 下级会员角色
     */
    @ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(name = "sub_role_id", referencedColumnName = "id")
    private MemberRoleDO subRole;

    /**
     * 整改期限开始
     */
    @Column(columnDefinition = "timestamp")
    private LocalDateTime rectifyTimeStart;

    /**
     * 整改期限结束
     */
    @Column(columnDefinition = "timestamp")
    private LocalDateTime rectifyTimeEnd;

    /**
     * 整改原因
     */
    @Column(columnDefinition = "varchar(120)")
    private String reason;

    /**
     * 整改要求
     */
    @Column(columnDefinition = "varchar(120)")
    private String require;

    /**
     * 整改附件
     */
    @Convert(converter = JpaJsonToFileListConverter.class)
    @Column(columnDefinition = "jsonb")
    private List<FileBO> attachments;

    /**
     * 整改报告摘要
     */
    @Column(columnDefinition = "varchar(120)")
    private String reportDigest;

    /**
     * 报告附件
     */
    @Convert(converter = JpaJsonToFileListConverter.class)
    @Column(columnDefinition = "jsonb")
    private List<FileBO> reportAttachments;

    /**
     * 整改结果(是否通过) 0-否 1-是
     */
    @Column
    private Integer agreeResult;

    /**
     * 整改结果原因
     */
    @Column(columnDefinition = "varchar(120)")
    private String resultRemark;

    /**
     * 创建时间
     */
    @Column(columnDefinition = "timestamp")
    private LocalDateTime createTime;
}
