package com.ssy.lingxi.member.model.req.basic;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.PositiveOrZero;
import java.io.Serializable;

/**
 * 上级会员关系Id（可以为0）作为接口参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-19
 */
@Data
public class UpperRelationIdReq implements Serializable {
    private static final long serialVersionUID = -5737780817813434671L;

    /**
     * 会员关系Id，当没有上级会员时，可以为0
     */
    @NotNull(message = "上级会员Id要大于等于0")
    @PositiveOrZero(message = "上级会员Id要大于等于0")
    private Long upperRelationId;
}
