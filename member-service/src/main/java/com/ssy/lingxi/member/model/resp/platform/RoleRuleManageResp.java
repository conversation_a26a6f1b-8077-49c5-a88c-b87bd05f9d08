package com.ssy.lingxi.member.model.resp.platform;

import lombok.Data;

import java.io.Serializable;

/**
 * 会员角色信息查询结果VO
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-3-10
 */
@Data
public class RoleRuleManageResp implements Serializable {
    private static final long serialVersionUID = 3589923534300445094L;

    /**
     * 角色id
     */
    private Long roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色类型，标识角色是"服务提供者"、"服务消费者"
     */
    private Integer roleTypeEnum;

    /**
     * 角色类型名称
     */
    private String roleTypeName;

    /**
     * 会员类型枚举，标识角色是“企业会员”、“企业个人会员”
     */
    private Integer memberType;

    /**
     * 会员类型名称
     */
    private String memberTypeName;

    /**
     * 角色标签
     */
    private Integer roleTag;

    /**
     * 角色标签名称
     */
    private String roleTagName;
}
