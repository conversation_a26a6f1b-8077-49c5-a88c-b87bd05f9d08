package com.ssy.lingxi.member.enums;

import com.ssy.lingxi.component.base.language.LanguageHolder;

/**
 * 会员考察创建来源枚举
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-24
 */
public enum MemberInspectSourceEnum {
    /**
     * 通过会员能力的接口创建 - 0
     */
    BY_MEMBER_ABILITY(0, "通过会员能力功能创建"),

    /**
     * 通过会员审核相关流程创建 - 1
     */
    BY_MEMBER_PROCESS(1, "通过会员审核相关流程创建");

    MemberInspectSourceEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    private final Integer code;
    private final String message;

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return LanguageHolder.getTranslation(this.getClass(), this.message, this.code);
    }
}
