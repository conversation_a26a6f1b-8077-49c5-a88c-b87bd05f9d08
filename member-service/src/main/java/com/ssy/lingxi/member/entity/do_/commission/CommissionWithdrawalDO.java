package com.ssy.lingxi.member.entity.do_.commission;

import com.ssy.lingxi.common.constant.TableNameConstant;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 佣金提现明细表
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-19
 */
@Data
@Entity
@FieldNameConstants
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_MEMBER_SERVICE + "commission_withdrawal")
public class CommissionWithdrawalDO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 分佣账户id
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long commissionAccountId;

    /**
     * 用户id
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long userId;

    /**
     * 银行卡id
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long bankCardId;

    /**
     * 佣金提现流水号
     */
    @Column(columnDefinition = "varchar(32)", nullable = false, unique = true)
    private String withdrawalTradeCode;

    /**
     * 提现发起时间
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long createTime;

    /**
     * 提现金额
     */
    @Column(columnDefinition = "decimal", nullable = false)
    private BigDecimal withdrawalAmount;

    /**
     * 提现银行卡姓名
     */
    @Column(columnDefinition = "varchar(100)", nullable = false)
    private String bankCardHolderName;

    /**
     * 提现银行卡账号
     */
    @Column(columnDefinition = "varchar(50)", nullable = false)
    private String bankCardNumber;

    /**
     * 所属银行
     */
    @Column(columnDefinition = "varchar(100)", nullable = false)
    private String bankName;

    /**
     * 所属支行
     */
    @Column(columnDefinition = "varchar(200)")
    private String branchName;

    /**
     * 提现状态: 1-申请中, 2-处理中, 3-提现成功, 4-提现失败, 5-已取消
     */
    @Column(columnDefinition = "int", nullable = false)
    private Integer withdrawalStatus = 1;

    /**
     * 提现成功时间
     */
    @Column(columnDefinition = "int8")
    private Long successTime;

    /**
     * 失败原因
     */
    @Column(columnDefinition = "varchar(500)")
    private String failureReason;

    /**
     * 处理人id
     */
    @Column(columnDefinition = "int8")
    private Long processUserId;

    /**
     * 处理人姓名
     */
    @Column(columnDefinition = "varchar(100)")
    private String processUserName;

    /**
     * 处理时间
     */
    @Column(columnDefinition = "int8")
    private Long processTime;

    /**
     * 备注
     */
    @Column(columnDefinition = "varchar(500)")
    private String remark;

    /**
     * 更新时间
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long updateTime;
}
