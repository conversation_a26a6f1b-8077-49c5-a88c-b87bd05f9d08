package com.ssy.lingxi.member.model.resp.maintenance;


import com.ssy.lingxi.component.base.annotation.AesEncryptAnnotation;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户账号实名验证查询返回VO
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-06
 */
@Data
public class UserAccountAuthQueryResp implements Serializable {
    private static final long serialVersionUID = -7034244675370189141L;

    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号码（需要解密）
     */
    @AesEncryptAnnotation
    private String cardNo;

    /**
     * 身份证正面(人头面)（需要解密）
     */
    @AesEncryptAnnotation
    private String frontUrl;

    /**
     * 身份证反面(国徽面)（需要解密）
     */
    @AesEncryptAnnotation
    private String backUrl;
}
