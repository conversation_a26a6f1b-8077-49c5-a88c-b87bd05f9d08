package com.ssy.lingxi.member.model.req.basic;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * 发送邀请email VO
 * <AUTHOR>
 * @version 1.0
 * @since 2022/7/3 21:32
 */
@Setter
@Getter
@ToString
public class MemberSendInvitationEmailReq extends MemberSendInvitationIdReq implements Serializable {
    private static final long serialVersionUID = 4035079345933488108L;

    /**
     * 邮箱消息
     */
    @NotEmpty(message = "邮箱消息不能为空")
    private String emailMsg;
}
