package com.ssy.lingxi.member.model.req.maintenance;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.PositiveOrZero;
import java.io.Serializable;

/**
 * 会员物料库存模式修改VO
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/6/25
 */
@Data
public class PlatformMemberMaterialInventoryUpdateReq implements Serializable {
    private static final long serialVersionUID = 7157779381821347846L;

    /**
     * 会员id
     */
    @NotNull(message = "会员id不能为空")
    @PositiveOrZero(message = "会员Id要大于等于0")
    private Long memberId;

    /**
     * 角色id
     */
    @NotNull(message = "角色id不能为空")
    @PositiveOrZero(message = "角色Id要大于等于0")
    private Long roleId;

    /**
     * 是否开启物料库存模式
     */
    @NotBlank
    private Boolean materialInventoryMode;

    /**
     * 只有materialInventoryMode为true时才会有值
     * 需要同步物料库存的角色id
     */
    private Long synchronousRoleId;

    /**
     * 只有materialInventoryMode为true时才会有值
     * 需要同步物料库存的角色name
     */
    private String synchronousRoleName;

}
