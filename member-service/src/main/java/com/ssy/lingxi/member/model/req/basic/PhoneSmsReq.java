package com.ssy.lingxi.member.model.req.basic;

import com.ssy.lingxi.component.base.annotation.AesDecryptAnnotation;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 注册页面，校验短信注册码是否正确接口参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-12-03
 */
@Data
public class PhoneSmsReq implements Serializable {
    private static final long serialVersionUID = -5728926925247520075L;

    /**
     * 手机号码前缀
     */
    @NotBlank(message = "手机号码前缀不能为空")
    private String telCode;

    /**
     * 手机号码（需要加密）
     */
    @AesDecryptAnnotation
    @NotBlank(message = "手机号码不能为空")
    private String phone;

    /**
     * 短信验证码（需要加密）
     */
    @AesDecryptAnnotation
    @NotBlank(message = "短信验证码不能为空")
    private String smsCode;
}
