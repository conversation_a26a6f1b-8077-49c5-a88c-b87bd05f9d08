package com.ssy.lingxi.member.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.member.constant.MemberRedisConstant;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Random;
import java.util.regex.Pattern;

/**
 * 安全字符串工具类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-06
 */
public class SecurityStringUtil {

    /**
     * 屏蔽关键信息的字符串
     */
    private static final String MASK_STR = "*";

    /**
     * Email的符号
     */
    private static final String EMAIL_SYMBOL = "@";

    /**
     * 短信验证码随机生成器
     */
    private static final Random RANDOM = new Random();

    /**
     * 屏蔽手机号码中间4位
     *
     * @param telCode     手机号码前缀
     * @param phone       手机号码
     * @return 屏蔽字符串
     */
    public static String getMaskPhone(String telCode, String phone) {
        if (!StringUtils.hasLength(phone)) {
            return "";
        }

        telCode = StringUtils.hasLength(telCode) ? telCode : "";
        return telCode + " " + phone.substring(0, 3) + String.join("", Collections.nCopies(4, "*")) + phone.substring(7);
    }

    /**
     * 屏蔽邮箱地址
     *
     * @param email 邮箱地址
     * @return 屏蔽字符串
     */
    public static String getMaskMail(String email) {
        if (!StringUtils.hasLength(email) || !email.contains(EMAIL_SYMBOL)) {
            return "";
        }

        String emailAccount = email.substring(0, email.indexOf(EMAIL_SYMBOL));
        String emailDomain = email.substring(email.indexOf(EMAIL_SYMBOL));
        switch (emailAccount.length()) {
            case 0:
            case 1:
                return MASK_STR + emailDomain;
            case 2:
                return emailAccount.charAt(0) + MASK_STR + emailDomain;
            case 3:
            case 4:
            case 5:
                return emailAccount.charAt(0) + String.join("", Collections.nCopies(emailAccount.length() - 2, MASK_STR)) + emailAccount.substring(emailAccount.length() - 1) + emailDomain;
            default:
                return emailAccount.substring(0, 2) + String.join("", Collections.nCopies(emailAccount.length() - 4, MASK_STR)) + emailAccount.substring(emailAccount.length() - 2) + emailDomain;
        }
    }

    /**
     * 生成6位数字的短信验证码
     *
     * @return 短信验证码
     */
    public static String getRandomSmsCode() {
        int code = RANDOM.nextInt(1000000);
        return String.format("%06d", code);
    }

    /**
     * 将前端传递的注册资料标签，转换为List<String>
     * @param src 注册资料信息，在Map<String, Object>中的value
     * @return 如果转换成功，则返回转换结果，否则返回Null
     */
    public static List<String> convertStringToList(String src) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.readValue(src, new TypeReference<List<String>>() {
            });
        } catch (Exception e1) {
            return null;
        }
    }

    /**
     * 将前端传递的注册资料标签，转换为List<Long>
     * @param src 注册资料信息，在Map<String, Object>中的value
     * @return 如果转换成功，则返回转换结果，否则返回Null
     */
    public static List<Long> convertStringToLongList(String src) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.readValue(src, new TypeReference<List<Long>>() {
            });
        } catch (Exception e1) {
            return null;
        }
    }

    /**
     * 将前端传递的注册资料标签，转换为Long<Long>
     * @param src 注册资料信息，在Map<String, Object>中的value
     * @return 如果转换成功，则返回转换结果，否则返回Null
     */
    public static Long convertStringToLong(String src) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            if(!StringUtils.hasLength(src)) {
                return null;
            }

            return Long.parseLong(src);
        } catch (Exception e1) {
            return null;
        }
    }

    /**
     * 检查字符串是否符合yyyy-MM-dd格式
     * @param dateStr 日期字符串
     * @return 是/否
     */
    public static boolean notDateString(String dateStr) {
        final String slash = "-";
        if(!StringUtils.hasLength(dateStr) || !dateStr.contains(slash)) {
            return true;
        }

        try {
            LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } catch (Exception e) {
            return true;
        }

        return false;
    }

    /**
     * 校验手机号码格式是否正确
     * @param telCode 手机号码前缀
     * @param phone 手机号码
     * @return 格式是否正确
     */
    public static boolean checkPhone(String telCode, String phone) {
        if ("+86".equals(telCode)) {
            return checkChinaPhone(phone);
        }

        return checkPhoneByNumber(phone);
    }

    /**
     * 校验国内手机号码格式
     * @param phone 手机号码
     * @return 格式是否正确
     */
    public static boolean checkChinaPhone(String phone) {
        if(!StringUtils.hasLength(phone)) {
            return false;
        }

        String regex = "^(1[3-9]\\d{9}$)";
        return Pattern.compile(regex).matcher(phone).matches();
    }

    /**
     * 校验手机号是否由数字组成
     * @param phone 手机号码
     */
    public static boolean checkPhoneByNumber(String phone) {
        if(!StringUtils.hasLength(phone) || phone.length() > 20) {
            return false;
        }

        String regex = "^\\d+$";
        return Pattern.compile(regex).matcher(phone).matches();
    }

    /**
     * 按角色标签替换文本中带会员的字段（兼容国际化）<br>
     * 如：当角色标签2（供应商），会员入库审核不通过 -> 供应商入库审核不通过<br>
     * 2022年718版本将会员拆成供应商和客户，要求将原会员字段全部替换成对应角色标签下的字段，之所以使引入该方法，是为了兼容旧数据。
     * @param str     会员编码串
     * @param roleTag 角色标签
     * @return 字符串
     */
    @NonNull
    public static String replaceMemberPrefix(@NonNull String str, @NonNull Integer roleTag) {
        if (StringUtils.hasLength(str) && NumberUtil.notNullOrZero(roleTag)) {
            return str.replace(RoleTagEnum.MEMBER.getName(), RoleTagEnum.getName(roleTag));
        }
        return str;
    }

    /**
     * 按角色标签替换文本中对应角色标签字段（兼容国际化）<br>
     *
     * @param str           会员编码串
     * @param sourceRoleTag 原角色标签
     * @param targetRoleTag 目标角色标签
     * @return 字符串
     */
    @NonNull
    public static String replaceMemberPrefix(@NonNull String str, @NonNull Integer sourceRoleTag, @NonNull Integer targetRoleTag) {
        return str.replace(RoleTagEnum.getName(sourceRoleTag), RoleTagEnum.getName(targetRoleTag));
    }

    /**
     * 如果str不等于空，返回str本身，否则返回空字符串
     */
    @NonNull
    public static String getOrDefault(@Nullable String str) {
        return Objects.nonNull(str) ? str : "";
    }

    /**
     * 用来生成阻止重复发送短信验证码的缓存key
     */
    public static String getSmsCodeSecurityKey(String cacheKey) {
        return cacheKey + "_" + MemberRedisConstant.SMS_CODE_SECURITY_SUFFIX;
    }

    private SecurityStringUtil() {
    }
}
