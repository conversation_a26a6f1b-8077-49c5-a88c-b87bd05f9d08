package com.ssy.lingxi.member.model.req.maintenance;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 新增会员角色接口参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-30
 */
@Data
public class MemberRoleAddReq implements Serializable {

    private static final long serialVersionUID = -375268474785103780L;

    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称不能为空")
    @Size(max = 20, message = "角色名称最长20个字符")
    private String roleName;

    /**
     * 角色描述
     */
    @Size(max = 120, message = "角色描述最长120个字符")
    private String remark;

    /**
     * 权限列表，左侧勾选菜单的id列表
     */
    private List<Long> menuIds;

    /**
     * IM客服权限标识 0：否；1：是
     * */
    @NotNull(message = "IM客服权限标识不能为空")
    private Integer imFlag;
}
