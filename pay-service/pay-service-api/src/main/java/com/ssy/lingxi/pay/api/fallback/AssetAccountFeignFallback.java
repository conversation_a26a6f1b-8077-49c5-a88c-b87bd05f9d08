package com.ssy.lingxi.pay.api.fallback;

import com.ssy.lingxi.common.model.req.api.member.ActualControllerSyncReq;
import com.ssy.lingxi.common.model.req.api.pay.MaterialStockDataSyncReq;
import com.ssy.lingxi.common.model.req.api.pay.MemberAssetAccountLineOfCreditSyncReq;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.pay.api.feign.IAssetAccountFeign;
import com.ssy.lingxi.pay.api.model.req.CheckAssetAccountReq;
import com.ssy.lingxi.pay.api.model.req.MemberIdsReq;
import com.ssy.lingxi.pay.api.model.req.assetAccount.*;
import com.ssy.lingxi.pay.api.model.resp.assetAccount.AccountPayChannelResultResp;
import com.ssy.lingxi.pay.api.model.resp.assetAccount.EditLineOfCreditReq;
import com.ssy.lingxi.pay.api.model.resp.assetAccount.MemberAssetAccountResp;
import com.ssy.lingxi.pay.api.model.resp.assetAccount.MemberLineOfCreditResp;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 资金账户
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/9/24
 */
@Slf4j
public class AssetAccountFeignFallback implements IAssetAccountFeign {

    private final Throwable throwable;

    public AssetAccountFeignFallback(Throwable cause) {
        this.throwable = cause;
    }

    @Override
    public WrapperResp<String> saveMemberAssetAccount(List<MemberAssetAccountAddReq> memberAssetAccountRequestList) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_PAY_ERROR);
    }

    @Override
    public WrapperResp<Boolean> updateMemberAssetAccount(MemberAssetAccountUpdateReq memberAssetAccountUpdateReq) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_PAY_ERROR);
    }

    @Override
    public WrapperResp<String> balancePay(BalancePayReq balancePayReq) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_PAY_ERROR);
    }

    @Override
    public WrapperResp<String> balanceCashback(BalanceCashbackReq balanceCashbackReq) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_PAY_ERROR);
    }

    @Override
    public WrapperResp<Boolean> balanceRefund(BalanceRefundReq balanceRefundReq) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_PAY_ERROR);
    }

    @Override
    public WrapperResp<Boolean> checkAssetAccount(CheckAssetAccountReq request) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_PAY_ERROR);
    }

    @Override
    public WrapperResp<Boolean> memberAssetAccountLineOfCreditSync(MemberAssetAccountLineOfCreditSyncReq memberAssetAccountSyncReq) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_PAY_ERROR);
    }

    @Override
    public WrapperResp<Boolean> memberAssetAccountMaterialStockSync(MaterialStockDataSyncReq materialStockDataSyncReq) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_PAY_ERROR);
    }

    @Override
    public WrapperResp<List<MemberLineOfCreditResp>> findByMemberIds(MemberIdsReq memberIdsReq) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_PAY_ERROR);
    }

    @Override
    public WrapperResp<Boolean> lineOfCreditSync(ActualControllerSyncReq req) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_PAY_ERROR);
    }

    @Override
    public WrapperResp<Boolean> editLineOfCredit(List<EditLineOfCreditReq> editLineOfCreditReqs) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_PAY_ERROR);
    }

    @Override
    public WrapperResp<AccountPayChannelResultResp> frozenAccountBalance(FrozenAccountBalanceReq frozenAccountBalanceReq) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_PAY_ERROR);
    }

    @Override
    public WrapperResp<Boolean> unFrozenAccountBalance(UnFrozenAccountBalanceReq unFrozenAccountBalanceReq) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_PAY_ERROR);
    }

    @Override
    public WrapperResp<Boolean> unLockAccountBalance(UnLockAccountBalanceReq unFrozenAccountBalanceReq) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_PAY_ERROR);
    }

    @Override
    public WrapperResp<Boolean> payFinishAndUnLock(PayFinishAndUnLockReq payFinishAndUnLockReq) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_PAY_ERROR);
    }

    @Override
    public WrapperResp<MemberAssetAccountResp> getEosAccountBalance(Long memberId) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_PAY_ERROR);
    }

    @Override
    public WrapperResp<MemberAssetAccountResp> getEosAccountBalanceAndFrozenAccountBalance(GetEosAccountBalanceAndFrozenAccountBalanceReq eosAccountBalanceReq) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_PAY_ERROR);
    }

}
