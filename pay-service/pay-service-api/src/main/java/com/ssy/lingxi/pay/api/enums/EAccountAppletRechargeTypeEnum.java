package com.ssy.lingxi.pay.api.enums;

import com.ssy.lingxi.common.model.resp.MapResp;

import java.util.ArrayList;
import java.util.List;

/**
 * e账户管理-通联-充值类型
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/12/2
 */
public enum EAccountAppletRechargeTypeEnum {
    WECHATPAY_MINIPROGRAM_ORG("WECHATPAY_MINIPROGRAM_ORG", "微信支付"),
    QUICKPAY_VSP("QUICKPAY_VSP", "快捷支付");

    private String code;
    private String msg;

    EAccountAppletRechargeTypeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static String getMessage(String code) {
        EAccountAppletRechargeTypeEnum[] values = EAccountAppletRechargeTypeEnum.values();
        for (EAccountAppletRechargeTypeEnum allInPayPayMethodEnum : values) {
            if (allInPayPayMethodEnum.code.equals(code)) {
                return allInPayPayMethodEnum.msg;
            }
        }
        return null;
    }

    public static List<MapResp> getCodes(){
        List<MapResp> mapResponseList = new ArrayList<>();
        for (EAccountAppletRechargeTypeEnum e : EAccountAppletRechargeTypeEnum.values()) {
            MapResp mapResponse = new MapResp();
            mapResponse.setKey(e.getCode());
            mapResponse.setValue(e.getMsg());
            mapResponseList.add(mapResponse);
        }
        return mapResponseList;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
