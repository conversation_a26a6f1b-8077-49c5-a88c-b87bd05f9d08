package com.ssy.lingxi.pay.api.model.resp.allInPay;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/12/4
 */
@Data
public class ConsumeApplyResp implements Serializable {

    /**
     * 支付状态 仅交易验证方式为“0”时返回 成功：success 进行中：pending 失败：fail 订单成功时会发订单结果通知商户。 其中收银宝 POS/当面付查询模式、手 机银行 APP、微信支付宝等须用户在 终端确认支付的，不返回此字段
     */
    private String payStatus;
    /**
     * 支付失败信息  仅交易验证方式为“0”时返回 只有 payStatus 为 fail 时有效
     */
    private String payFailMessage;
    /**
     * 商户系统用户标识，商户 系统中唯一编号。 仅交易验证方式为“0”时返回 平台，返回#yunBizUserId_B2C#
     * 付款人
     */
    private String bizUserId;
    /**
     * 云商通订单号
     */
    private String orderNo;

    /**
     * 商户订单号（支付订单）
     */
    private String bizOrderNo;
    /**
     * 交易编号
     */
    private String tradeNo;
    /**
     * 微信 APP 支付信息
     */
    private String weChatAPPInfo;
    /**
     * 扫码支付信息/JS支付串信息（微信、支付宝、QQ钱包）/微信小程序/微信原生H5支付串信息/支付宝原生APP支付串信息
     */
    private String payInfo;
    /**
     * 支付人帐号 (仅收银宝-付款码支付方式返回， 微信支付的 openid 支付宝平台的 user_id)
     */
    private String acct;
    /**
     * 扩展参数
     */
    private String extendInfo;

}
