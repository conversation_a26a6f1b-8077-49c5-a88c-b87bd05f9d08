package com.ssy.lingxi.pay.api.feign;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.api.member.ActualControllerSyncReq;
import com.ssy.lingxi.common.model.req.api.pay.MaterialStockDataSyncReq;
import com.ssy.lingxi.common.model.req.api.pay.MemberAssetAccountLineOfCreditSyncReq;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.pay.api.fallback.factory.AssetAccountFeignFallbackFactory;
import com.ssy.lingxi.pay.api.model.req.CheckAssetAccountReq;
import com.ssy.lingxi.pay.api.model.req.MemberIdsReq;
import com.ssy.lingxi.pay.api.model.req.assetAccount.*;
import com.ssy.lingxi.pay.api.model.resp.assetAccount.AccountPayChannelResultResp;
import com.ssy.lingxi.pay.api.model.resp.assetAccount.EditLineOfCreditReq;
import com.ssy.lingxi.pay.api.model.resp.assetAccount.MemberAssetAccountResp;
import com.ssy.lingxi.pay.api.model.resp.assetAccount.MemberLineOfCreditResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;
import java.util.Set;

/**
 * 资金账户内部接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/9/24
 */
@FeignClient(name = ServiceModuleConstant.PAY_SERVICE, fallbackFactory = AssetAccountFeignFallbackFactory.class)
public interface IAssetAccountFeign {

    String PATH_PREFIX = ServiceModuleConstant.PAY_FEIGN_PATH_PREFIX + "/assetAccount/";
    
    /**
     * 新增会员资金账户
     * @param memberAssetAccountRequestList 会员资金账户实体
     */
    @PostMapping(value = PATH_PREFIX + "saveMemberAssetAccount")
    WrapperResp<String> saveMemberAssetAccount(@RequestBody List<MemberAssetAccountAddReq> memberAssetAccountRequestList);

    /**
     * 修改会员资金账户状态
     * @param memberAssetAccountUpdateReq 会员资金账户实体
     */
    @PostMapping(value = PATH_PREFIX + "updateMemberAssetAccount")
    WrapperResp<Boolean> updateMemberAssetAccount(@RequestBody MemberAssetAccountUpdateReq memberAssetAccountUpdateReq);

    /**
     * 余额支付
     * @param balancePayReq 参数
     */
    @PostMapping(value = PATH_PREFIX + "balancePay")
    WrapperResp<String> balancePay(@RequestBody BalancePayReq balancePayReq);

    /**
     * 余额返现
     * @param balanceCashbackReq 参数
     */
    @PostMapping(value = PATH_PREFIX + "balanceCashback")
    WrapperResp<String> balanceCashback(@RequestBody BalanceCashbackReq balanceCashbackReq);

    /**
     * 余额退款
     * @param balanceRefundReq 参数
     */
    @PostMapping(value = PATH_PREFIX + "balanceRefund")
    WrapperResp<Boolean> balanceRefund(@RequestBody BalanceRefundReq balanceRefundReq);

    /**
     * 校验当前会员资金账号状态
     * true:存在资金账户, 并且账户余额不为0; false:无资金账户,或者账户余额为0
     * @param request 参数
     */
    @PostMapping(value = PATH_PREFIX + "checkAssetAccount")
    WrapperResp<Boolean> checkAssetAccount(@Valid @RequestBody CheckAssetAccountReq request);

    /**
     * 同步会员资金账户-授信信息
     * @param memberAssetAccountSyncReq
     * @return
     */
    @PostMapping(value = PATH_PREFIX + "memberAssetAccountLineOfCreditSync")
    WrapperResp<Boolean> memberAssetAccountLineOfCreditSync(@RequestBody MemberAssetAccountLineOfCreditSyncReq memberAssetAccountSyncReq);

    /**
     * 同步会员资金账户-物料信息
     * @param materialStockDataSyncReq
     * @return
     */
    @PostMapping(value = PATH_PREFIX + "memberAssetAccountMaterialStockSync")
    WrapperResp<Boolean> memberAssetAccountMaterialStockSync(@RequestBody MaterialStockDataSyncReq materialStockDataSyncReq);

    /**
     * 根据会员ID查询会员资金账户
     * @param memberIds 会员ID列表
     * @return 会员资金账户响应
     */
    @PostMapping(value = PATH_PREFIX + "findByMemberIds")
    WrapperResp<List<MemberLineOfCreditResp>> findByMemberIds(@RequestBody MemberIdsReq memberIds);

    /**
     * 同步会员授信信息
     * @param req 授信信息列表
     * @return 操作结果
     */
    @PostMapping(value = PATH_PREFIX + "lineOfCreditSync")
    WrapperResp<Boolean> lineOfCreditSync(@RequestBody ActualControllerSyncReq req);

    /**
     * 同步会员授信信息
     * @param editLineOfCreditReqs 授信信息列表
     * @return 操作结果
     */
    @PostMapping(value = PATH_PREFIX + "editLineOfCredit")
    WrapperResp<Boolean> editLineOfCredit(@RequestBody List<EditLineOfCreditReq> editLineOfCreditReqs);


    /**
     *  冻结资金账户余额
     * @param frozenAccountBalanceReq
     * @return
     */
    @PostMapping(value = PATH_PREFIX+"frozenAccountBalance")
    public WrapperResp<AccountPayChannelResultResp> frozenAccountBalance(@RequestBody @Valid FrozenAccountBalanceReq frozenAccountBalanceReq);


    /**
     * 解冻资金账户余额
     * @param unFrozenAccountBalanceReq
     * @return
     */
    @PostMapping(value = PATH_PREFIX+"unFrozenAccountBalance")
    public WrapperResp<Boolean> unFrozenAccountBalance(@RequestBody UnFrozenAccountBalanceReq unFrozenAccountBalanceReq);


    /**
     * 充值资金账户余额
     * @param
     * @return
     */
    @PostMapping(value = PATH_PREFIX+"unLockAccountBalance")
    public WrapperResp<Boolean> unLockAccountBalance(@RequestBody UnLockAccountBalanceReq unFrozenAccountBalanceReq);


    /**
     *  扣除冻结金额，完成支付
     */
    @PostMapping(value = PATH_PREFIX+"/payFinishAndUnLock")
    WrapperResp<Boolean> payFinishAndUnLock(@RequestBody PayFinishAndUnLockReq payFinishAndUnLockReq);

    /**
     * 从eos获取账户余额
     * @param memberId
     * @return
     */
    @PostMapping(value = PATH_PREFIX+"/getEosAccountBalance")
    public WrapperResp<MemberAssetAccountResp> getEosAccountBalance(@RequestParam("memberId") Long memberId);

    /**
     * 获取Eos账号余额信息和订单冻结的金额
     * 将orderPaymentId订单冻结的余额/存料重新计算进可用余额/存料中
     * @param eosAccountBalanceReq
     * @return
     */
    @PostMapping(value = PATH_PREFIX+"/getEosAccountBalanceAndFrozenAccountBalance")
    public WrapperResp<MemberAssetAccountResp> getEosAccountBalanceAndFrozenAccountBalance(@RequestBody @Valid GetEosAccountBalanceAndFrozenAccountBalanceReq eosAccountBalanceReq);

}
