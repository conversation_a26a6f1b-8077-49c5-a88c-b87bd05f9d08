package com.ssy.lingxi.pay.api.model.req.eAccount;

import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * e账号退款实体
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/12/9
 */
@Data
public class EAccountRefundReq implements Serializable {
    private static final long serialVersionUID = -1258232046870601994L;

    /**
     * 会员id(付款方)
     */
    @NotNull(message = "会员id要大于0")
    @Positive(message = "会员id要大于0")
    private Long memberId;

    /**
     * 会员角色id(付款方)
     */
    @NotNull(message = "角色id要大于0")
    @Positive(message = "角色id要大于0")
    private Long memberRoleId;

    /**
     * 退款订单号
     */
    @NotBlank(message = "退款订单号不能为空")
    @Size(max = 32, message = "退款订单号最长32个字符")
    private String orderCode;

    /**
     * 原支付订单号
     */
    @NotBlank(message = "原支付订单号不能为空")
    @Size(max = 32, message = "原支付订单号最长32个字符")
    private String payCode;

    /**
     * 退款金额
     */
    @NotNull(message = "退款金额要大于等于0")
    @PositiveOrZero(message = "退款金额要大于等于0")
    private BigDecimal refundMoney;

    /**
     * 退款金额类型(代收代付时使用) 0-全额 1-部分
     *
     */
    private Integer refundType=0;
    //----------**退部分金额时需要用到一下字段**-------------
    /**
     * 会员id(收款方)
     */
    private Long reMemberId;

    /**
     * 会员角色id(收款方)
     */
    private Long reMemberRoleId;

    /**
     * 下单时付款业务id
     */
    private String buyerBizUserId;

}
