package com.ssy.lingxi.pay.api.model.req.assetAccount;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Set;

/**
 * 获取eos账号余额信息和订单冻结的金额
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2025/7/23
 */
@Data
public class GetEosAccountBalanceAndFrozenAccountBalanceReq {

    /**
     * 会员id
     */
    @NotNull(message = "会员id,不能为空")
    private Long memberId;

    /**
     *  支付记录id
     */
    private Set<String> paymentIds;

}
