package com.ssy.lingxi.pay.api.model.resp;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 授信VO
 * <AUTHOR>
 * @since 2020/11/3
 * @version 2.0.0
 */
public class CreditFeignDetailResp implements Serializable {
    private static final long serialVersionUID = -1331662624200809540L;

    /**
     * 现有额度
     */
    private BigDecimal quota;

    /**
     * 已用额度
     */
    private BigDecimal useQuota;

    /**
     * 可用额度
     */
    private BigDecimal canUseQuota;

    /**
     * 是否可用 0-否 1-是
     */
    private Integer isUsable;

    public BigDecimal getQuota() {
        return quota;
    }

    public void setQuota(BigDecimal quota) {
        this.quota = quota;
    }

    public BigDecimal getUseQuota() {
        return useQuota;
    }

    public void setUseQuota(BigDecimal useQuota) {
        this.useQuota = useQuota;
    }

    public BigDecimal getCanUseQuota() {
        return canUseQuota;
    }

    public void setCanUseQuota(BigDecimal canUseQuota) {
        this.canUseQuota = canUseQuota;
    }

    public Integer getIsUsable() {
        return isUsable;
    }

    public void setIsUsable(Integer isUsable) {
        this.isUsable = isUsable;
    }
}
