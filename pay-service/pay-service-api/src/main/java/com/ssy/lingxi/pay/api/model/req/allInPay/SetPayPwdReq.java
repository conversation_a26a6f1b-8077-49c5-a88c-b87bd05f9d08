package com.ssy.lingxi.pay.api.model.req.allInPay;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 设置/修改/重置支付密码实体类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/12/2
 */
@Data
public class SetPayPwdReq implements Serializable {
    /**
     * 1-H5 页面 2-小程序页面 兼容存量模式，不上送默 认跳转 H5 页面
     */
    private Integer jumpPageType = 1;
    /**
     * 手机号码
     */
    @NotNull(message = "手机号码不能为空")
    private String phone;

    /**
     * 用户名称
     */
    @NotNull(message = "用户名称不能为空")
    private String name;

    /**
     * 证件类型 1-身份证
     */
    @NotNull(message = "证件类型不能为空")
    private Integer identityType;

    /**
     * 证件号码
     */
    @NotNull(message = "证件号码不能为空")
    private String identityNo;

    /**
     * 类型 1-设置支付密码 2-修改支付密码 3-重置支付密码
     */
    @NotNull(message = "类型不能为空")
    private Integer type;
}
