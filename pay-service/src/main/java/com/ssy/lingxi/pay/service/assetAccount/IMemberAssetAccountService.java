package com.ssy.lingxi.pay.service.assetAccount;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.req.api.member.ActualControllerSyncReq;
import com.ssy.lingxi.common.model.req.api.pay.MaterialStockDataSyncReq;
import com.ssy.lingxi.common.model.req.api.pay.MemberAssetAccountLineOfCreditSyncReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.pay.api.model.req.CheckAssetAccountReq;
import com.ssy.lingxi.pay.api.model.req.MemberIdsReq;
import com.ssy.lingxi.pay.api.model.req.assetAccount.*;
import com.ssy.lingxi.pay.api.model.resp.assetAccount.*;
import com.ssy.lingxi.pay.entity.do_.assetAccount.MemberAssetAccountDO;
import com.ssy.lingxi.pay.model.req.MemberAssetAccountReq;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * 会员资金账户
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/21
 */
public interface IMemberAssetAccountService {
    /**
     * 查询会员资金账户
     * @param pageDataReq
     * @param memberAssetAccountMiddle
     * @return
     */
    Page<MemberAssetAccountDO> getMemberAssetAccountList(PageDataReq pageDataReq, MemberAssetAccountReq memberAssetAccountMiddle);

    /**
     * 查询会员资金账户
     * @param id
     * @return Wrapper<MemberAssetAccount>
     */
    MemberAssetAccountDO getMemberAssetAccount(Long id);

    /**
     * 添加会员资金账户
     * @param memberAssetAccountDOList
     * @return
     */
    String saveOrUpdateMemberAssetAccount(List<MemberAssetAccountDO> memberAssetAccountDOList);

    /**
     * 修改会员资金账户
     * @param memberAssetAccountUpdateReq
     * @return
     */
    Boolean updateMemberAssetAccount(MemberAssetAccountUpdateReq memberAssetAccountUpdateReq);

    /**
     * 冻结/解冻会员资金账户
     * @param id
     * @param status
     */
    void updateMemberAssetAccountEnable(Long id, Integer status, String remark);

    /**
     * 资金账户充值
     * @param rechargeReq
     * @return
     */
    AccountRechargeResp rechargeAssetAccount(HttpServletRequest request, RechargeReq rechargeReq);

    /**
     * 资金账户充值(app)
     * @param rechargeReq
     * @return
     */
    AccountRechargeAppResp rechargeAssetAccountApp(HttpServletRequest request, RechargeReq rechargeReq);

    /**
     * 资金账户充值(小程序)
     * @param rechargeAppletReq
     * @return
     */
    AccountRechargeAppletResp rechargeAssetAccountApplet(HttpServletRequest request, RechargeAppletReq rechargeAppletReq);

    /**
     * 资金账户充值(H5)
     *
     * @param request
     * @param rechargeJsApiReq
     * @return
     */
    AccountRechargeJsApiResp rechargeAssetAccountJsApi(UserLoginCacheDTO userLoginCacheDTO,HttpServletRequest request, RechargeJsApiReq rechargeJsApiReq);

    /**
     * 查询账户充值结果
     * @param tradeRecordId
     * @return
     */
    Boolean getRechargeResult(Long tradeRecordId);

    /**
     * 支付回调
     * @param out_trade_no 交易单号
     * @param trade_no 支付平台订单号
     */
    void payNotify(String out_trade_no, String trade_no);

    /**
     * 更新交易记录为支付失败
     * @param accountTradeRecordId 交易记录id
     */
    void updateAccountTradeRecordPayingFail(Long accountTradeRecordId);

    /**
     * 资金账户提现
     * @param cashOutReq
     * @return
     */
    void cashOutAssetAccount(CashOutReq cashOutReq);

    /**
     * 支付提现
     * @param payReq
     */
    void payCashOut(PayReq payReq);

    /**
     * 余额支付
     * @param balancePayReq
     * @return
     */
    String balancePay(BalancePayReq balancePayReq);

    /**
     * 余额返现
     * @param balanceCashbackReq
     * @return
     */
    String balanceCashback(BalanceCashbackReq balanceCashbackReq);

    /**
     * 余额退款
     * @param balanceRefundReq
     * @return
     */
    Boolean balanceRefund(BalanceRefundReq balanceRefundReq);

    /**
     * 统一检验账户
     * @return
     */
    void checkMemberAssetAccount(MemberAssetAccountDO memberAssetAccountDO);

    /**
     * 查询登录用户的账户余额
     * @param fundMode 资金归集模式: 1-平台; 2-会员直接到账
     * @param parentMemberId 上级会员id
     * @param parentMemberRoleId 上级会员角色id
     */
    BigDecimal getUserBalance(Integer fundMode, Long memberId, Long memberRoleId, Long parentMemberId, Long parentMemberRoleId);

    /**
     * 查询采购商的账户余额
     * @param childMemberId 下级会员id
     * @param childMemberRoleId 下级会员角色id
     */
    BigDecimal getChildUserBalance(Long memberId, Long memberRoleId, Long childMemberId, Long childMemberRoleId);

    /**
     * 查询登录用户的所有账户余额
     * @param memberId
     * @param memberRoleId
     * @return
     */
    BigDecimal getUserAllBalance(Long memberId, Long memberRoleId);

    /**
     * 查询平台账户详情
     */
    MemberAssetAccountDO getPlatFormAssetAccount(UserLoginCacheDTO sysUser);

    /**
     * 查询商户账户详情
     * @param sysUser
     * @return
     */
    public MemberAssetAccountDO getMerchantFormAssetAccount(UserLoginCacheDTO sysUser);

    /**
     * 查询会员用户的账户详情
     * @param memberId              登录用户会员id
     * @param memberRoleId          登录用户会员角色id
     * @param parentMemberId        上级会员id
     * @param parentMemberRoleId    上级会员角色id
     * @return  账户详情
     */
    MemberAssetAccountResp getUserAssetAccount(Long memberId, Long memberRoleId, Long parentMemberId, Long parentMemberRoleId);

    /**
     * 校验当前会员资金账号状态
     * true:存在资金账户, 并且账户余额不为0; false:无资金账户,或者账户余额为0
     * @param request 参数
     */
    Boolean checkAssetAccount(CheckAssetAccountReq request);

    /**
     * 分页查询会员资金账户
     * @param memberAssetAccountPageQueryReq
     * @param memberAssetAccountReq
     * @return
     */
    public PageDataResp<MemberAssetAccountResp> getMemberAssetAccountListV2(MemberAssetAccountPageQueryReq memberAssetAccountPageQueryReq, MemberAssetAccountReq memberAssetAccountReq);

    /**
     * 同步会员账户 - 授信信息
     * @param memberAssetAccountSyncReq
     * @return
     */
    void memberAssetAccountLineOfCreditSync(MemberAssetAccountLineOfCreditSyncReq memberAssetAccountSyncReq);

    /**
     * 同步会员账户 - 物料信息
     * @param materialStockDataSyncReq
     * @return
     */
    void memberAssetAccountMaterialStockSync(MaterialStockDataSyncReq materialStockDataSyncReq);

    /**
     * 查询账户信息详情
     * @param id
     * @return
     */
    public MemberAssetAccountResp getMemberAssetAccountV2(Long id);

    /**
     * 账户充值 余额（元）/物料（克）
     * @param depositAccountReq
     * @return
     */
    void depositAccount(DepositAccountReq depositAccountReq);

    /**
     * 修改授信额度
     * @param updateLineOfCreditReq
     * @return
     */
    void updateLineOfCredit(@Valid UpdateLineOfCreditReq updateLineOfCreditReq);

    /**
     * 冻结账户余额
     * @param frozenAccountBalanceReq
     * @return
     */
    AccountPayChannelResultResp frozenAccountBalance(FrozenAccountBalanceReq frozenAccountBalanceReq);

    /**
     * 导出会员资金账户数据
     * @param response
     * @param memberAssetAccountMiddle
     */
    void export(HttpServletResponse response, MemberAssetAccountReq memberAssetAccountMiddle, MemberAssetAccountPageQueryReq memberAssetAccountPageQueryReq);

    /**
     * 查询登录用户的账户余额
     * @param sysUser
     * @return
     */
    MemberAssetAccountResp getAccountBalance(UserLoginCacheDTO sysUser);


    /**
     * 查询登录用户的EOS账户余额
     * @param sysUser
     * @return
     */
    public MemberAssetAccountResp getEosAccountBalance(Long memberId);

    /**
     * 管理员操作 账户充值
     * @param depositAccountReq
     * @return
     */
    Boolean depositAccountByManager(DepositAccountByManagerReq depositAccountReq);

    /**
     * 根据会员ID查询会员资金账户
     * @param ids
     * @return
     */
    List<MemberLineOfCreditResp> findByMemberIds(MemberIdsReq ids);

    /**
     * 同步会员授信信息
     * @param req 授信信息列表
     */
    void lineOfCreditSync(ActualControllerSyncReq req);

    /**
     * 同步客户授信信息
     * @param editLineOfCreditReqs 授信信息列表
     */
    void editLineOfCredit(List<EditLineOfCreditReq> editLineOfCreditReqs);

    /**
     * 解冻账户余额
     * @param unFrozenAccountBalanceReq
     * @return
     */
    Boolean unFrozenAccountBalance(UnFrozenAccountBalanceReq unFrozenAccountBalanceReq);


    /**
     * 解冻存料和存款
     * @param unFrozenAccountBalanceReq
     * @return
     */
    Boolean unLockAccountBalance(@RequestBody @Valid UnLockAccountBalanceReq unFrozenAccountBalanceReq);

    /**
     * 支付完成，扣除冻结金额
     * @return
     */
    Boolean payFinishAndUnLock(@RequestBody @Valid PayFinishAndUnLockReq payFinishAndUnLockReq);

    /**
     * 获取Eos账号余额信息和订单冻结的金额
     * 将orderPaymentId订单冻结的余额/存料重新计算进可用余额/存料中
     * @param eosAccountBalanceReq
     * @return
     */
    MemberAssetAccountResp getEosAccountBalanceAndFrozenAccountBalance(@RequestBody @Valid GetEosAccountBalanceAndFrozenAccountBalanceReq eosAccountBalanceReq);
}
