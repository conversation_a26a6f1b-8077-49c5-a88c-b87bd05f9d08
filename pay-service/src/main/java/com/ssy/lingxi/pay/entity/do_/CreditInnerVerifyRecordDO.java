package com.ssy.lingxi.pay.entity.do_;

import com.ssy.lingxi.common.constant.TableNameConstant;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 授信内部审核记录表
 * <AUTHOR>
 * @since 2020/8/18
 * @version 2.0.0
 */
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_PAY_SERVICE + "credit_inner_verify_record",
        indexes = {@Index(name = TableNameConstant.TABLE_PRE_PAY_SERVICE + "credit_inner_verify_record_credit_id_idx", columnList = "creditId"),
                @Index(name = TableNameConstant.TABLE_PRE_PAY_SERVICE + "credit_inner_verify_record_apply_id_idx", columnList = "applyId"),
                @Index(name = TableNameConstant.TABLE_PRE_PAY_SERVICE + "credit_inner_verify_record_member_id_idx", columnList = "memberId"),
                @Index(name = TableNameConstant.TABLE_PRE_PAY_SERVICE + "credit_inner_verify_record_role_id_idx", columnList = "roleId")})
public class CreditInnerVerifyRecordDO implements Serializable {
    private static final long serialVersionUID = -3646567311757524217L;

    /**
     * 审核记录id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 授信id
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long creditId;

    /**
     * 授信申请id
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long applyId;

    /**
     * 步骤
     */
    @Column(columnDefinition = "int", nullable = false)
    private Integer step;

    /**
     * 用户id
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long userId;

    /**
     * 会员id
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long memberId;

    /**
     * 角色id
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long roleId;

    /**
     * 操作者
     */
    @Column(columnDefinition = "varchar(50)", nullable = false)
    private String operator;

    /**
     * 部门
     */
    @Column(columnDefinition = "varchar(50)", nullable = false)
    private String department;

    /**
     * 职位
     */
    @Column(columnDefinition = "varchar(50)", nullable = false)
    private String jobTitle;

    /**
     * 状态 0或Null-所有 1-待提交审核 2-待一级审核 3-待二级审核 4-待三级审核 5-审核不通过 6-审核通过
     */
    @Column(columnDefinition = "int", nullable = false)
    private Integer status;

    /**
     * 操作
     */
    @Column(columnDefinition = "varchar(50)", nullable = false)
    private String operate;

    /**
     * 操作时间
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long operateTime;

    /**
     * 审核意见
     */
    @Column(columnDefinition = "varchar(120)")
    private String opinion;

    public Integer getStep() {
        return step;
    }

    public void setStep(Integer step) {
        this.step = step;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCreditId() {
        return creditId;
    }

    public void setCreditId(Long creditId) {
        this.creditId = creditId;
    }

    public Long getApplyId() {
        return applyId;
    }

    public void setApplyId(Long applyId) {
        this.applyId = applyId;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getJobTitle() {
        return jobTitle;
    }

    public void setJobTitle(String jobTitle) {
        this.jobTitle = jobTitle;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getOperate() {
        return operate;
    }

    public void setOperate(String operate) {
        this.operate = operate;
    }

    public Long getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(Long operateTime) {
        this.operateTime = operateTime;
    }

    public String getOpinion() {
        return opinion;
    }

    public void setOpinion(String opinion) {
        this.opinion = opinion;
    }
}
