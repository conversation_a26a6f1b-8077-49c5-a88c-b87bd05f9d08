package com.ssy.lingxi.pay.controller.feign;

import com.ssy.lingxi.common.model.req.api.member.ActualControllerSyncReq;
import com.ssy.lingxi.common.model.req.api.pay.MaterialStockDataSyncReq;
import com.ssy.lingxi.common.model.req.api.pay.MemberAssetAccountLineOfCreditSyncReq;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.pay.api.feign.IAssetAccountFeign;
import com.ssy.lingxi.pay.api.model.req.CheckAssetAccountReq;
import com.ssy.lingxi.pay.api.model.req.FrozenCreditReq;
import com.ssy.lingxi.pay.api.model.req.MemberIdsReq;
import com.ssy.lingxi.pay.api.model.req.assetAccount.*;
import com.ssy.lingxi.pay.api.model.resp.assetAccount.AccountPayChannelResultResp;
import com.ssy.lingxi.pay.api.model.resp.assetAccount.EditLineOfCreditReq;
import com.ssy.lingxi.pay.api.model.resp.assetAccount.MemberAssetAccountResp;
import com.ssy.lingxi.pay.api.model.resp.assetAccount.MemberLineOfCreditResp;
import com.ssy.lingxi.pay.entity.do_.assetAccount.MemberAssetAccountDO;
import com.ssy.lingxi.pay.service.ICreditService;
import com.ssy.lingxi.pay.service.assetAccount.IMemberAssetAccountService;
import com.ssy.lingxi.pay.service.eAccount.IEAccountService;
import org.modelmapper.TypeToken;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Set;

/**
 * 同步资金账户内部接口类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/28
 * @ignore 不需要提交到Yapi
 */
@RestController
public class AssetAccountFeignController extends BaseController implements IAssetAccountFeign {

    @Resource
    private IMemberAssetAccountService memberAssetAccountService;

    @Resource
    private IEAccountService eAccountService;

    @Resource
    private ICreditService creditService;

    /**
     * 新增会员资金账户
     * @param memberAssetAccountRequestList 会员资金账户实体
     */
    @Override
    public WrapperResp<String> saveMemberAssetAccount(@RequestBody List<MemberAssetAccountAddReq> memberAssetAccountRequestList) {
        List<MemberAssetAccountDO> memberAssetAccountDOList = this.modelMapper.map(memberAssetAccountRequestList, new TypeToken<List<MemberAssetAccountDO>>() {
        }.getType());
        return WrapperUtil.success(memberAssetAccountService.saveOrUpdateMemberAssetAccount(memberAssetAccountDOList));
    }

    /**
     * 修改会员资金账户状态
     * @param memberAssetAccountUpdateReq 会员资金账户实体
     */
    @Override
    public WrapperResp<Boolean> updateMemberAssetAccount(@RequestBody MemberAssetAccountUpdateReq memberAssetAccountUpdateReq) {
        Long memberId = memberAssetAccountUpdateReq.getMemberId();
        Long memberRoleId = memberAssetAccountUpdateReq.getMemberRoleId();
        Integer memberStatus = memberAssetAccountUpdateReq.getMemberStatus();
        Long parentMemberId = memberAssetAccountUpdateReq.getParentMemberId();
        Long parentMemberRoleId = memberAssetAccountUpdateReq.getParentMemberRoleId();

        // 冻结/解冻授信
        FrozenCreditReq frozenCreditReq = new FrozenCreditReq();
        frozenCreditReq.setMemberId(memberId);
        frozenCreditReq.setRoleId(memberRoleId);
        frozenCreditReq.setParentMemberId(parentMemberId);
        frozenCreditReq.setParentMemberRoleId(parentMemberRoleId);
        if (memberStatus == 1) {
            frozenCreditReq.setStatus(CommonBooleanEnum.NO.getCode());
        } else if (memberStatus == 2) {
            frozenCreditReq.setStatus(CommonBooleanEnum.YES.getCode());
        }
        creditService.frozenCredit(frozenCreditReq);

        //冻结/解冻e账户的会员状态
        eAccountService.updateMemberAssetAccount(memberId, memberRoleId, memberStatus);

        return WrapperUtil.success(memberAssetAccountService.updateMemberAssetAccount(memberAssetAccountUpdateReq));
    }

    /**
     * 余额支付
     * @param balancePayReq 参数
     */
    @Override
    public WrapperResp<String> balancePay(@RequestBody BalancePayReq balancePayReq) {
        return WrapperUtil.success(memberAssetAccountService.balancePay(balancePayReq));
    }

    /**
     * 余额返现
     * @param balanceCashbackReq 参数
     */
    @Override
    public WrapperResp<String> balanceCashback(@RequestBody BalanceCashbackReq balanceCashbackReq) {
        return WrapperUtil.success(memberAssetAccountService.balanceCashback(balanceCashbackReq));
    }

    /**
     * 余额退款
     * @param balanceRefundReq 参数
     */
    @Override
    public WrapperResp<Boolean> balanceRefund(@RequestBody BalanceRefundReq balanceRefundReq) {
        return WrapperUtil.success(memberAssetAccountService.balanceRefund(balanceRefundReq));
    }

    /**
     * 校验当前会员资金账号状态
     * true:存在资金账户, 并且账户余额不为0; false:无资金账户,或者账户余额为0
     * @param request 参数
     */
    @Override
    public WrapperResp<Boolean> checkAssetAccount(@Valid @RequestBody CheckAssetAccountReq request) {
        return WrapperUtil.success(memberAssetAccountService.checkAssetAccount(request));
    }

    /**
     * 同步会员资金账户 - 授信信息
     * @param memberAssetAccountSyncReq
     * @return
     */
    @Override
    public WrapperResp<Boolean> memberAssetAccountLineOfCreditSync(@RequestBody @Valid MemberAssetAccountLineOfCreditSyncReq memberAssetAccountSyncReq) {
        memberAssetAccountService.memberAssetAccountLineOfCreditSync(memberAssetAccountSyncReq);
        return WrapperUtil.success(true);
    }

    /**
     * 同步会员资金账户 - 物料信息
     * @param materialStockDataSyncReq
     * @return
     */
    @Override
    public WrapperResp<Boolean> memberAssetAccountMaterialStockSync(@RequestBody @Valid MaterialStockDataSyncReq materialStockDataSyncReq) {
        memberAssetAccountService.memberAssetAccountMaterialStockSync(materialStockDataSyncReq);
        return WrapperUtil.success(true);
    }

    /**
     * 通过会员id查询账户信息详情
     * @param memberIds
     * @return
     */
    @Override
    public WrapperResp<List<MemberLineOfCreditResp>> findByMemberIds(MemberIdsReq memberIds) {
        return WrapperUtil.success(memberAssetAccountService.findByMemberIds(memberIds));
    }


    @Override
    public WrapperResp<Boolean> lineOfCreditSync(ActualControllerSyncReq req) {
        memberAssetAccountService.lineOfCreditSync(req);
        return WrapperUtil.success(true);
    }

    @Override
    public WrapperResp<Boolean> editLineOfCredit(List<EditLineOfCreditReq> editLineOfCreditReqs) {
        memberAssetAccountService.editLineOfCredit(editLineOfCreditReqs);
        return WrapperUtil.success(true);
    }

    /**
     * 冻结钱包资金
     * @param frozenAccountBalanceReq
     * @return
     */
    @Transactional
    @Override
    public WrapperResp<AccountPayChannelResultResp> frozenAccountBalance(@RequestBody @Valid FrozenAccountBalanceReq frozenAccountBalanceReq) {
        return WrapperUtil.success(memberAssetAccountService.frozenAccountBalance(frozenAccountBalanceReq));
    }

    @Override
    public WrapperResp<Boolean> unFrozenAccountBalance(@RequestBody @Valid UnFrozenAccountBalanceReq unFrozenAccountBalanceReq) {
        return WrapperUtil.success(memberAssetAccountService.unFrozenAccountBalance(unFrozenAccountBalanceReq));
    }
    @Override
    public WrapperResp<Boolean> unLockAccountBalance(@RequestBody @Valid UnLockAccountBalanceReq unFrozenAccountBalanceReq){
        return WrapperUtil.success(memberAssetAccountService.unLockAccountBalance(unFrozenAccountBalanceReq));
    }

    @Override
    public WrapperResp<Boolean> payFinishAndUnLock(@RequestBody PayFinishAndUnLockReq payFinishAndUnLockReq) {
        return WrapperUtil.success(memberAssetAccountService.payFinishAndUnLock(payFinishAndUnLockReq));
    }

    @Override
    public WrapperResp<MemberAssetAccountResp> getEosAccountBalance(@RequestParam("memberId") Long memberId) {
        return WrapperUtil.success(memberAssetAccountService.getEosAccountBalance(memberId));
    }

    /**
     * 获取Eos账号余额信息和订单冻结的金额
     * 将orderPaymentId订单冻结的余额/存料重新计算进可用余额/存料中
     * @param eosAccountBalanceReq
     * @return
     */
    @Override
    public WrapperResp<MemberAssetAccountResp> getEosAccountBalanceAndFrozenAccountBalance(@RequestBody @Valid GetEosAccountBalanceAndFrozenAccountBalanceReq eosAccountBalanceReq) {
        return WrapperUtil.success(memberAssetAccountService.getEosAccountBalanceAndFrozenAccountBalance(eosAccountBalanceReq));
    }


}
