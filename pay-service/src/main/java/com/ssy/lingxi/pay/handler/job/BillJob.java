package com.ssy.lingxi.pay.handler.job;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.ssy.lingxi.component.xxlJob.annotation.XxlRegister;
import com.ssy.lingxi.pay.api.model.req.CreditBillInitReq;
import com.ssy.lingxi.pay.service.ICreditBillService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 账单定时任务
 * <AUTHOR>
 * @since 2020/11/25
 * @version 2.0.0
 */
@Slf4j
@Component
public class BillJob {

    @Resource
    private ICreditBillService billService;

    /**
     * 授信逾期账单任务(每天1:30执行)
     */
    @XxlRegister(cron = "0 30 01 ? * *", jobDesc = "授信逾期账单任务")
    @XxlJob("BillJobHandler")
    public void billJobHandler() {
        try {
            log.info("开始处理授信逾期账单");
            // 初始化逾期账单
            CreditBillInitReq initVO = new CreditBillInitReq();
            // 逾期时间为前一天
            initVO.setInitDate(DateUtil.formatDateTime(DateUtil.offsetDay(DateUtil.date(), -1)));
            billService.initOverdue(initVO);
        } catch (Exception e) {
            log.error("初始化授信逾期账单定时任务失败：{}", e.getMessage());
        }
        try {
            log.info("开始处理授信账单");
            // 初始化新账单
            CreditBillInitReq initVO = new CreditBillInitReq();
            initVO.setInitDate(DateUtil.format(DateUtil.date(), DatePattern.NORM_DATE_PATTERN));
            billService.init(initVO);
        } catch (Exception e) {
            log.error("初始化授信账单定时任务失败：{}", e.getMessage());
        }
        try {
            log.info("开始授信已出账单通知任务");
            // 已出账单通知
            String time = DateUtil.format(DateUtil.date(), DatePattern.NORM_DATE_PATTERN);
            billService.billNotice(time);
        } catch (Exception e) {
            log.error("授信已出账单通知任务失败：{}", e.getMessage());
        }
        try {
            log.info("开始授信逾期账单通知任务");
            // 逾期一天后发通知
            String time = DateUtil.formatDateTime(DateUtil.offsetDay(DateUtil.date(), -1));
            billService.overdueNotice(time);
        } catch (Exception e) {
            log.error("授信逾期账单通知任务失败：{}", e.getMessage());
        }
        try {
            log.info("开始授信账单还款通知任务");
            // 账单前三天发通知
            String time = DateUtil.formatDateTime(DateUtil.offsetDay(DateUtil.date(), 3));
            billService.repayNotice(time);
        } catch (Exception e) {
            log.error("授信账单还款通知任务失败：{}", e.getMessage());
        }
    }
}
