package com.ssy.lingxi.pay.model.resp.allInPay.memberInfo;

import lombok.Data;
import lombok.experimental.FieldNameConstants;

/**
 * 企业会员信息
 *
 * <AUTHOR>
 */
@Data
@FieldNameConstants
public class EnterpriseMemberInfoResp {

    /**
     * 企业名称
     */
    private String companyName;
    /**
     * 企业地址
     */
    private String companyAddress;
    /**
     * 企业性质
     * 1 - 企业
     * 2 - 个体户
     * 3 - 事业单位
     * 无值，则不返回
     */
    private Integer comproperty;
    /**
     * 认证类型（三证或一证）
     */
    private Integer authType;
    /**
     * 营业执照号（三证）
     */
    private String businessLicense;
    /**
     * 组织机构代码（三证）
     */
    private String organizationCode;
    /**
     * 统一社会信用（一证）
     */
    private String uniCredit;
    /**
     * 税务登记证（三证）
     */
    private String taxRegister;
    /**
     * 统一社会信用/营业执照号到期时间
     * 格式：yyyy - MM - dd
     */
    private String expLicense;
    /**
     * 联系电话
     */
    private String telephone;
    /**
     * 手机号码
     */
    private String phone;
    /**
     * 法人姓名
     */
    private String legalName;
    /**
     * 法人证件类型
     */
    private Integer identityType;
    /**
     * 法人证件号码
     * AES 加密，详细
     */
    private String legalIds;
    /**
     * 证件有效开始日期
     * 格式：9999 - 12 - 31
     */
    private String identityBeginDate;
    /**
     * 证件有效截止日期
     * 格式：9999 - 12 - 31
     */
    private String identityEndDate;
    /**
     * 法人手机号码
     */
    private String legalPhone;
    /**
     * 账户类型
     * 0 - 对私
     * 1 - 对公
     * 无值，则不返回
     */
    private Integer acctType;
    /**
     * 企业账号
     * 对私银行账户四要素认证失败，则不返回
     * 对公账户，必返
     * AES 加密，详细
     */
    private String accountNo;
    /**
     * 对公户名
     * 仅账户类型对公时有值
     */
    private String accountName;
    /**
     * 开户银行名称
     * 对公账户，必返
     */
    private String parentBankName;
    /**
     * 开户行地区代码
     * 根据中国地区代码表 详情
     */
    private String bankCityNo;
    /**
     * 开户行支行名称
     */
    private String bankName;
    /**
     * 支付行号，12 位数字
     */
    private String unionBank;
    /**
     * 开户行所在省
     * 开户行所在市必须同时上送
     * 根据中国省市表的“省份”内容填写。详情
     */
    private String province;
    /**
     * 开户行所在市
     * 开户行所在省必须同时上送
     * 根据中国省市表的“城市”内容填写。详情
     */
    private String city;
    /**
     * 是否已签电子协议
     */
    private Boolean isSignContract;
    /**
     * 审核状态
     * 详细
     */
    private Integer status;
    /**
     * 审核时间
     * yyyy - MM - dd HH:mm:ss
     */
    private String checkTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 审核失败原因
     */
    private String failReason;
    /**
     * 开户机构类型
     * 0 - 通联
     */
    private Integer acctOrgType;
    /**
     * 会员开通的通联子账号
     */
    private String subAcctNo;
    /**
     * 云商通用户 id
     */
    private String userId;
    /**
     * 是否绑定手机
     */
    private Boolean isPhoneChecked;
    /**
     * 账户提现协议签订时间
     */
    private String signAcctProtocolTime;
    /**
     * 对私银行账户认证结果
     * 2：认证成功。
     * 3：认证失败。
     * 注：个体工商户的对私银行账户四要素认证结果
     */
    private Integer accountSetResult;
    /**
     * OCR 识别与企业工商认证信息是否一致
     * 0 - 否
     * 1 - 是
     * 若营业执照未进行识别该字段不返
     */
    private Integer ocrRegnumComparisonResult;
    /**
     * OCR 识别与企业法人实名信息是否一致
     * 0 - 否
     * 1 - 是
     * 若法人身份证未进行识别该字段不返
     */
    private Integer ocrIdcardComparisonResult;
    /**
     * 比对结果说明
     * 存在多种结果信息一起返回，使用“;”进行拼接
     */
    private String resultInfo;
    /**
     * 法人账户提现协议签订时间
     * 仅当企业会员法人签订账户提现协议时有值
     */
    private String legalSignAcctProtocolTime;
    /**
     * 法人账户提现协议编号
     * 仅当企业会员法人签订账户提现协议时有值
     */
    private String legalAcctProtocolNo;

}