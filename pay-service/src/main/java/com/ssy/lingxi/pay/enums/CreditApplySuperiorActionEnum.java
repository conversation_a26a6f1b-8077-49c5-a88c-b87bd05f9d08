package com.ssy.lingxi.pay.enums;

import com.ssy.lingxi.component.base.language.LanguageHolder;

import java.util.Arrays;

/**
 * 授信申请上级操作描述
 * <AUTHOR>
 * @since 2021/5/20
 * @version 2.0.0
 */
public enum CreditApplySuperiorActionEnum {

    ALL(0, "所有"),
    /**
     * 提交审核授信申请单
     */
    SUBMIT_VERIFY(1, "提交审核授信申请单"),
    /**
     * 审核授信申请单(一级)
     */
    STEP1_VERIFY(2, "审核授信申请单(一级)"),
    /**
     * 审核授信申请单(二级)
     */
    STEP2_VERIFY(3, "审核授信申请单(二级)"),
    /**
     * 审核授信申请单(三级)
     */
    STEP3_VERIFY(4, "审核授信申请单(三级)"),
    /**
     * 确认授信申请单
     */
    CONFIRM_VERIFY(5, "确认授信申请单");

    private final Integer code;
    private final String message;

    CreditApplySuperiorActionEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return LanguageHolder.getTranslation(this.getClass(), this.message, this.code);
    }


    public static String getItemMessage(Integer code) {
        return Arrays.stream(CreditApplySuperiorActionEnum.values()).filter(e -> e.getCode().equals(code)).findFirst().orElse(ALL).getMessage();
    }
}
