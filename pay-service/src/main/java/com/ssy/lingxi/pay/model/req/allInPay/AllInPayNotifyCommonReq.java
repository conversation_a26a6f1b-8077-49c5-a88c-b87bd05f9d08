package com.ssy.lingxi.pay.model.req.allInPay;

import cn.hutool.core.date.DatePattern;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 通联通知公共参数
 *
 * <AUTHOR>
 */
@Data
public class AllInPayNotifyCommonReq {

    /**
     * 应用Id
     */
    private String appId;

    /**
     * 业务内容
     */
    private String bizContent;

    /**
     * 字符集
     */
    private String charset;

    /**
     * 通知Id
     */
    private String notifyId;

    /**
     * 通知时间
     */
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime notifyTime;

    /**
     * 通知类型
     */
    private String notifyType;

    /**
     * 签名
     */
    private String sign;

    /**
     * 签名类型
     */
    private String signType;

    /**
     * 版本
     */
    private String version;

}
