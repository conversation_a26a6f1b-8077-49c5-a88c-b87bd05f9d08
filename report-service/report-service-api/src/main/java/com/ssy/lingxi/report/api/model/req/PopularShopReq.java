package com.ssy.lingxi.report.api.model.req;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @since 2021/1/7
 * @version 2.0.0
 */
public class PopularShopReq implements Serializable {

    private static final long serialVersionUID = 591064737080849124L;
    /**
     * 查询数量
     */
    @NotNull(message = "查询数量不能为空")
    private Integer count;

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }
}
