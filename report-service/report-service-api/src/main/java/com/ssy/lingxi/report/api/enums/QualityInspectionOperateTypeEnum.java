package com.ssy.lingxi.report.api.enums;

import com.ssy.lingxi.component.base.language.LanguageHolder;

import java.util.Arrays;

/**
 * todo 跳转链接未明确
 * 质量中心-质检单 操作枚举
 * <AUTHOR>
 * @email <EMAIL>
 * @since 2022/6/2 9:54
 */
public enum QualityInspectionOperateTypeEnum {
    TO_BE_COMMIT_SRM(1,"待提交质检单（SRM）","/memberCenter/quality/qualityManage/srm"),
    TO_BE_COMMIT_B2B(2,"待提交质检单（B2B）","/memberCenter/quality/qualityManage/b2b"),
    ;
    /**
     * 编码
     */
    private final Integer code;
    /**
     * 名称
     */
    private final String name;
    /**
     * 链接
     */
    private final String link;


    QualityInspectionOperateTypeEnum(Integer code, String name, String link) {
        this.code = code;
        this.name = name;
        this.link = link;
    }

    public static QualityInspectionOperateTypeEnum getEnum(Integer code) {
        return Arrays.stream(QualityInspectionOperateTypeEnum.values()).filter(e -> e.getCode().equals(code)).findFirst().orElse(null);
    }


    public Integer getCode() {
        return code;
    }

    public String getName() {
        return LanguageHolder.getTranslation(this.getClass(), this.name, this.code);
    }

    public String getLink() {
        return link;
    }
}
