package com.ssy.lingxi.report.api.enums;

import com.ssy.lingxi.component.base.language.LanguageHolder;

import java.util.Arrays;

/**
 * 营销中心-商家优惠券管理
 * <AUTHOR>
 * @email <EMAIL>
 * @since 2022/6/2 10:50
 */
public enum MarketingMerchantCouponOperateTypeEnum {
    TO_BE_COMMIT_AUDIT(1,"待提交审核","/memberCenter/marketingAbility/merchantCoupon/unsubmitted"),
    TO_BE_AUDIT_STEP1(2,"待审核（一级）","/memberCenter/marketingAbility/merchantCoupon/notVerify1"),
    TO_BE_AUDIT_STEP2(3,"待审核（二级）","/memberCenter/marketingAbility/merchantCoupon/notVerify2"),
    TO_BE_COMMIT(4,"待提交","/memberCenter/marketingAbility/merchantCoupon/toConfirm"),
    ;

    /**
     * 编码
     */
    private final Integer code;
    /**
     * 名称
     */
    private final String name;
    /**
     * 链接
     */
    private final String link;


    MarketingMerchantCouponOperateTypeEnum(Integer code, String name, String link) {
        this.code = code;
        this.name = name;
        this.link = link;
    }

    public static MarketingMerchantCouponOperateTypeEnum getEnum(Integer code) {
        return Arrays.stream(MarketingMerchantCouponOperateTypeEnum.values()).filter(e -> e.getCode().equals(code)).findFirst().orElse(null);
    }


    public Integer getCode() {
        return code;
    }

    public String getName() {
        return LanguageHolder.getTranslation(this.getClass(), this.name, this.code);
    }

    public String getLink() {
        return link;
    }
}
