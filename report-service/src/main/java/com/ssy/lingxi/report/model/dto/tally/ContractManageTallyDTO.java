package com.ssy.lingxi.report.model.dto.tally;

import java.io.Serializable;

/**
 * 合同管理计数报表
 * <AUTHOR>
 * @since 2020/5/11
 * @version 2.0.0
 */

public class ContractManageTallyDTO implements Serializable {

    private static final long serialVersionUID = -3987329366139343083L;

    public ContractManageTallyDTO() {
        this.toBeCreateInquiryCount = 0L;
        this.toBeCreateViePriceCount = 0L;
        this.toBeCreateInviteTenderCount = 0L;
        this.toBeCreateRequisitionCount = 0L;
        this.toBeCommitCount = 0L;
        this.toBeCommitValifyCount = 0L;
        this.toBeValifyStep1Count = 0L;
        this.toBeValifyStep2Count = 0L;
        this.toBeSignCount = 0L;
        this.toBeCommitAuditCreateCount = 0L;
        this.toBeAuditCreateStep1Count = 0L;
        this.toBeAuditCreateStep2Count = 0L;
        this.toBeCommitCreateCount = 0L;
    }
    /**
     * 待创建采购询价合同
     */
    private Long toBeCreateInquiryCount;
    /**
     * 待创建采购竞价合同
     */
    private Long toBeCreateViePriceCount;
    /**
     * 待创建采购招标合同
     */
    private Long toBeCreateInviteTenderCount;
    /**
     * 待创建采购请购合同
     */
    private Long toBeCreateRequisitionCount;
    /**
     * 待提交合同
     */
    @Deprecated
    private Long toBeCommitCount;

    /**
     * 待提交审核合同签订
     */
    private Long toBeCommitValifyCount;
    /**
     * 待审核合同签订（一级）
     */
    private Long toBeValifyStep1Count;
    /**
     *待审核合同签订（二级）
     */
    private Long toBeValifyStep2Count;
    /**
     *待签订合同
     */
    private Long toBeSignCount;

    /**
     * 待提交审核合同创建
     */
    private Long toBeCommitAuditCreateCount;
    /**
     * 待审核合同创建（一级）
     */
    private Long toBeAuditCreateStep1Count;
    /**
     *  待审核合同创建（二级）
     */
    private Long toBeAuditCreateStep2Count;
    /**
     *  待提交合同创建
     */
    private Long toBeCommitCreateCount;

    public Long getToBeCreateInquiryCount() {
        return toBeCreateInquiryCount;
    }

    public void setToBeCreateInquiryCount(Long toBeCreateInquiryCount) {
        this.toBeCreateInquiryCount = toBeCreateInquiryCount;
    }

    public Long getToBeCreateViePriceCount() {
        return toBeCreateViePriceCount;
    }

    public void setToBeCreateViePriceCount(Long toBeCreateViePriceCount) {
        this.toBeCreateViePriceCount = toBeCreateViePriceCount;
    }

    public Long getToBeCreateInviteTenderCount() {
        return toBeCreateInviteTenderCount;
    }

    public void setToBeCreateInviteTenderCount(Long toBeCreateInviteTenderCount) {
        this.toBeCreateInviteTenderCount = toBeCreateInviteTenderCount;
    }

    public Long getToBeCreateRequisitionCount() {
        return toBeCreateRequisitionCount;
    }

    public void setToBeCreateRequisitionCount(Long toBeCreateRequisitionCount) {
        this.toBeCreateRequisitionCount = toBeCreateRequisitionCount;
    }

    public Long getToBeCommitCount() {
        return toBeCommitCount;
    }

    public void setToBeCommitCount(Long toBeCommitCount) {
        this.toBeCommitCount = toBeCommitCount;
    }

    public Long getToBeCommitValifyCount() {
        return toBeCommitValifyCount;
    }

    public void setToBeCommitValifyCount(Long toBeCommitValifyCount) {
        this.toBeCommitValifyCount = toBeCommitValifyCount;
    }

    public Long getToBeValifyStep1Count() {
        return toBeValifyStep1Count;
    }

    public void setToBeValifyStep1Count(Long toBeValifyStep1Count) {
        this.toBeValifyStep1Count = toBeValifyStep1Count;
    }

    public Long getToBeValifyStep2Count() {
        return toBeValifyStep2Count;
    }

    public void setToBeValifyStep2Count(Long toBeValifyStep2Count) {
        this.toBeValifyStep2Count = toBeValifyStep2Count;
    }

    public Long getToBeSignCount() {
        return toBeSignCount;
    }

    public void setToBeSignCount(Long toBeSignCount) {
        this.toBeSignCount = toBeSignCount;
    }

    public Long getToBeCommitAuditCreateCount() {
        return toBeCommitAuditCreateCount;
    }

    public void setToBeCommitAuditCreateCount(Long toBeCommitAuditCreateCount) {
        this.toBeCommitAuditCreateCount = toBeCommitAuditCreateCount;
    }

    public Long getToBeAuditCreateStep1Count() {
        return toBeAuditCreateStep1Count;
    }

    public void setToBeAuditCreateStep1Count(Long toBeAuditCreateStep1Count) {
        this.toBeAuditCreateStep1Count = toBeAuditCreateStep1Count;
    }

    public Long getToBeAuditCreateStep2Count() {
        return toBeAuditCreateStep2Count;
    }

    public void setToBeAuditCreateStep2Count(Long toBeAuditCreateStep2Count) {
        this.toBeAuditCreateStep2Count = toBeAuditCreateStep2Count;
    }

    public Long getToBeCommitCreateCount() {
        return toBeCommitCreateCount;
    }

    public void setToBeCommitCreateCount(Long toBeCommitCreateCount) {
        this.toBeCommitCreateCount = toBeCommitCreateCount;
    }
}
