package com.ssy.lingxi.report.model.dto.tally;

import java.io.Serializable;

/**
 * 质量中心-质检单
 * <AUTHOR>
 * @email <EMAIL>
 * @since 2022/6/2 9:42
 */
public class QualityInspectionTallyDTO implements Serializable {
    private static final long serialVersionUID = -6641826152919836098L;

    /**
     * 待提交质检单（SRM）
     */
    private Long toBeCommitSrmCount = 0L;

    /**
     * 待提交质检单（B2B）
     */
    private Long toBeCommitB2bCount = 0L;



    public Long getToBeCommitSrmCount() {
        return toBeCommitSrmCount;
    }

    public void setToBeCommitSrmCount(Long toBeCommitSrmCount) {
        this.toBeCommitSrmCount = toBeCommitSrmCount;
    }

    public Long getToBeCommitB2bCount() {
        return toBeCommitB2bCount;
    }

    public void setToBeCommitB2bCount(Long toBeCommitB2bCount) {
        this.toBeCommitB2bCount = toBeCommitB2bCount;
    }

//    public Long getToBeCommit8DSrmCount() {
//        return toBeCommit8DSrmCount;
//    }
//
//    public void setToBeCommit8DSrmCount(Long toBeCommit8DSrmCount) {
//        this.toBeCommit8DSrmCount = toBeCommit8DSrmCount;
//    }
//
//    public Long getToBeCommit8DB2bCount() {
//        return toBeCommit8DB2bCount;
//    }
//
//    public void setToBeCommit8DB2bCount(Long toBeCommit8DB2bCount) {
//        this.toBeCommit8DB2bCount = toBeCommit8DB2bCount;
//    }
}
