package com.ssy.lingxi.report.handler.converter;

import com.ssy.lingxi.common.util.JsonUtil;
import com.ssy.lingxi.report.model.dto.tally.MemberTallyDTO;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.List;

/**
 * 将自定义的Entity，与授信支付记录表中的Jsonb字段进行转换
 * <AUTHOR>
 * @since 2020/8/24
 * @version 2.0.0
 */
@Converter(autoApply = true)
public class JpaJsonToMemberTallyBOConverter implements AttributeConverter<List<MemberTallyDTO>, String> {
    @Override
    public String convertToDatabaseColumn(List<MemberTallyDTO> meta) {
        return JsonUtil.toJson(meta);
    }

    @Override
    public List<MemberTallyDTO> convertToEntityAttribute(String dbData) {
        return JsonUtil.toList(dbData, MemberTallyDTO.class);
    }
}
