package com.ssy.lingxi.report.repository.report;

import com.ssy.lingxi.report.entity.report.MemberMonthReportDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 会员月报表
 * <AUTHOR>
 * @since 2020/11/9
 * @version 2.0.0
 */
public interface MemberMonthReportRepository extends JpaRepository<MemberMonthReportDO, Long>, JpaSpecificationExecutor<MemberMonthReportDO> {

    /**
     * 删除指定范围会员月报表
     * <AUTHOR>
     * @since 2020/11/9
     * @param startTime:
     * @param endTime:
     **/
    @Transactional
    void deleteByDateTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询指定范围会员月注册数据
     * <AUTHOR>
     * @since 2020/11/10
     * @param startTime:
     * @param endTime:
     * @return java.util.List<com.ssy.lingxi.report.entity.report.MemberDayReportDO>
     **/
    List<MemberMonthReportDO> getAllByDateTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取注册总数
     * <AUTHOR>
     * @since 2020/11/10
     * @return java.lang.Long
     **/
    @Query(value = "select sum(m.count) from MemberMonthReportDO as m")
    Long getAllRegisterCount();
}
