package com.ssy.lingxi.report.model.dto.tally;

import java.io.Serializable;

/**
 * 订单中心 - 送货计划
 * <AUTHOR>
 * @email <EMAIL>
 * @since 2022/6/1 20:01
 */
public class TradeDeliveryPlanTallyDTO implements Serializable {
    private static final long serialVersionUID = -4206897538720659411L;

    /**
     * 待提交送货计划（SRM）
     */
    private Long toBeCommitSrmCount = 0L;

    /**
     * 待提交送货计划（B2B）
     */
    private Long toBeCommitB2bCount = 0L;

    public Long getToBeCommitSrmCount() {
        return toBeCommitSrmCount;
    }

    public void setToBeCommitSrmCount(Long toBeCommitSrmCount) {
        this.toBeCommitSrmCount = toBeCommitSrmCount;
    }

    public Long getToBeCommitB2bCount() {
        return toBeCommitB2bCount;
    }

    public void setToBeCommitB2bCount(Long toBeCommitB2bCount) {
        this.toBeCommitB2bCount = toBeCommitB2bCount;
    }
}
