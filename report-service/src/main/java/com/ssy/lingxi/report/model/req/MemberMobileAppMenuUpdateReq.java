package com.ssy.lingxi.report.model.req;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.constraints.PositiveOrZero;
import java.io.Serializable;

/**
 * 会员手机端应用菜单更新VO
 * <AUTHOR>
 * @since 2021/3/3
 * @version 2.0.0
 */
public class MemberMobileAppMenuUpdateReq implements Serializable {
    private static final long serialVersionUID = -3097190962680385414L;

    /**
     * 应用code
     */
    @NotNull(message = "应用code需大于0")
    @Positive(message = "应用code需大于0")
    private Integer code;

    /**
     * 排序：越小越靠前
     */
    @NotNull(message = "排序需大于0")
    @Positive(message = "排序需大于0")
    private Integer sort;

    /**
     * 是否打开:0-否，1-是
     */
    @NotNull(message = "是否打开不能为空")
    @PositiveOrZero(message = "是否打开不能为空")
    private Integer isOpen;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getIsOpen() {
        return isOpen;
    }

    public void setIsOpen(Integer isOpen) {
        this.isOpen = isOpen;
    }
}
