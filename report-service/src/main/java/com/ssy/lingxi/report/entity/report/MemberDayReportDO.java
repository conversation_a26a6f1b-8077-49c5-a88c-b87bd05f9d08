package com.ssy.lingxi.report.entity.report;

import com.ssy.lingxi.common.constant.TableNameConstant;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 会员日报表
 * <AUTHOR>
 * @since 2020/11/9
 * @version 2.0.0
 */
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_REPORT_SERVICE + "member_day_report", indexes = {@Index(name = TableNameConstant.TABLE_PRE_REPORT_SERVICE + "member_day_report_date_time_idx", columnList = "dateTime")})
public class MemberDayReportDO implements Serializable {
    private static final long serialVersionUID = 8020713051752080826L;

    /**
     * id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 日期时间
     */
    @Column(columnDefinition = "timestamp")
    private LocalDateTime dateTime;

    /**
     * 角色名称
     */
    @Column(columnDefinition = "varchar(50)", nullable = false)
    private String roleName;

    /**
     * 数量
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long count;

    /**
     * key：报表时间+角色名称
     */
    @Transient
    private String key;

    public String getKey() {
        return this.getDateTime() + this.getRoleName();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDateTime getDateTime() {
        return dateTime;
    }

    public void setDateTime(LocalDateTime dateTime) {
        this.dateTime = dateTime;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public Long getCount() {
        return count;
    }

    public void setCount(Long count) {
        this.count = count;
    }
}
