package com.ssy.lingxi.report.entity.report;

import com.ssy.lingxi.common.constant.TableNameConstant;
import com.ssy.lingxi.report.handler.converter.JpaJsonToMemberMobileMenuBOConverter;
import com.ssy.lingxi.report.model.dto.MemberMobileMenuDTO;

import javax.persistence.*;
import java.io.Serializable;
import java.util.List;

/**
 * 会员手机端菜单
 * <AUTHOR>
 * @since 2021/3/3
 * @version 2.0.0
 */
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_REPORT_SERVICE + "member_mobile_menu",
        indexes = {@Index(name = TableNameConstant.TABLE_PRE_REPORT_SERVICE + "member_mobile_menu_idx", columnList = "memberId,roleId")})
public class MemberMobileMenuDO implements Serializable {

    private static final long serialVersionUID = -6609216246845200962L;
    /**
     * id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 商户id
     */
    @Column(columnDefinition = "int8")
    private Long memberId;

    /**
     * 商户角色id
     */
    @Column(columnDefinition = "int8")
    private Long roleId;

    /**
     * 计数报表
     */
    @Convert(converter = JpaJsonToMemberMobileMenuBOConverter.class)
    @Column(columnDefinition = "jsonb")
    private List<MemberMobileMenuDTO> menuList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public List<MemberMobileMenuDTO> getMenuList() {
        return menuList;
    }

    public void setMenuList(List<MemberMobileMenuDTO> menuList) {
        this.menuList = menuList;
    }
}
