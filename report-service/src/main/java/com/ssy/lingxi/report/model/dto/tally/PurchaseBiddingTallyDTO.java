package com.ssy.lingxi.report.model.dto.tally;

import java.io.Serializable;

/**
 * 竞价计数统计
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/5/14
 */
public class PurchaseBiddingTallyDTO implements Serializable {

    private static final long serialVersionUID = 725059851991267781L;

    /**
     * 待提交审核采购竞价单
     */
    private Long toBeCommitValifyCount = 0L;

    /**
     * 待审核采购竞价单（一级）
     */
    private Long toBeValifyStep1Count = 0L;

    /**
     * 待审核采购竞价单（二级）
     */
    private Long toBeValifyStep2Count = 0L;

    /**
     * 待提交采购竞价单
     */
    private Long toBeCommitCount = 0L;

    /**
     * 待审核竞价报名
     */
    private Long toBeValifyCount = 0L;

    /**
     * 待竞价
     */
    private Long toBeBiddingCount = 0L;
    /**
     * 待提交审核竞价结果
     */
    private Long toBeCommitValifyResultCount = 0L;

    /**
     * 待审核竞价结果（一级）
     */
    private Long toBeValifyResultStep1Count = 0L;

    /**
     * 待审核竞价结果（二级）
     */
    private Long toBeValifyResultStep2Count = 0L;

    /**
     * 待确认竞价结果
     */
    private Long toBeConfirmResultCount = 0L;

    public Long getToBeCommitValifyCount() {
        return toBeCommitValifyCount;
    }

    public void setToBeCommitValifyCount(Long toBeCommitValifyCount) {
        this.toBeCommitValifyCount = toBeCommitValifyCount;
    }

    public Long getToBeValifyStep1Count() {
        return toBeValifyStep1Count;
    }

    public void setToBeValifyStep1Count(Long toBeValifyStep1Count) {
        this.toBeValifyStep1Count = toBeValifyStep1Count;
    }

    public Long getToBeValifyStep2Count() {
        return toBeValifyStep2Count;
    }

    public void setToBeValifyStep2Count(Long toBeValifyStep2Count) {
        this.toBeValifyStep2Count = toBeValifyStep2Count;
    }

    public Long getToBeCommitCount() {
        return toBeCommitCount;
    }

    public void setToBeCommitCount(Long toBeCommitCount) {
        this.toBeCommitCount = toBeCommitCount;
    }

    public Long getToBeValifyCount() {
        return toBeValifyCount;
    }

    public void setToBeValifyCount(Long toBeValifyCount) {
        this.toBeValifyCount = toBeValifyCount;
    }

    public Long getToBeBiddingCount() {
        return toBeBiddingCount;
    }

    public void setToBeBiddingCount(Long toBeBiddingCount) {
        this.toBeBiddingCount = toBeBiddingCount;
    }

    public Long getToBeCommitValifyResultCount() {
        return toBeCommitValifyResultCount;
    }

    public void setToBeCommitValifyResultCount(Long toBeCommitValifyResultCount) {
        this.toBeCommitValifyResultCount = toBeCommitValifyResultCount;
    }

    public Long getToBeValifyResultStep1Count() {
        return toBeValifyResultStep1Count;
    }

    public void setToBeValifyResultStep1Count(Long toBeValifyResultStep1Count) {
        this.toBeValifyResultStep1Count = toBeValifyResultStep1Count;
    }

    public Long getToBeValifyResultStep2Count() {
        return toBeValifyResultStep2Count;
    }

    public void setToBeValifyResultStep2Count(Long toBeValifyResultStep2Count) {
        this.toBeValifyResultStep2Count = toBeValifyResultStep2Count;
    }

    public Long getToBeConfirmResultCount() {
        return toBeConfirmResultCount;
    }

    public void setToBeConfirmResultCount(Long toBeConfirmResultCount) {
        this.toBeConfirmResultCount = toBeConfirmResultCount;
    }
}
