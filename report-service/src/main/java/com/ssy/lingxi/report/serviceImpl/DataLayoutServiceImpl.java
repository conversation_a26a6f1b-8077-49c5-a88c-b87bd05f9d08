package com.ssy.lingxi.report.serviceImpl;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.report.entity.report.DataLayoutDO;
import com.ssy.lingxi.report.model.req.UpdateDataLayoutReq;
import com.ssy.lingxi.report.model.resp.DataLayoutResp;
import com.ssy.lingxi.report.repository.report.DataLayoutRepository;
import com.ssy.lingxi.report.service.IDataLayoutService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 数据布局接口实现
 * <AUTHOR>
 * @since 2020/11/19
 * @version 2.0.0
 */
@Service
public class DataLayoutServiceImpl implements IDataLayoutService {
    @Resource
    private DataLayoutRepository repository;

    /**
     * 获取数据布局
     * @param sysUser 登录用户
     * @return 数据布局
     */
    @Override
    public List<DataLayoutResp> getDetail(UserLoginCacheDTO sysUser) {
        List<DataLayoutResp> tempList =null;
        List<DataLayoutDO> doList = repository.findByMemberIdAndRoleIdAndUserId(sysUser.getMemberId(), sysUser.getMemberRoleId(),sysUser.getUserId());
        if (doList != null && !doList.isEmpty()) {
            tempList=doList.stream().map(o ->{
                DataLayoutResp entity = new DataLayoutResp();
                BeanUtils.copyProperties(o,entity);
                return entity;
            }).sorted(Comparator.comparing(DataLayoutResp::getSort)).collect(Collectors.toList());
        }
        return tempList;
    }

    /**
     * 更新数据布局
     * @param sysUser 登录用户
     * @param layoutVOS 布局数据
     */
    @Transactional
    @Override
    public void update(UserLoginCacheDTO sysUser, List<UpdateDataLayoutReq> layoutVOS) {
        //清空所有
        if(CollectionUtils.isEmpty(layoutVOS)){
            // 删除旧数据
            repository.deleteByMemberIdAndRoleId(sysUser.getMemberId(), sysUser.getMemberRoleId());
            return ;
        }
        //删除取消显示的选项
        List<DataLayoutDO> oldDataLayoutList = repository.findByMemberIdAndRoleIdAndUserId(sysUser.getMemberId(), sysUser.getMemberRoleId(), sysUser.getUserId());
        if(!CollectionUtils.isEmpty(oldDataLayoutList)){
            //修改项id
            List<UpdateDataLayoutReq> reqIdsStream = layoutVOS.stream().filter(o -> o.getId() != null).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(reqIdsStream)) {
                Map<Long, UpdateDataLayoutReq> reqIdsMap = reqIdsStream.stream().collect(Collectors.toMap(UpdateDataLayoutReq::getId, e -> e));
                //取消显示的选项id
                List<Long> delIds = oldDataLayoutList.stream().map(DataLayoutDO::getId).filter(id -> reqIdsMap.get(id) == null).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(delIds)){
                    repository.deleteByIdInAndUserId(delIds,sysUser.getUserId());
                }
            }
        }
        List<DataLayoutDO> resultList = layoutVOS.stream().map(r -> {
            DataLayoutDO entity = new DataLayoutDO();
            entity.setId(r.getId());
            entity.setCode(r.getCode());
            entity.setName(r.getName());
            entity.setSort(r.getSort());
            entity.setIsShow(r.getIsShow());
            entity.setMemberId(sysUser.getMemberId());
            entity.setRoleId(sysUser.getMemberRoleId());
            entity.setUserId(sysUser.getUserId());
            return entity;
        }).collect(Collectors.toList());
        repository.saveAll(resultList);
    }
}
