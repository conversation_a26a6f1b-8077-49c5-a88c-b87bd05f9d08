package com.ssy.lingxi.marketing.model.vo.coupon.request;

import com.ssy.lingxi.component.base.enums.marketing.BelongTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 装修-优惠券列表
 * <AUTHOR>
 */
@Data
public class AdornCouponListReq {

    /**
     * 优惠券归属类型
     *
     * @see BelongTypeEnum
     */
    @NotNull
    private Integer belongType;
    /**
     * 优惠券ID
     */
    @NotNull
    private Long couponId;

}
