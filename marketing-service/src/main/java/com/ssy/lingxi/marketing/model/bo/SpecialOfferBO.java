package com.ssy.lingxi.marketing.model.bo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 特价促销 - BO
 * <AUTHOR>
 * @since 2021/06/17
 * @version 2.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SpecialOfferBO extends ActivityDefinedBO{

    private static final long serialVersionUID = 912580675119215314L;
    /**
     * 允许叠加活动类型：4-满量促销，5-满额促销，6-赠送促销，13-换购
     * */
    private List<Integer> allowActivity;

    /**
     * 是否允许叠加优惠劵
     * */
    @NotNull(message = "是否允许叠加优惠劵不能为空")
    private Boolean allowCoupon;

    /**
     * 超限规则：1-原价购买 2-不可购买
     * */
    private Integer exceedRule;

    /**
     * 活动描述
     * */
    @Length(max = 500,message = "活动描述最长500个汉字")
    private String describe;

}
