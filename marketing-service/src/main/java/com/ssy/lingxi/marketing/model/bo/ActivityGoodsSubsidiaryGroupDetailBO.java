package com.ssy.lingxi.marketing.model.bo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *  商品-附属(赠品/换购/套餐商品)信息-组明细
 * <AUTHOR> yzc
 * @since 2021/6/21
 * @version 2.0.0
 */
@Getter
@Setter
public class ActivityGoodsSubsidiaryGroupDetailBO implements Serializable {
    private static final long serialVersionUID = 8371758933062303299L;

    /**
     * 活动附属品id
     * */
    private Long id;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 品类
     */
    private String category;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 单位
     */
    private String unit;

    /**
     * 商品价格
     * */
    private BigDecimal price;

    /**
     * 换购价格
     * */
    private BigDecimal swapPrice;

    /**
     * 允许换购数量/赠送数量/搭配数量
     */
    private BigDecimal num;

    /**
     * 赠品主图
     */
    private String productImgUrl;
}
