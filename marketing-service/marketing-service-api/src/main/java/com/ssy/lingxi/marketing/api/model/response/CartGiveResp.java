package com.ssy.lingxi.marketing.api.model.response;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *  赠品活动
 * <AUTHOR>
 * @since 2021/10/19
 * @version 2.0.0
 */
@Getter
@Setter
public class CartGiveResp implements Serializable {
    private static final long serialVersionUID = -1200471378856243233L;
    /**
     * 赠品表id（ActivityGoodsCouponDO/ActivityGoodsSubsidiaryDO）
     * */
    private Long giftId;
    /**
     * 赠品类型：1-赠商品 2-赠优惠卷
     * */
    private Integer giftType;
    /**
     * 分组编号/优惠阶梯
     */
    private Integer groupNo;
    /**
     * 优惠门槛数量或金额
     */
    private BigDecimal limitValue;

    /**
     * 赠品名称（优惠券或商品名称）
     */
    private String name;

    /**
     * 赠品id(skuid或优惠券id)
     */
    private Long id;
    /**
     * 商品id
     */
    private Long productId;

    /**
     * 赠送数量/搭配数量
     */
    private BigDecimal num;

    /**
     * 品类
     */
    private String category;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 单位
     */
    private String unit;
    /**
     * 赠品主图
     */
    private String productImgUrl;
}
