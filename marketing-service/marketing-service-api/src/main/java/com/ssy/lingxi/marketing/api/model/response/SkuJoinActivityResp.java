package com.ssy.lingxi.marketing.api.model.response;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 活动商品价格和活动标签返回类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/9/1
 */
@Getter
@Setter
@ToString
public class SkuJoinActivityResp implements Serializable {
    private static final long serialVersionUID = 16398667045693966L;
    /**
     * 所属类型 1-平台 2-商家
     */
    private Integer belongType;
    /**
     * 符合条件的活动
     */
    private Long activityId;
    /**
     * 单价优惠金额
     */
    private BigDecimal salePrice;
    /**
     * sku总优惠金额
     */
    private BigDecimal saleTotalAmount;
    /**
     * 满额总优惠金额（非（满额/组合）为null）
     */
    private BigDecimal fullMoneyAmount;

    /**
     * 活动名称
     * */
    private String activityName;

    /**
     * 活动开始时间
     * */
    private Long startTime;

    /**
     * 活动结束时间
     * */
    private Long endTime;

    /**
     * 活动类型: 1-特价促销 2-直降促销 3-折扣促销 4-满量促销 5-满额促销
     *         6-赠送促销 7-多件促销 8-组合促销 9-拼团 10-抽奖
     *         11-砍价 12-秒杀 13-换购 14-预售 15-套餐 16-试用
     *         ActivityTypeEnum.class
     * */
    private Integer activityType;

    /**
     * 是否允许适用优惠券 0-否 1-是
     * 注意: 与activityDefined内定义的类型不一样
     */
    private Integer allowCoupon;
}
