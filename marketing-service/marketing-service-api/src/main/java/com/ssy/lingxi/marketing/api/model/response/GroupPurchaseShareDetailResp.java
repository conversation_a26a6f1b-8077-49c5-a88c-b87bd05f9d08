package com.ssy.lingxi.marketing.api.model.response;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 *  拼团列表
 * <AUTHOR>
 * @since 2021/10/14
 * @version 2.0.0
 */
@Getter
@Setter
public class GroupPurchaseShareDetailResp implements Serializable {

    private static final long serialVersionUID = 8381126066452342386L;
    /**
     * 成团人数
     */
    private Integer assembleNum;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 商品价格
     * */
    private BigDecimal price;
    /**
     * 活动价格
     * */
    private BigDecimal activityPrice;
    /**
     * 活动适应商城的环境 1.Web 2.H5 3.小程序 4.APP
     */
    private List<Integer> environmentList;

    /**
     * 商品Id
     */
    private Long productId;
    /**
     * 商品skuId
     */
    private Long skuId;
    /**
     * 状态 是否进行中
     */
    private Boolean online;

    /**
     * 供应商会员ID
     */
    private Long supplierMemberId;

    /**
     * 供应商会员角色ID
     */
    private Long supplierRoleId;
    /**
     * 商品主图
     */
    private String productImgUrl;
}
