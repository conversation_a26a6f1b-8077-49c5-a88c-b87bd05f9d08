package com.ssy.lingxi.purchase.controller.bidding;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.purchase.api.model.req.PurchaseMakeListReq;
import com.ssy.lingxi.purchase.api.model.req.*;
import com.ssy.lingxi.purchase.api.model.req.bidding.*;
import com.ssy.lingxi.purchase.api.model.req.online.DiscardReq;
import com.ssy.lingxi.purchase.api.model.req.purchase.PurchaseListReq;
import com.ssy.lingxi.purchase.api.model.resp.BiddingExternalWorkStateEnumResp;
import com.ssy.lingxi.purchase.api.model.resp.bidding.*;
import com.ssy.lingxi.purchase.api.model.resp.online.OnlineBiddingListResp;
import com.ssy.lingxi.purchase.api.model.resp.online.OnlineMaterielResp;
import com.ssy.lingxi.purchase.api.model.resp.online.OnlineProductResp;
import com.ssy.lingxi.purchase.api.model.resp.purchase.PurchaseListResp;
import com.ssy.lingxi.purchase.api.model.resp.purchase.SearchReSourceListResp;
import com.ssy.lingxi.purchase.entity.do_.bidding.BiddingQuoteMaterielDO;
import com.ssy.lingxi.purchase.service.bidding.IBiddingService;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.TypeToken;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * 采购能力 - 采购竞价
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/03/23
 */
@RestController
@RequestMapping(ServiceModuleConstant.PURCHASE_PATH_PREFIX + "/bidding")
public class BiddingController extends BaseController {

    @Resource
    private IBiddingService biddingService;

    @Resource
    private HttpServletRequest request;

    /**
     *采购竞价单查询
     */
    @GetMapping("/list")
    public WrapperResp<PageDataResp<BiddingListResp>> biddingList(BiddingListReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        return WrapperUtil.success(biddingService.biddingList(request,sysUser));
    }

    /**
     *采购竞价单详情
     */
    @GetMapping("/details")
    public WrapperResp<BiddingDetailsResp> biddingDetails(BaseDetailsReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        return WrapperUtil.success(biddingService.biddingDetails(request,sysUser));
    }

    /**
     *采购物料分页查询
     */
    @GetMapping("/materiel/page")
    public WrapperResp<PageDataResp<BiddingProductReq>> materielPage(BaseDetailsReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        return WrapperUtil.success(biddingService.materielPage(request,sysUser));
    }

    /**
     *需求对接会员列表分页查询
     */
    @GetMapping("/member/page")
    public WrapperResp<PageDataResp<BiddingMemberResp>> memberPage(BaseDetailsReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        return WrapperUtil.success(biddingService.memberPage(request,sysUser));
    }

    /**
     *报价明细-分页
     */
    @GetMapping("/quoted/price/detaild")
    public WrapperResp<PageDataResp<OnlineProductResp>> quotedPriceDetaild(OnlineDetailsReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        return WrapperUtil.success(biddingService.quotedPriceDetaild(request,sysUser));
    }

    /**
     *作废竞价单
     */
    @PostMapping("/discard")
    public WrapperResp<Void> discard(@Validated @RequestBody DiscardReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        biddingService.discard(request,sysUser);
        return WrapperUtil.success();
    }

    /**
     *待新增采购竞价单列表
     * 待新增采购竞价单
     * @param request 分页列表查询请求参数
     * @param httpServletRequest 请求
     * @return 待新增 采购竞价单分页列表
     */
    @GetMapping("/await/new/list")
    public WrapperResp<PageDataResp<BiddingListResp>> awaitNewList(BiddingListReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        return WrapperUtil.success(biddingService.awaitNewList(request,sysUser));
    }

    /**
     * 待新增商城采购竞价单
     * @param request 分页列表查询请求参数
     * @param httpServletRequest
     * @return 待新增商城采购竞价单分页列表
     */
    @GetMapping("/await/shop/new/list")
    public WrapperResp<PageDataResp<BiddingListResp>> awaitShopNewList(BiddingListReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        return WrapperUtil.success(biddingService.awaitShopNewList(request,sysUser));
    }

    /**
     *添加采购竞价单-待新增采购竞价单
     */
    @PostMapping("/add")
    public WrapperResp<Void> add(@Validated @RequestBody BiddingAddReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        biddingService.add(request,sysUser, modelMapper);
        return WrapperUtil.success();
    }

    /**
     *修改采购竞价单-待新增采购竞价单
     */
    @PostMapping("/update")
    public WrapperResp<Void> update(@Validated @RequestBody BiddingUpdateReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        biddingService.update(request,sysUser,modelMapper);
        return WrapperUtil.success();
    }

    /**
     *删除采购竞价单-待新增采购竞价单
     */
    @PostMapping("/delete")
    public WrapperResp<Void> delete(@Validated @RequestBody BaseReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        biddingService.delete(request,sysUser);
        return WrapperUtil.success();
    }

    /**
     *批量删除采购竞价单-待新增采购竞价单
     */
    @PostMapping("/delete/batch")
    public WrapperResp<Void> deleteBatch(@Validated @RequestBody BaseAllReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        biddingService.deleteBatch(request,sysUser);
        return WrapperUtil.success();
    }

    /**
     *提交待审核采购竞价单-待新增采购竞价单
     */
    @PostMapping("/examine")
    public WrapperResp<Void> biddingExamine(@Validated @RequestBody BaseReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        biddingService.biddingExamine(request,sysUser);
        return WrapperUtil.success();
    }

    /**
     *批量提交待审核采购竞价单-待新增采购竞价单
     */
    @PostMapping("/examine/batch")
    public WrapperResp<Void> biddingExamineBatch(@Validated @RequestBody BaseAllReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        biddingService.biddingExamineBatch(request,sysUser);
        return WrapperUtil.success();
    }

    /**
     *采购竞价-待审核采购竞价单(一级)
     */
    @GetMapping("/stay/examine/list1")
    public WrapperResp<PageDataResp<BiddingListResp>> biddingStayExamineList1(BiddingListReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        return WrapperUtil.success(biddingService.biddingStayExamineList1(request,sysUser));
    }

    /**
     *采购竞价-审核采购竞价单(一级)
     */
    @PostMapping("/examine1")
    public WrapperResp<Void> biddingExamine1(@Validated @RequestBody BaseExamineReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        biddingService.biddingExamine1(request,sysUser);
        return WrapperUtil.success();
    }

    /**
     *采购竞价-审核采购竞价单(一级)批量
     */
    @PostMapping("/examine1/batch")
    public WrapperResp<Void> biddingExamine1Batch(@Validated @RequestBody BaseAllReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        biddingService.biddingExamine1Batch(request,sysUser);
        return WrapperUtil.success();
    }

    /**
     *采购竞价-待审核采购竞价单(二级)
     */
    @GetMapping("/stay/examine/list2")
    public WrapperResp<PageDataResp<BiddingListResp>> biddingStayExamineList2(BiddingListReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        return WrapperUtil.success(biddingService.biddingStayExamineList2(request,sysUser));
    }

    /**
     *采购竞价-审核采购竞价单(二级)
     */
    @PostMapping("/examine2")
    public WrapperResp<Void> biddingExamine2(@Validated @RequestBody BaseExamineReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        biddingService.biddingExamine2(request,sysUser);
        return WrapperUtil.success();
    }

    /**
     *采购竞价-审核采购竞价单(二级)批量
     */
    @PostMapping("/examine2/batch")
    public WrapperResp<Void> biddingExamine2Batch(@Validated @RequestBody BaseAllReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        biddingService.biddingExamine2Batch(request,sysUser);
        return WrapperUtil.success();
    }

    /**
     *采购竞价-待提交采购竞价单
     */
    @GetMapping("/stay/submit/list")
    public WrapperResp<PageDataResp<BiddingListResp>> biddingStaySubmitList(BiddingListReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        return WrapperUtil.success(biddingService.biddingStaySubmitList(request,sysUser));
    }

    /**
     *采购竞价-提交采购竞价单
     */
    @PostMapping("/submit")
    public WrapperResp<Void> biddingSubmit(@Validated @RequestBody BaseExamineReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        biddingService.biddingSubmit(request,sysUser);
        return WrapperUtil.success();
    }

    /**
     *采购竞价-批量提交采购竞价单
     */
    @PostMapping("/submit/batch")
    public WrapperResp<Void> biddingSubmitBatch(@Validated @RequestBody BaseAllReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        biddingService.biddingSubmitBatch(request,sysUser);
        return WrapperUtil.success();
    }

    /**
     * 待审核竞价报名
     */
    @GetMapping("/stay/examin/bidding/list")
    public WrapperResp<PageDataResp<OnlineBiddingListResp>> stayExaminBiddingList(BiddingListReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        return WrapperUtil.success(biddingService.stayExaminBiddingList(request,sysUser));
    }

    /**
     * 审核竞价报名
     */
    @PostMapping("/examin/bidding/signup")
    public WrapperResp<Void> stayExaminBiddingSignup(@Validated @RequestBody BiddingAuditReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        biddingService.stayExaminBiddingSignup(request,sysUser);
        return WrapperUtil.success();
    }

    /**
     * 待竞价
     */
    @GetMapping("/stay/bidding/list")
    public WrapperResp<PageDataResp<BiddingListResp>> stayBiddingList(@Validated BiddingListReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        return WrapperUtil.success(biddingService.stayBiddingList(request,sysUser));
    }

    /**
     * 竞价管理
     */
    @PostMapping("/manage/bidding")
    public WrapperResp<ManageBiddingResp> manageBidding(@Validated  @RequestBody BaseDetailsReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        return WrapperUtil.success(biddingService.manageBidding(request,sysUser));
    }

    /**
     * 竞价动态-竞价管理
     */
    @GetMapping("/dynamic/bidding")
    public WrapperResp<BiddingDynamicResp> dynamicBidding(@Validated BaseDetailsReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        return WrapperUtil.success(biddingService.dynamicBidding(request,sysUser));
    }

    /**
     * 竞价排名-竞价管理
     */
    @GetMapping("/ranking/bidding")
    public WrapperResp<List<QuotationRankingResp>> rankingBidding(@Validated BaseDetailsReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        return WrapperUtil.success(biddingService.rankingBidding(request,sysUser));
    }

    /**
     * 报名会员-竞价管理
     */
    @GetMapping("/signup/member")
    public WrapperResp<List<SignUpInfoResp>> signupMember(@Validated BaseDetailsReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        return WrapperUtil.success(biddingService.signupMember(request,sysUser));
    }

    /**
     * 报价台-竞价物料&-竞价管理
     */
    @GetMapping("/bidding/materiel")
    public WrapperResp<List<AwardProcessResp>> biddingMateriel(@Validated BaseDetailsReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        return WrapperUtil.success(biddingService.biddingMateriel(request,sysUser));
    }

    /**
     * 获取采购竞价单内部流程状态
     */
    @GetMapping("/interior/status")
    public WrapperResp<List<BiddingExternalWorkStateEnumResp>> interiorStatus() {
        return WrapperUtil.success(biddingService.interiorStatus());
    }

    /**
     * 获取采购竞价单外部流程状态
     */
    @GetMapping("/external/status")
    public WrapperResp<List<BiddingExternalWorkStateEnumResp>> externalStatus() {
        return WrapperUtil.success(biddingService.externalStatus());
    }


/********************************************* 审核竞价结果 *************************************************************************/
    /**
     *待提交审核竞价结果
     */
    @GetMapping("/stay/submit/bidding/list")
    public WrapperResp<PageDataResp<StaySubmitBiddingListResp>> staySubmitBiddingList(BiddingListReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        return WrapperUtil.success(biddingService.staySubmitBiddingList(request,sysUser));
    }

    /**
     *提交审核竞价结果详情
     */
    @GetMapping("/stay/submit/details")
    public WrapperResp<StaySubmitDetailsResp> staySubmitDetails(BaseDetailsReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        return WrapperUtil.success(biddingService.staySubmitDetails(request,sysUser));
    }

    /**
     *提交审核竞价结果
     */
    @PostMapping("/submit/examine/bidding/return")
    public WrapperResp<Void> submitExamineBiddingReturn(@Validated @RequestBody SubmitExamineBiddingReturnReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        biddingService.submitExamineBiddingReturn(request,sysUser);
        return WrapperUtil.success();
    }

    /**
     *修改审核竞价结果
     */
    @PostMapping("/update/bidding/return")
    public WrapperResp<Void> updateBiddingReturn(@Validated @RequestBody SubmitExamineBiddingReturnReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        biddingService.updateBiddingReturn(request,sysUser);
        return WrapperUtil.success();
    }

    /**
     *待审核竞价结果（一级）
     */
    @GetMapping("/stay/submit/bidding/list1")
    public WrapperResp<PageDataResp<StaySubmitBiddingListResp>> staySubmitBiddingList1(BiddingListReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        return WrapperUtil.success(biddingService.staySubmitBiddingList1(request,sysUser));
    }

    /**
     *审核竞价结果（一级）
     */
    @PostMapping("/submit/bidding1")
    public WrapperResp<Void> staySubmitBidding1(@Validated @RequestBody BaseExamineReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        biddingService.staySubmitBidding(request,sysUser);
        return WrapperUtil.success();
    }

    /**
     *审核竞价结果（一级）批量
     */
    @PostMapping("/submit/bidding1/batch")
    public WrapperResp<Void> staySubmitBidding1Batch(@Validated @RequestBody BaseAllReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        biddingService.staySubmitBidding1Batch(request,sysUser);
        return WrapperUtil.success();
    }

    /**
     *待审核竞价结果（二级）
     */
    @GetMapping("/stay/submit/bidding/list2")
    public WrapperResp<PageDataResp<StaySubmitBiddingListResp>> staySubmitBiddingList2(BiddingListReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        return WrapperUtil.success(biddingService.staySubmitBiddingList2(request,sysUser));
    }

    /**
     *审核竞价结果（二级）
     */
    @PostMapping("/submit/bidding2")
    public WrapperResp<Void> staySubmitBidding2(@Validated @RequestBody BaseExamineReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        biddingService.staySubmitBidding2(request,sysUser);
        return WrapperUtil.success();
    }

    /**
     *审核竞价结果（二级）批量
     */
    @PostMapping("/submit/bidding2/batch")
    public WrapperResp<Void> staySubmitBidding2Batch(@Validated @RequestBody BaseAllReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        biddingService.staySubmitBidding2Batch(request,sysUser);
        return WrapperUtil.success();
    }

    /**
     * 待确认竞价结果
     */
    @GetMapping("/stay/confirm/bidding/list")
    public WrapperResp<PageDataResp<StaySubmitBiddingListResp>> stayConfirmBiddingList(BiddingListReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        return WrapperUtil.success(biddingService.stayConfirmBiddingList(request,sysUser));
    }

    /**
     * 确认竞价结果
     */
    @PostMapping("/stay/confirm/bidding")
    public WrapperResp<Void> stayConfirmBidding(@Validated @RequestBody BaseExamineReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        biddingService.stayConfirmBidding(request,sysUser);
        return WrapperUtil.success();
    }

    /**
     * 采购商机筛选-竞价单查询
     */
    @GetMapping("/business/opportunity/bidding/list")
    public WrapperResp<PageDataResp<PurchaseListResp>> businessPpportunityBiddingList(@Validated PurchaseListReq request) {
        return WrapperUtil.success(biddingService.businessPpportunityBiddingList(request));
    }

    /**
     * 采购公示
     */
    @GetMapping("/purchase/make/list")
    public WrapperResp<PageDataResp<com.ssy.lingxi.purchase.api.model.req.bidding.PurchaseMakeListReq>> purchaseMakeList(PurchaseMakeListReq request){
        return WrapperUtil.success(biddingService.purchaseMakeList(request));
    }

    /**
     *采购寻源--采购竞价--采购竞价详情
     */
    @GetMapping("/search/source/details")
    public WrapperResp<BiddingDetailsResp> searchReSourceDetails(BaseDetailsReq request, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        return WrapperUtil.success(biddingService.searchReSourceDetails(request, sysUser));
    }

    /**
     *采购物料分页查询
     */
    @GetMapping("/search/source/materiel/page")
    public WrapperResp<PageDataResp<BiddingProductReq>> searchSourceMaterielPage(BaseDetailsReq request) {
        return WrapperUtil.success(biddingService.searchSourceMaterielPage(request));
    }

    /**
     * 分页查询在线竞标-中标的采购物料
     * @param pageDataReq 分页实体
     * @param id 竞标id
     */
    @GetMapping("/prize/materiel/page")
    public WrapperResp<PageDataResp<OnlineMaterielResp>> prizeMaterielPage(PageDataReq pageDataReq, @RequestParam("id") Long id) {
        PageDataResp<BiddingQuoteMaterielDO> pageDataResp = biddingService.prizeMaterielPage(pageDataReq, id);
        Long totalCount = pageDataResp.getTotalCount();
        if(totalCount == null || totalCount <= 0){
            return WrapperUtil.success(new PageDataResp<>(totalCount, new ArrayList<>()));
        }
        List<OnlineMaterielResp> responses = modelMapper.map(pageDataResp.getData(), new TypeToken<List<OnlineMaterielResp>>() {}.getType());
        return WrapperUtil.success(new PageDataResp<>(totalCount, responses));
    }

    /*****************************************************  采购门户  ********************************************************************/
    /**
     *商城能力--企业直采--采购门户--采购寻源筛选-接口
     */
    @GetMapping("/search/source/list")
    public WrapperResp<PageDataResp<SearchReSourceListResp>> searchReSourceList(PurchaseListReq purchaseListReq) {
        Long shopId = StringUtils.isEmpty(request.getHeader("shopId")) ? null : Long.parseLong(request.getHeader("shopId"));
        return WrapperUtil.success(biddingService.searchReSourceList(purchaseListReq, shopId, this.getSysUserOrNull()));
    }
}
