package com.ssy.lingxi.purchase.entity.do_.bidding;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ssy.lingxi.common.constant.TableNameConstant;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;

/**
 * 采购竞价需求对接会员表
 */
@Getter
@Setter
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_PURCHASE_SERVICE + "bidding_demand_member")
public class BiddingDemandMemberDO {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 采购竞价信息
     */
    @JsonIgnore
    @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE})
    @JoinColumn(name = "bidding_id", referencedColumnName = "id")
    private BiddingDO bidding;

    /**
     * 会员Id
     */
    @Column(columnDefinition = "int8")
    private Long memberId;

    /**
     * 会员名称
     */
    @Column(columnDefinition = "varchar(200)")
    private String memberName;

    /**
     * 会员类型名称
     */
    @Column(columnDefinition = "varchar(50)")
    private String memberTypeName;

    /**
     * 会员角色Id
     */
    @Column(columnDefinition = "int8")
    private Long roleId;

    /**
     * 会员角色名称
     */
    @Column(columnDefinition = "varchar(50)")
    private String roleName;

    /**
     * 会员等级
     */
    @Column(columnDefinition = "varchar(50)")
    private String levelTag;

    /**
     * 发送邀请 0.无需发送邀请 1.发送邀请
     */
    @Column(columnDefinition = "int2")
    private Integer state = 1;
}
