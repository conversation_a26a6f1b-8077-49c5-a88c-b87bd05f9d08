package com.ssy.lingxi.purchase.service.purchase;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.engine.PolicyResultResp;
import com.ssy.lingxi.component.base.enums.order.OrderPurchaseProcessTypeEnum;
import com.ssy.lingxi.order.api.model.resp.OrderPurchaseProcessFeignDetailResp;
import com.ssy.lingxi.purchase.entity.do_.purchase.requisition.PurchaseRequisitionDO;
import com.ssy.lingxi.purchase.model.req.RequisitionPageDataReq;
import com.ssy.lingxi.purchase.model.req.RequisitionPageSRMDataReq;
import com.ssy.lingxi.purchase.model.req.RequisitionProductReq;
import com.ssy.lingxi.purchase.model.resp.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-10-28
 */
public interface ICommonService {
    /**
     * 校验Srm订单商品接口参数
     *
     * @param requisition 订单
     * @param products    订单物料列表
     * @param isCreate    是否新增，true表示新增， false表示修改
     * @return 订单总金融
     */
    PurchaseProductCheckResp checkRequisitionProduct(PurchaseRequisitionDO requisition, List<RequisitionProductReq> products, boolean isCreate);

    /**
     * 启动订单流程
     *
     * @param requisition 请购单
     * @return 启动结果
     */
    PurchaseProcessTaskResp startPurchaseProcess(PurchaseRequisitionDO requisition);

    /**
     * 启动并执行下一个订单流程
     *
     * @param requisition 请购单
     * @return 启动结果
     */
    PurchaseProcessTaskResp startPurchaseProcessThenCompleteFirstTask(PurchaseRequisitionDO requisition);

    /**
     * 查询（采购）会员采购流程配置
     *
     * @param buyerMemberId           采购会员Id
     * @param buyerRoleId             采购会员角色Id
     * @param purchaseProcessTypeEnum 采购流程类型
     * @return 查询结果
     */
    OrderPurchaseProcessFeignDetailResp findPurchaseProcess(Long buyerMemberId, Long buyerRoleId, OrderPurchaseProcessTypeEnum purchaseProcessTypeEnum);

    /**
     * 保存订单内部流转记录
     *
     * @param memberId         登录用户会员Id
     * @param roleId           登录用户会员角色Id
     * @param userName         登录用户姓名
     * @param organizationName 登录用户组织机构名称
     * @param jobTitle         登录用户职位
     * @param orderId          订单Id
     * @param operation        操作
     * @param statusName       订单状态名称
     * @param remark           备注
     */
    void savePurchaseInnerHistory(Long memberId, Long roleId, String userName, String organizationName, String jobTitle, Long orderId, String operation, String statusName, String remark);

    /**
     * 查询请购单流程
     *
     * @param processKey     流程Key
     * @param taskId         流程步骤任务Id
     * @param vendorMemberId 供应商会员Id
     * @return 查询结果
     */
    ProcessStepsResp findRequisitionProcessSteps(String processKey, String taskId, Long vendorMemberId);

    /**
     * 订单商品汇总信息
     *
     * @param requisition 订单
     * @return 订单商品汇总
     */
    PurchaseProductResp purchaseProducts(PurchaseRequisitionDO requisition);

    /**
     * 查询采购订单内部流转记录
     *
     * @param buyerMemberId 采购会员Id
     * @param buyerRoleId   采购会员角色Id
     * @param orderId       订单Id
     * @return 内部流转记录
     */
    List<PurchaseInnerHistoryResp> listBuyerOrderInnerHistories(Long buyerMemberId, Long buyerRoleId, Long orderId);

    /**
     * 执行请购单下个流程
     *
     * @param requisition 请购单
     * @param agree       执行下个流程跳转参数
     * @return 内部流转记录
     */
    PurchaseProcessTaskResp executeRequisitionProcess(PurchaseRequisitionDO requisition, Integer agree);

    /**
     * 公共的请购单分页实现
     *
     * @param loginUser          登录用户信息
     * @param req                分页请求参数
     * @param buyerInnerStatus   内部状态集合
     * @param isTransferPurchase 是否为请购单转采购订单分页
     * @return 查询结果
     */
    PageDataResp<RequisitionPageResp> getBaseRequisitionPage(UserLoginCacheDTO loginUser, RequisitionPageDataReq req, List<Integer> buyerInnerStatus, Boolean isTransferPurchase);

    /**
     * SRM端-公共的请购单分页实现
     *
     * @param loginUser          登录用户信息
     * @param req                分页请求参数
     * @param buyerInnerStatus   内部状态集合
     * @param isTransferPurchase 是否为请购单转采购订单分页
     * @param isCheck            是否是审核列表
     * @return 查询结果
     */
    PageDataResp<RequisitionPageResp> getBaseRequisitionSRMPage(UserLoginCacheDTO loginUser, RequisitionPageSRMDataReq req, List<Integer> buyerInnerStatus, Boolean isTransferPurchase, Boolean isCheck);

    /**
     * 连续执行工作流
     *
     * @param loginUser   当前登录用户
     * @param requisition 请购单实体
     * @param agree       是否同意
     * @param policy      决策结果
     * @return 操作结果
     */
    PurchaseProcessTaskResp executeWorkSerialTasks(UserLoginCacheDTO loginUser, PurchaseRequisitionDO requisition, Integer agree, PolicyResultResp policy);

    /**
     * 新增请购单合同数据
     *
     * @param requisition 请购单实体
     */
    void sendAsyncPurchaseRequisition(PurchaseRequisitionDO requisition);

    /**
     * 校验物料是否同一个引擎
     *
     * @param requisition 请购单
     * @param buyerMemberId            会员ID
     * @param buyerRoleId        会员角色ID
     * @param type                流程类型
     */
    void checkEngine(PurchaseRequisitionDO requisition, Long buyerMemberId, Long buyerRoleId, Integer type);
}