package com.ssy.lingxi.purchase.repository.tender;

import com.ssy.lingxi.purchase.entity.do_.tender.ExpertDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * 评标专家库持久化层
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/3/22
 */
public interface ExpertRepository extends JpaRepository<ExpertDO,Long>, JpaSpecificationExecutor<ExpertDO> {
    List<ExpertDO> findByMemberIdAndMemberRoleIdAndNameAndIdNot(Long memberId, Long memberRoleId, String name, Long id);

    List<ExpertDO> findByMemberIdAndMemberRoleIdAndName(Long memberId, Long memberRoleId, String name);

    List<ExpertDO> findByMemberIdAndMemberRoleIdAndIdIn(Long memberId, Long memberRoleId, List<Long> expertIdList);
}
