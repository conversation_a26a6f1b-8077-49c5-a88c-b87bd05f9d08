package com.ssy.lingxi.purchase.serviceImpl.purchase;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.dto.engine.EngineResultDTO;
import com.ssy.lingxi.common.model.req.engine.EngineRuleQueryReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.model.resp.engine.PolicyResultResp;
import com.ssy.lingxi.common.model.resp.engine.ProcessEngineRuleResp;
import com.ssy.lingxi.common.model.resp.engine.RuleEngineConfigResp;
import com.ssy.lingxi.common.util.DateTimeUtil;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.order.OrderPurchaseProcessTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.EngineRuleUtil;
import com.ssy.lingxi.component.base.util.JpaUtil;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.contract.api.feign.IPurchaseRequisitionContractFeign;
import com.ssy.lingxi.contract.api.model.req.PurchaseRequisitionAddReq;
import com.ssy.lingxi.contract.api.model.req.PurchaseRequisitionProductReq;
import com.ssy.lingxi.engine.api.feign.IProcessEngineRuleFeign;
import com.ssy.lingxi.engine.api.feign.IRuleEngineConfigFeign;
import com.ssy.lingxi.engine.api.model.req.PolicyReq;
import com.ssy.lingxi.engine.api.model.req.RuleEngineByPredicateReq;
import com.ssy.lingxi.member.api.feign.IMemberLifeCycleStageRuleFeign;
import com.ssy.lingxi.member.api.model.req.MemberFeignWithLifeCycleRuleReq;
import com.ssy.lingxi.member.api.model.req.MemberLifeCycleRuleCheckReq;
import com.ssy.lingxi.member.api.model.resp.MemberFeignLifeCycleRuleResp;
import com.ssy.lingxi.order.api.feign.IOrderProcessFeign;
import com.ssy.lingxi.order.api.model.req.OrderPurchaseProcessFeignReq;
import com.ssy.lingxi.order.api.model.resp.OrderPurchaseProcessFeignDetailResp;
import com.ssy.lingxi.purchase.constant.PurchaseConstant;
import com.ssy.lingxi.purchase.entity.do_.purchase.PurchaseInnerHistoryDO;
import com.ssy.lingxi.purchase.entity.do_.purchase.requisition.PurchaseRequisitionDO;
import com.ssy.lingxi.purchase.entity.do_.purchase.requisition.PurchaseRequisitionProductDO;
import com.ssy.lingxi.purchase.entity.do_.purchase.requisition.QPurchaseRequisitionDO;
import com.ssy.lingxi.purchase.enums.PurchaseRequisitionProcessEnum;
import com.ssy.lingxi.purchase.enums.RequisitionInnerStatusEnum;
import com.ssy.lingxi.purchase.enums.RequisitionOperationEnum;
import com.ssy.lingxi.purchase.model.req.RequisitionPageDataReq;
import com.ssy.lingxi.purchase.model.req.RequisitionPageSRMDataReq;
import com.ssy.lingxi.purchase.model.req.RequisitionProductReq;
import com.ssy.lingxi.purchase.model.resp.*;
import com.ssy.lingxi.purchase.repository.purchase.PurchaseInnerHistoryRepository;
import com.ssy.lingxi.purchase.repository.purchase.PurchaseProductRepository;
import com.ssy.lingxi.purchase.repository.purchase.requisition.PurchaseRequisitionRepository;
import com.ssy.lingxi.purchase.service.purchase.ICommonService;
import com.ssy.lingxi.workflow.api.feign.IProcessFeign;
import com.ssy.lingxi.workflow.api.model.req.InternalProcessQueryReq;
import com.ssy.lingxi.workflow.api.model.req.SerialTaskExecuteReq;
import com.ssy.lingxi.workflow.api.model.req.TaskExecuteReq;
import com.ssy.lingxi.workflow.api.model.req.TaskStartReq;
import com.ssy.lingxi.workflow.api.model.resp.ComplexTaskCompleteResp;
import com.ssy.lingxi.workflow.api.model.resp.SimpleProcessDefResp;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-10-28
 */
@Service
public class CommonServiceImpl implements ICommonService {
    @Resource
    private PurchaseProductRepository purchaseProductRepository;
    @Resource
    private IProcessFeign processFeign;
    @Resource
    private IOrderProcessFeign orderFeignService;
    @Resource
    private PurchaseInnerHistoryRepository purchaseInnerHistoryRepository;
    @Resource
    private JPAQueryFactory jpaQueryFactory;
    @Resource
    private PurchaseRequisitionRepository purchaseRequisitionRepository;
    @Resource
    private IRuleEngineConfigFeign ruleEngineConfigFeign;
    @Resource
    private IProcessFeign taskExecuteControllerFeign;
    @Resource
    private IPurchaseRequisitionContractFeign purchaseRequisitionControllerFeign;
    @Resource
    private IProcessEngineRuleFeign processEngineRuleFeign;
    @Resource
    private IMemberLifeCycleStageRuleFeign memberLifeCycleStageRuleFeign;


    /**
     * 校验请购单商品接口参数
     *
     * @param requisition 订单
     * @param products    订单物料列表
     * @param isCreate    是否新增，true表示新增， false表示修改
     * @return 订单总金融
     */
    @Override
    public PurchaseProductCheckResp checkRequisitionProduct(PurchaseRequisitionDO requisition, List<RequisitionProductReq> products, boolean isCreate) {
        //商品总金额
        BigDecimal productAmount = BigDecimal.ZERO;
        //请购总数量
        BigDecimal quantity = BigDecimal.ZERO;

        List<PurchaseRequisitionProductDO> productList = new ArrayList<>();
        for (RequisitionProductReq productReq : products) {
            PurchaseRequisitionProductDO product = new PurchaseRequisitionProductDO();
            product.setPurchaseRequisition(requisition);
            product.setProductId(productReq.getProductId());
            product.setProductNo(productReq.getProductNo());
            product.setName(productReq.getName());
            product.setCategory(productReq.getCategory());
            product.setCategoryId(productReq.getCategoryId());
            product.setBrand(StringUtils.hasLength(productReq.getBrand()) ? productReq.getBrand() : "");
            product.setSpec(StringUtils.hasLength(productReq.getSpec()) ? productReq.getSpec() : "");
            product.setUnit(productReq.getUnit());
            product.setPrice(productReq.getPrice());
            product.setQuantity(productReq.getQuantity());
            //剩余数量初始与请购数量一致
            product.setRemainQuantity(productReq.getQuantity());
            product.setAmount(NumberUtil.isNullOrZero(productReq.getPrice()) ? BigDecimal.ZERO : productReq.getPrice().multiply(productReq.getQuantity()));
            //新增或修改已转采购订单数量设为0
            product.setTransferQuantity(BigDecimal.ZERO);
            product.setGoodsGroup(productReq.getGoodsGroup());
            product.setManuFacturer(productReq.getManuFacturer());
            product.setPlaceOrigin(productReq.getPlaceOrigin());
            product.setRemark(productReq.getRemark());
            product.setOrderProductIds(productReq.getOrderProductIds());
            //统计商品总金额
            productAmount = productAmount.add(product.getAmount());
            //统计请购总数量
            quantity = quantity.add(product.getQuantity());
            //设置物料图片
            product.setGoodsPic(productReq.getGoodsPic());

            productList.add(product);
        }

        //如果是修改，删除原有数据
        if (!isCreate) {
            purchaseProductRepository.deleteByPurchaseRequisition(requisition);
        }
        purchaseProductRepository.saveAll(productList);

        requisition.setProducts(new HashSet<>(productList));
        return new PurchaseProductCheckResp(productAmount, quantity);
    }

    /**
     * 启动订单流程
     *
     * @param requisition 请购单
     * @return 启动结果
     */
    @Override
    public PurchaseProcessTaskResp startPurchaseProcess(PurchaseRequisitionDO requisition) {
        TaskStartReq startVO = new TaskStartReq();
        startVO.setProcessKey(requisition.getTask().getProcessKey());
        startVO.setMemberId(requisition.getBuyerMemberId());
        startVO.setRoleId(requisition.getBuyerRoleId());
        startVO.setDataId(requisition.getId());
        //这里是启动流程
        WrapperResp<ComplexTaskCompleteResp> result = processFeign.startProcess(startVO);
        if (result.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            throw new BusinessException(result.getCode(),result.getMessage());
        }

        PurchaseProcessTaskResp taskResult = new PurchaseProcessTaskResp(result.getData());

        //供应商内部状态，以及下一步的任务Id
        requisition.setBuyerInnerStatus(taskResult.getBuyerInnerStatus());
        requisition.getTask().setTaskId(taskResult.getTaskId());

        return taskResult;
    }

    /**
     * 启动并执行下一个订单流程
     *
     * @param requisition 请购单
     * @return 启动结果
     */
    @Override
    public PurchaseProcessTaskResp startPurchaseProcessThenCompleteFirstTask(PurchaseRequisitionDO requisition) {
        TaskStartReq startVO = new TaskStartReq();
        startVO.setProcessKey(requisition.getTask().getProcessKey());
        startVO.setMemberId(requisition.getBuyerMemberId());
        startVO.setRoleId(requisition.getBuyerRoleId());
        startVO.setDataId(requisition.getId());
        //这里是启动流程
        WrapperResp<ComplexTaskCompleteResp> result = processFeign.startProcessThenCompleteFirstTask(startVO);
        if (result.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            throw new BusinessException(result.getCode(),result.getMessage());
        }

        PurchaseProcessTaskResp taskResult = new PurchaseProcessTaskResp(result.getData());

        //供应商内部状态，以及下一步的任务Id
        requisition.setBuyerInnerStatus(taskResult.getBuyerInnerStatus());
        requisition.setBuyerInnerStep(taskResult.getBuyerInnerStep());
        requisition.getTask().setTaskId(taskResult.getTaskId());

        return taskResult;
    }

    /**
     * 查询（采购）会员采购流程配置
     *
     * @param buyerMemberId           采购会员Id
     * @param buyerRoleId             采购会员角色Id
     * @param purchaseProcessTypeEnum 采购流程类型
     * @return 查询结果
     */
    @Override
    public OrderPurchaseProcessFeignDetailResp findPurchaseProcess(Long buyerMemberId, Long buyerRoleId, OrderPurchaseProcessTypeEnum purchaseProcessTypeEnum) {
        OrderPurchaseProcessFeignReq feignVO = new OrderPurchaseProcessFeignReq();
        feignVO.setMemberId(buyerMemberId);
        feignVO.setRoleId(buyerRoleId);
        feignVO.setProcessTypeEnum(purchaseProcessTypeEnum);
        WrapperResp<OrderPurchaseProcessFeignDetailResp> respWrapperResp = orderFeignService.findBuyerPurchaseProcess(feignVO);
        return respWrapperResp.getData();
    }

    /**
     * 保存订单内部流转记录
     *
     * @param memberId         登录用户会员Id
     * @param roleId           登录用户会员角色Id
     * @param userName         登录用户姓名
     * @param organizationName 登录用户组织机构名称
     * @param jobTitle         登录用户职位
     * @param orderId          订单Id列表
     * @param operation        操作
     * @param statusName       订单状态名称
     * @param remark           备注
     */
    @Async
    @Override
    public void savePurchaseInnerHistory(Long memberId, Long roleId, String userName, String organizationName, String jobTitle, Long orderId, String operation, String statusName, String remark) {
        PurchaseInnerHistoryDO innerHistory = new PurchaseInnerHistoryDO();
        innerHistory.setCreateTime(LocalDateTime.now());
        innerHistory.setOrderId(orderId);
        innerHistory.setMemberId(memberId);
        innerHistory.setRoleId(roleId);
        innerHistory.setOperator(userName);
        innerHistory.setDepartment(organizationName);
        innerHistory.setJobTitle(jobTitle);
        innerHistory.setOperation(operation);
        innerHistory.setStatusName(statusName);
        innerHistory.setRemark(StringUtils.hasLength(remark) ? remark.trim() : "");
        purchaseInnerHistoryRepository.save(innerHistory);
    }

    /**
     * 查询请购单流程
     *
     * @param processKey     流程Key
     * @param taskId         流程步骤任务Id
     * @param vendorMemberId 供应商会员Id
     * @return 查询结果
     */
    @Override
    public ProcessStepsResp findRequisitionProcessSteps(String processKey, String taskId, Long vendorMemberId) {
        InternalProcessQueryReq queryVO = new InternalProcessQueryReq();
        queryVO.setProcessKey(processKey);
        queryVO.setTaskId(taskId);
        queryVO.setMemberId(vendorMemberId);
        WrapperResp<SimpleProcessDefResp> result = processFeign.findSimpleInternalTaskDefinitions(queryVO);
        if (result.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            throw new BusinessException(result.getCode(),result.getMessage());
        }

        ProcessStepsResp stepsRes = new ProcessStepsResp();
        stepsRes.setInnerSteps(result.getData().getTasks().stream().map(internalTask -> new WorkFlowStepResp(internalTask.getTaskStep(), internalTask.getTaskName(), internalTask.getRoleName())).sorted(Comparator.comparingInt(WorkFlowStepResp::getStep)).collect(Collectors.toList()));
        stepsRes.setCurrentInnerStep(StringUtils.hasLength(taskId) ? result.getData().getCurrentStep() : stepsRes.getInnerSteps().stream().max(Comparator.comparingInt(WorkFlowStepResp::getStep)).map(WorkFlowStepResp::getStep).orElse(0));
        return stepsRes;
    }

    /**
     * 订单商品汇总信息
     *
     * @param requisition 订单
     * @return 订单商品汇总
     */
    @Override
    public PurchaseProductResp purchaseProducts(PurchaseRequisitionDO requisition) {
        PurchaseProductsResp summaryRes = new PurchaseProductsResp();
        //商品总金额
        summaryRes.setProductAmount(NumberUtil.formatAmount(requisition.getProductAmount()));
        //请购总数量
        summaryRes.setQuantity(NumberUtil.formatToInteger(requisition.getQuantity()));
        //已转采购订单总数量
        summaryRes.setTransferQuantity(NumberUtil.formatToInteger(requisition.getTransferQuantity()));
        List<PurchaseRequisitionProductDO> orderProducts = purchaseProductRepository.findByPurchaseRequisition(requisition, Sort.by("id").ascending());
        summaryRes.setProducts(orderProducts.stream().map(product -> {
            PurchaseProductDetailResp detailVO = new PurchaseProductDetailResp();
            detailVO.setId(product.getId());
            detailVO.setProductId(product.getProductId());
            detailVO.setProductNo(StringUtils.hasLength(product.getProductNo()) ? product.getProductNo() : "");
            detailVO.setName(product.getName());
            detailVO.setCategory(product.getCategory());
            detailVO.setBrand(product.getBrand());
            detailVO.setUnit(product.getUnit());
            detailVO.setSpec(StringUtils.hasLength(product.getSpec()) ? product.getSpec() : "");
            detailVO.setQuantity(NumberUtil.formatToInteger(product.getQuantity()));
            detailVO.setTransferQuantity(NumberUtil.formatToInteger(product.getTransferQuantity()));
            detailVO.setPrice(product.getPrice());
            detailVO.setAmount(NumberUtil.formatAmount(product.getAmount()));
            detailVO.setOrderProductIds(product.getOrderProductIds());
            BeanUtils.copyProperties(product, detailVO);
            return detailVO;
        }).collect(Collectors.toList()));

        PurchaseProductResp productRes = new PurchaseProductResp();
        productRes.setProductVO(summaryRes);
        return productRes;
    }

    /**
     * 查询采购订单内部流转记录
     *
     * @param buyerMemberId 采购会员Id
     * @param buyerRoleId   采购会员角色Id
     * @param orderId       订单Id
     * @return 内部流转记录
     */
    @Override
    public List<PurchaseInnerHistoryResp> listBuyerOrderInnerHistories(Long buyerMemberId, Long buyerRoleId, Long orderId) {
        return purchaseInnerHistoryRepository.findByMemberIdAndRoleIdAndOrderId(buyerMemberId, buyerRoleId, orderId, Sort.by("id").descending()).stream().map(historyDO -> {
            PurchaseInnerHistoryResp historyVO = new PurchaseInnerHistoryResp();
            historyVO.setId(historyDO.getId());
            historyVO.setCreateTime(historyDO.getCreateTime().format(PurchaseConstant.DEFAULT_TIME_FORMATTER));
            historyVO.setOperator(historyDO.getOperator());
            historyVO.setDepartment(historyDO.getDepartment());
            historyVO.setJobTitle(historyDO.getJobTitle());
            historyVO.setOperation(historyDO.getOperation());
            historyVO.setStatusName(historyDO.getStatusName());
            historyVO.setRemark(historyDO.getRemark());
            return historyVO;
        }).collect(Collectors.toList());
    }

    /**
     * 执行请购单下个流程
     *
     * @param requisition 请购单
     * @param agree       执行下个流程跳转参数
     * @return 内部流转记录
     */
    @Override
    public PurchaseProcessTaskResp executeRequisitionProcess(PurchaseRequisitionDO requisition, Integer agree) {
        TaskExecuteReq executeVO = new TaskExecuteReq();
        executeVO.setProcessKey(requisition.getTask().getProcessKey());
        executeVO.setTaskId(requisition.getTask().getTaskId());
        executeVO.setMemberId(requisition.getBuyerMemberId());
        executeVO.setRoleId(requisition.getBuyerRoleId());
        executeVO.setDataId(requisition.getId());
        executeVO.setAgree(agree == null ? -1 : agree);

        WrapperResp<ComplexTaskCompleteResp> result = processFeign.completeTask(executeVO);
        if (result.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            throw new BusinessException(result.getCode(),result.getMessage());
        }

        PurchaseProcessTaskResp taskResult = new PurchaseProcessTaskResp(result.getData());

        requisition.setBuyerInnerStatus(taskResult.getBuyerInnerStatus());
        requisition.setBuyerInnerStep(taskResult.getBuyerInnerStep());
        requisition.getTask().setTaskId(taskResult.getTaskId());
        return taskResult;
    }

    /**
     * 公共的请购单分页实现
     *
     * @param loginUser          登录用户信息
     * @param req                分页请求参数
     * @param buyerInnerStatus   内部状态集合
     * @param isTransferPurchase 是否为请购单转采购订单分页
     * @return 查询结果
     */
    @Override
    public PageDataResp<RequisitionPageResp> getBaseRequisitionPage(UserLoginCacheDTO loginUser, RequisitionPageDataReq req, List<Integer> buyerInnerStatus, Boolean isTransferPurchase) {
        //构造查询条件
        BooleanBuilder predicates = new BooleanBuilder();
        //表实体
        QPurchaseRequisitionDO qRequisition = QPurchaseRequisitionDO.purchaseRequisitionDO;

        predicates.and(qRequisition.buyerMemberId.eq(loginUser.getMemberId()));
        predicates.and(qRequisition.buyerRoleId.eq(loginUser.getMemberRoleId()));
        if (StrUtil.isNotBlank(req.getRequisitionNo())) {
            predicates.and(qRequisition.requisitionNo.like("%" + req.getRequisitionNo() + "%"));
        }
        if (StrUtil.isNotBlank(req.getDigest())) {
            predicates.and(qRequisition.digest.like("%" + req.getDigest() + "%"));
        }
        if (StrUtil.isNotBlank(req.getMemberName())) {
            predicates.and(qRequisition.vendorMemberName.like("%" + req.getMemberName() + "%"));
        }
        if (StrUtil.isNotBlank(req.getStartDate())) {
            predicates.and(qRequisition.advanceDeliveryDate.goe(LocalDateTime.parse(req.getStartDate().concat(" 00:00:00"), PurchaseConstant.DEFAULT_TIME_FORMATTER)));
        }
        if (StrUtil.isNotBlank(req.getEndDate())) {
            predicates.and(qRequisition.advanceDeliveryDate.loe(LocalDateTime.parse(req.getEndDate().concat(" 00:00:00"), PurchaseConstant.DEFAULT_TIME_FORMATTER)));
        }
        if (StrUtil.isNotBlank(req.getDepartment())) {
            predicates.and(qRequisition.department.like("%" + req.getDepartment() + "%"));
        }
        if (StrUtil.isNotBlank(req.getPurpose())) {
            predicates.and(qRequisition.purpose.like("%" + req.getPurpose() + "%"));
        }
        if (req.getInnerStatus() != null) {
            predicates.and(qRequisition.buyerInnerStatus.eq(req.getInnerStatus()));
        }
        if (req.getInnerStatus() == null && CollUtil.isNotEmpty(buyerInnerStatus)) {
            predicates.and(qRequisition.buyerInnerStatus.in(buyerInnerStatus));
        }
        if (isTransferPurchase != null && isTransferPurchase) {
            predicates.and(qRequisition.buyerInnerStatus.eq(RequisitionInnerStatusEnum.BUYER__SUBMITTED.getCode())).and(qRequisition.transferQuantity.lt(qRequisition.quantity));
        }
        if (ObjectUtil.isNotNull(req.getRequisitioner())) {
            predicates.and(qRequisition.requisitioner.like("%" + req.getRequisitioner() + "%"));
        }

        if (NumberUtil.notNullOrZero(req.getLifeCycleStageRuleId())) {
            WrapperResp<Boolean> wrapperResp = memberLifeCycleStageRuleFeign.checkMemberHasConfigureLifeCycleRule(buildMemberLifeCycleRuleCheckVO(loginUser, req.getLifeCycleStageRuleId()));
            if (wrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                throw new BusinessException(wrapperResp.getCode(), wrapperResp.getMessage());
            }

            // 如果登录人没配置对应的生命周期，则返回空列表
            if (!wrapperResp.getData()) {
                return new PageDataResp<>(0L, Collections.emptyList());
            }

            WrapperResp<List<MemberFeignLifeCycleRuleResp>> response = memberLifeCycleStageRuleFeign.getSubMemberWithLifecycleRule(buildMemberFeignWithLifeCycleRuleVO(loginUser, req.getLifeCycleStageRuleId()));
            if (response.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                throw new BusinessException(response.getCode(),response.getMessage());
            }

            // 过滤允许创建该供应商的订单的下级会员
            List<MemberFeignLifeCycleRuleResp> memberFeignLifeCycleRuleRespList = response.getData();
            BooleanBuilder lifeCycleRuleBuilder = new BooleanBuilder();
            memberFeignLifeCycleRuleRespList.forEach(vo -> lifeCycleRuleBuilder.or(qRequisition.vendorMemberId.eq(vo.getMemberId()).and(qRequisition.vendorRoleId.eq(vo.getRoleId()))));
            predicates.and(lifeCycleRuleBuilder);
        }

        JPAQuery<PurchaseRequisitionDO> query = jpaQueryFactory.select(qRequisition).from(qRequisition).where(predicates);

        long totalCount = query.fetchCount();

        List<RequisitionPageResp> requisitionList = query.orderBy(qRequisition.id.desc()).offset(req.getCurrentOffset()).limit(req.getPageSize()).fetch().stream().map(RequisitionPageResp::new).collect(Collectors.toList());

        return new PageDataResp<>(totalCount, requisitionList);
    }

    /**
     * 构造规则引擎拼接查询条件
     *
     * @param finalConditions 条件实体
     * @param criteriaBuilder 标准生成器
     * @param root            数据源
     */
    private Predicate addEnginePredicate(Predicate finalConditions, CriteriaBuilder criteriaBuilder, Root<PurchaseRequisitionDO> root, RequisitionPageDataReq req, List<Integer> buyerInnerStatus, UserLoginCacheDTO loginUser) {

        if (req.getInnerStatus() == null && CollUtil.isEmpty(buyerInnerStatus)) {
            return finalConditions;
        }
        if (req.getInnerStatus() != null) {
            switch (req.getInnerStatus()) {
                case 2://待审核一级
                    return toBeCheckOne(finalConditions, criteriaBuilder, root, loginUser);
                case 4://待审核二级
                    return toBeCheckTwo(finalConditions, criteriaBuilder, root, loginUser);
                case 6://待提交
                    return toBeSubmit(finalConditions, criteriaBuilder, root, loginUser);
                default:
                    return finalConditions;
            }
        }
        if (CollUtil.isNotEmpty(buyerInnerStatus)) {
            req.setInnerStatus(buyerInnerStatus.get(0));
            switch (req.getInnerStatus()) {
                case 2://待审核一级
                    return toBeCheckOne(finalConditions, criteriaBuilder, root, loginUser);
                case 4://待审核二级
                    return toBeCheckTwo(finalConditions, criteriaBuilder, root, loginUser);
                case 6://待提交
                    return toBeSubmit(finalConditions, criteriaBuilder, root, loginUser);
                default:
                    return finalConditions;
            }
        }
        return finalConditions;
    }

    /**
     * 构造规则引擎拼接查询条件(重载方法)
     *
     * @param finalConditions 条件实体
     * @param criteriaBuilder 标准生成器
     * @param root            数据源
     */
    private Predicate addEnginePredicate(Predicate finalConditions, CriteriaBuilder criteriaBuilder, Root<PurchaseRequisitionDO> root, RequisitionPageSRMDataReq req, List<Integer> buyerInnerStatus, UserLoginCacheDTO loginUser, Boolean isCheck) {
        if (isCheck) {
            if (req.getInnerStatus() == null && CollUtil.isEmpty(buyerInnerStatus)) {
                //下面三条(待审核一级,待审核二级,待提交)的总和
                return toBeCheckAll(finalConditions, criteriaBuilder, root, loginUser);
            }
            if (req.getInnerStatus() != null) {
                switch (req.getInnerStatus()) {
                    case 2://待审核一级
                        return toBeCheckOne(finalConditions, criteriaBuilder, root, loginUser);
                    case 4://待审核二级
                        return toBeCheckTwo(finalConditions, criteriaBuilder, root, loginUser);
                    case 6://待提交
                        return toBeSubmit(finalConditions, criteriaBuilder, root, loginUser);
                    default:
                        return finalConditions;
                }
            }
            if (CollUtil.isNotEmpty(buyerInnerStatus)) {
                req.setInnerStatus(buyerInnerStatus.get(0));
                switch (req.getInnerStatus()) {
                    case 2://待审核一级
                        return toBeCheckOne(finalConditions, criteriaBuilder, root, loginUser);
                    case 4://待审核二级
                        return toBeCheckTwo(finalConditions, criteriaBuilder, root, loginUser);
                    case 6://待提交
                        return toBeSubmit(finalConditions, criteriaBuilder, root, loginUser);
                    default:
                        return finalConditions;
                }
            }
        }
        return finalConditions;
    }

    //(待审核一级,待审核二级,待提交)的总和
    private Predicate toBeCheckAll(Predicate finalConditions, CriteriaBuilder criteriaBuilder, Root<PurchaseRequisitionDO> root, UserLoginCacheDTO loginUser) {
        //构造新条件
        Predicate preResult = criteriaBuilder.disjunction();
        //************待审核一级**********
        //请购单流程--2级
        RuleEngineByPredicateReq request = new RuleEngineByPredicateReq();
        request.setProcessId(PurchaseRequisitionProcessEnum.PURCHASE_REQUISITION_INNER_VERIFY_2.getProcessKey());
        request.setProcessStep(RequisitionOperationEnum.VALIDATE_GRADE_ONE.getCode());
        request.setMemberId(loginUser.getMemberId());
        request.setMemberRoleId(loginUser.getMemberRoleId());
        WrapperResp<List<RuleEngineConfigResp>> ruleEngineCondition = ruleEngineConfigFeign.getRuleEngineCondition(request);
        if (ResponseCodeEnum.SUCCESS.getCode() == ruleEngineCondition.getCode()) {
            if (ruleEngineCondition.getData() != null) {
                List<RuleEngineConfigResp> data = ruleEngineCondition.getData();
                if (!CollectionUtils.isEmpty(data)) {
                    Predicate predicate = JpaUtil.getRuleEngineConfigPredicate(criteriaBuilder, root, PurchaseRequisitionDO.class, data, loginUser.getUserRoleIds());
                    preResult = criteriaBuilder.or(preResult, predicate);
                }
            }
        } else {
            throw new BusinessException(ruleEngineCondition.getCode(), ruleEngineCondition.getMessage());
        }
        //请购单流程--3级
        request.setProcessId(PurchaseRequisitionProcessEnum.PURCHASE_REQUISITION_INNER_VERIFY.getProcessKey());
        ruleEngineCondition = ruleEngineConfigFeign.getRuleEngineCondition(request);
        if (ResponseCodeEnum.SUCCESS.getCode() == ruleEngineCondition.getCode()) {
            if (ruleEngineCondition.getData() != null) {
                List<RuleEngineConfigResp> data = ruleEngineCondition.getData();
                if (!CollectionUtils.isEmpty(data)) {
                    Predicate predicate = JpaUtil.getRuleEngineConfigPredicate(criteriaBuilder, root, PurchaseRequisitionDO.class, data, loginUser.getUserRoleIds());
                    preResult = criteriaBuilder.or(preResult, predicate);
                }
            }
        } else {
            throw new BusinessException(ruleEngineCondition.getCode(), ruleEngineCondition.getMessage());
        }
        //*********待审核二级*******
        //请购单流程--2级
        request = new RuleEngineByPredicateReq();
        request.setProcessId(PurchaseRequisitionProcessEnum.PURCHASE_REQUISITION_INNER_VERIFY_2.getProcessKey());
        request.setProcessStep(RequisitionOperationEnum.VALIDATE_GRADE_TWO.getCode());
        request.setMemberId(loginUser.getMemberId());
        request.setMemberRoleId(loginUser.getMemberRoleId());
        ruleEngineCondition = ruleEngineConfigFeign.getRuleEngineCondition(request);
        if (ResponseCodeEnum.SUCCESS.getCode() == ruleEngineCondition.getCode()) {
            if (ruleEngineCondition.getData() != null) {
                List<RuleEngineConfigResp> data = ruleEngineCondition.getData();
                if (!CollectionUtils.isEmpty(data)) {
                    Predicate predicate = JpaUtil.getRuleEngineConfigPredicate(criteriaBuilder, root, PurchaseRequisitionDO.class, data, loginUser.getUserRoleIds());
                    finalConditions = criteriaBuilder.and(finalConditions, predicate);
                }
            }
        } else {
            throw new BusinessException(ruleEngineCondition.getCode(), ruleEngineCondition.getMessage());
        }
        //*********待提交************
        //请购单流程--1级
        request = new RuleEngineByPredicateReq();
        request.setProcessId(PurchaseRequisitionProcessEnum.PURCHASE_REQUISITION_INNER_VERIFY_1.getProcessKey());
        request.setProcessStep(RequisitionOperationEnum.SUBMIT_REQUISITIONS.getCode());
        request.setMemberId(loginUser.getMemberId());
        request.setMemberRoleId(loginUser.getMemberRoleId());
        ruleEngineCondition = ruleEngineConfigFeign.getRuleEngineCondition(request);
        if (ResponseCodeEnum.SUCCESS.getCode() == ruleEngineCondition.getCode()) {
            if (ruleEngineCondition.getData() != null) {
                List<RuleEngineConfigResp> data = ruleEngineCondition.getData();
                if (!CollectionUtils.isEmpty(data)) {
                    Predicate predicate = JpaUtil.getRuleEngineConfigPredicate(criteriaBuilder, root, PurchaseRequisitionDO.class, data, loginUser.getUserRoleIds());
                    preResult = criteriaBuilder.or(preResult, predicate);
                }
            }
        } else {
            throw new BusinessException(ruleEngineCondition.getCode(), ruleEngineCondition.getMessage());
        }
        //请购单流程--2级
        request.setProcessId(PurchaseRequisitionProcessEnum.PURCHASE_REQUISITION_INNER_VERIFY_2.getProcessKey());
        ruleEngineCondition = ruleEngineConfigFeign.getRuleEngineCondition(request);
        if (ResponseCodeEnum.SUCCESS.getCode() == ruleEngineCondition.getCode()) {
            if (ruleEngineCondition.getData() != null) {
                List<RuleEngineConfigResp> data = ruleEngineCondition.getData();
                if (!CollectionUtils.isEmpty(data)) {
                    Predicate predicate = JpaUtil.getRuleEngineConfigPredicate(criteriaBuilder, root, PurchaseRequisitionDO.class, data, loginUser.getUserRoleIds());
                    preResult = criteriaBuilder.or(preResult, predicate);
                }
            }
        } else {
            throw new BusinessException(ruleEngineCondition.getCode(), ruleEngineCondition.getMessage());
        }
        //请购单流程--3级
        request.setProcessId(PurchaseRequisitionProcessEnum.PURCHASE_REQUISITION_INNER_VERIFY.getProcessKey());
        ruleEngineCondition = ruleEngineConfigFeign.getRuleEngineCondition(request);
        if (ResponseCodeEnum.SUCCESS.getCode() == ruleEngineCondition.getCode()) {
            if (ruleEngineCondition.getData() != null) {
                List<RuleEngineConfigResp> data = ruleEngineCondition.getData();
                if (!CollectionUtils.isEmpty(data)) {
                    Predicate predicate = JpaUtil.getRuleEngineConfigPredicate(criteriaBuilder, root, PurchaseRequisitionDO.class, data, loginUser.getUserRoleIds());
                    preResult = criteriaBuilder.or(preResult, predicate);
                }
            }
        } else {
            throw new BusinessException(ruleEngineCondition.getCode(), ruleEngineCondition.getMessage());
        }
        return preResult.getExpressions().size() > 1 ? criteriaBuilder.and(finalConditions, preResult) : finalConditions;
    }

    //待审核二级
    private Predicate toBeCheckTwo(Predicate finalConditions, CriteriaBuilder criteriaBuilder, Root<PurchaseRequisitionDO> root, UserLoginCacheDTO loginUser) {
        //请购单流程--2级
        RuleEngineByPredicateReq request = new RuleEngineByPredicateReq();
        request.setProcessId(PurchaseRequisitionProcessEnum.PURCHASE_REQUISITION_INNER_VERIFY_2.getProcessKey());
        request.setProcessStep(RequisitionOperationEnum.VALIDATE_GRADE_TWO.getCode());
        request.setMemberId(loginUser.getMemberId());
        request.setMemberRoleId(loginUser.getMemberRoleId());
        WrapperResp<List<RuleEngineConfigResp>> ruleEngineCondition = ruleEngineConfigFeign.getRuleEngineCondition(request);
        if (ResponseCodeEnum.SUCCESS.getCode() == ruleEngineCondition.getCode()) {
            if (ruleEngineCondition.getData() != null) {
                List<RuleEngineConfigResp> data = ruleEngineCondition.getData();
                if (!CollectionUtils.isEmpty(data)) {
                    // 引擎决策(判断是否跳过流程)
                    Predicate predicate = JpaUtil.getRuleEngineConfigPredicate(criteriaBuilder, root, PurchaseRequisitionDO.class, data, loginUser.getUserRoleIds());
                    finalConditions = criteriaBuilder.and(finalConditions, predicate);
                }
            }
        } else {
            throw new BusinessException(ruleEngineCondition.getCode(), ruleEngineCondition.getMessage());
        }
        return finalConditions;
    }

    //待审核一级
    private Predicate toBeCheckOne(Predicate finalConditions, CriteriaBuilder criteriaBuilder, Root<PurchaseRequisitionDO> root, UserLoginCacheDTO loginUser) {
        //构造新条件
        Predicate preResult = criteriaBuilder.disjunction();
        //请购单流程--2级
        RuleEngineByPredicateReq request = new RuleEngineByPredicateReq();
        request.setProcessId(PurchaseRequisitionProcessEnum.PURCHASE_REQUISITION_INNER_VERIFY_2.getProcessKey());
        request.setProcessStep(RequisitionOperationEnum.VALIDATE_GRADE_ONE.getCode());
        request.setMemberId(loginUser.getMemberId());
        request.setMemberRoleId(loginUser.getMemberRoleId());
        WrapperResp<List<RuleEngineConfigResp>> ruleEngineCondition = ruleEngineConfigFeign.getRuleEngineCondition(request);
        if (ResponseCodeEnum.SUCCESS.getCode() == ruleEngineCondition.getCode()) {
            if (ruleEngineCondition.getData() != null) {
                List<RuleEngineConfigResp> data = ruleEngineCondition.getData();
                if (!CollectionUtils.isEmpty(data)) {
                    // 引擎决策(判断是否跳过流程)
                    Predicate predicate = JpaUtil.getRuleEngineConfigPredicate(criteriaBuilder, root, PurchaseRequisitionDO.class, data, loginUser.getUserRoleIds());
                    preResult = criteriaBuilder.or(preResult, predicate);
                }
            }
        } else {
            throw new BusinessException(ruleEngineCondition.getCode(), ruleEngineCondition.getMessage());
        }
        //请购单流程--3级
        request.setProcessId(PurchaseRequisitionProcessEnum.PURCHASE_REQUISITION_INNER_VERIFY.getProcessKey());
        ruleEngineCondition = ruleEngineConfigFeign.getRuleEngineCondition(request);
        if (ResponseCodeEnum.SUCCESS.getCode() == ruleEngineCondition.getCode()) {
            if (ruleEngineCondition.getData() != null) {
                List<RuleEngineConfigResp> data = ruleEngineCondition.getData();
                if (!CollectionUtils.isEmpty(data)) {
                    Predicate predicate = JpaUtil.getRuleEngineConfigPredicate(criteriaBuilder, root, PurchaseRequisitionDO.class, data, loginUser.getUserRoleIds());
                    preResult = criteriaBuilder.or(preResult, predicate);
                }
            }
        } else {
            throw new BusinessException(ruleEngineCondition.getCode(), ruleEngineCondition.getMessage());
        }
        return preResult.getExpressions().size() > 1 ? criteriaBuilder.and(finalConditions, preResult) : finalConditions;

    }

    //待提交
    private Predicate toBeSubmit(Predicate finalConditions, CriteriaBuilder criteriaBuilder, Root<PurchaseRequisitionDO> root, UserLoginCacheDTO loginUser) {
        //构造新条件
        Predicate preResult = criteriaBuilder.disjunction();
        //请购单流程--1级
        RuleEngineByPredicateReq request = new RuleEngineByPredicateReq();
        request.setProcessId(PurchaseRequisitionProcessEnum.PURCHASE_REQUISITION_INNER_VERIFY_1.getProcessKey());
        request.setProcessStep(RequisitionOperationEnum.SUBMIT_REQUISITIONS.getCode());
        request.setMemberId(loginUser.getMemberId());
        request.setMemberRoleId(loginUser.getMemberRoleId());
        WrapperResp<List<RuleEngineConfigResp>> ruleEngineCondition = ruleEngineConfigFeign.getRuleEngineCondition(request);
        if (ResponseCodeEnum.SUCCESS.getCode() == ruleEngineCondition.getCode()) {
            if (ruleEngineCondition.getData() != null) {
                List<RuleEngineConfigResp> data = ruleEngineCondition.getData();
                if (!CollectionUtils.isEmpty(data)) {
                    // 引擎决策(判断是否跳过流程)
                    PolicyResultResp policy = this.policy(loginUser, PurchaseRequisitionProcessEnum.PURCHASE_REQUISITION_INNER_VERIFY_1.getProcessKey(), RequisitionInnerStatusEnum.BUYER_TO_SUBMIT.getCode());
                    Predicate predicate = JpaUtil.getRuleEngineConfigPredicate(criteriaBuilder, root, PurchaseRequisitionDO.class, data, loginUser.getUserRoleIds());
                    preResult = criteriaBuilder.or(preResult, predicate);
                }
            }
        } else {
            throw new BusinessException(ruleEngineCondition.getCode(), ruleEngineCondition.getMessage());
        }
        //请购单流程--2级
        request.setProcessId(PurchaseRequisitionProcessEnum.PURCHASE_REQUISITION_INNER_VERIFY_2.getProcessKey());
        ruleEngineCondition = ruleEngineConfigFeign.getRuleEngineCondition(request);
        if (ResponseCodeEnum.SUCCESS.getCode() == ruleEngineCondition.getCode()) {
            if (ruleEngineCondition.getData() != null) {
                List<RuleEngineConfigResp> data = ruleEngineCondition.getData();
                if (!CollectionUtils.isEmpty(data)) {
                    Predicate predicate = JpaUtil.getRuleEngineConfigPredicate(criteriaBuilder, root, PurchaseRequisitionDO.class, data, loginUser.getUserRoleIds());
                    preResult = criteriaBuilder.or(preResult, predicate);
                }
            }
        } else {
            throw new BusinessException(ruleEngineCondition.getCode(), ruleEngineCondition.getMessage());
        }
        //请购单流程--3级
        request.setProcessId(PurchaseRequisitionProcessEnum.PURCHASE_REQUISITION_INNER_VERIFY.getProcessKey());
        ruleEngineCondition = ruleEngineConfigFeign.getRuleEngineCondition(request);
        if (ResponseCodeEnum.SUCCESS.getCode() == ruleEngineCondition.getCode()) {
            if (ruleEngineCondition.getData() != null) {
                List<RuleEngineConfigResp> data = ruleEngineCondition.getData();
                if (!CollectionUtils.isEmpty(data)) {
                    Predicate predicate = JpaUtil.getRuleEngineConfigPredicate(criteriaBuilder, root, PurchaseRequisitionDO.class, data, loginUser.getUserRoleIds());
                    preResult = criteriaBuilder.or(preResult, predicate);
                }
            }
        } else {
            throw new BusinessException(ruleEngineCondition.getCode(), ruleEngineCondition.getMessage());
        }
        return preResult.getExpressions().size() > 1 ? criteriaBuilder.and(finalConditions, preResult) : finalConditions;
    }

    /**
     * 规则决策
     *
     * @param sysUser     登录用户
     * @param processKey  流程标识
     * @param processStep 流程步骤
     * @return PolicyResult
     */
    private PolicyResultResp policy(UserLoginCacheDTO sysUser, String processKey, Integer processStep) {
        PolicyReq request = new PolicyReq();
        request.setMemberId(sysUser.getMemberId());
        request.setMemberRoleId(sysUser.getMemberRoleId());
        request.setProcessId(processKey);
        request.setProcessStep(processStep);
        request.setUserRoleList(sysUser.getUserRoleIds());
        WrapperResp<PolicyResultResp> wrapperResp = ruleEngineConfigFeign.policy(request);
        if (WrapperUtil.isOk(wrapperResp.getCode())) {
            return wrapperResp.getData();
        }
        return new PolicyResultResp(Boolean.FALSE, 0, new ArrayList<>());
    }

    /**
     * SRM端-公共的请购单分页实现
     *
     * @param loginUser          登录用户信息
     * @param req                分页请求参数
     * @param buyerInnerStatus   内部状态集合
     * @param isTransferPurchase 是否为请购单转采购订单分页
     * @param isCheck            是否是审核列表
     * @return 查询结果
     */
    @Override
    public PageDataResp<RequisitionPageResp> getBaseRequisitionSRMPage(UserLoginCacheDTO loginUser, RequisitionPageSRMDataReq req, List<Integer> buyerInnerStatus, Boolean isTransferPurchase, Boolean isCheck) {
        Pageable page = PageRequest.of(req.getCurrent() - 1, req.getPageSize());
        Specification<PurchaseRequisitionDO> specification = (root, query, criteriaBuilder) -> {
            Predicate finalConditions = criteriaBuilder.conjunction();
            finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get("buyerMemberId").as(Long.class), loginUser.getMemberId()));
            finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get("buyerRoleId").as(Long.class), loginUser.getMemberRoleId()));

            if (StrUtil.isNotBlank(req.getDigest())) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.like(root.get("digest").as(String.class), "%" + req.getDigest() + "%"));
            }
            if (StrUtil.isNotBlank(req.getDigestOrAccount())) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.or(criteriaBuilder.like(root.get("digest").as(String.class), "%" + req.getDigestOrAccount() + "%"), criteriaBuilder.like(root.get("vendorMemberName").as(String.class), "%" + req.getDigestOrAccount() + "%")));
            }
            if (StrUtil.isNotBlank(req.getEndDate())) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.between(root.get("advanceDeliveryDate").as(LocalDateTime.class),DateTimeUtil.parseDateStart(req.getStartDate()) ,DateTimeUtil.parseDateEnd(req.getEndDate())));
            }
            if (req.getInnerStatus() != null) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get("buyerInnerStatus").as(Long.class), req.getInnerStatus()));
            }
            if (req.getInnerStatus() == null && CollUtil.isNotEmpty(buyerInnerStatus)) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.in(root.get("buyerInnerStatus")).value(buyerInnerStatus));
            }
            if (isTransferPurchase != null && isTransferPurchase) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get("buyerInnerStatus").as(Long.class), RequisitionInnerStatusEnum.BUYER__SUBMITTED.getCode()));
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.lt(root.get("transferQuantity").as(BigDecimal.class), root.get("quantity").as(BigDecimal.class)));
            }
            //调用规则引擎服务拼接查询条件
            //超级管理员不需要拼接
            if (!CommonBooleanEnum.YES.getCode().equals(loginUser.getUserType())) {
                finalConditions = addEnginePredicate(finalConditions, criteriaBuilder, root, req, buyerInnerStatus, loginUser, isCheck);
            }
            query.orderBy(criteriaBuilder.desc(root.get("id")));
            return finalConditions;
        };
        Page<PurchaseRequisitionDO> requisitionPage = purchaseRequisitionRepository.findAll(specification, page);
        return new PageDataResp<>(requisitionPage.getTotalElements(), requisitionPage.getContent().stream().map(RequisitionPageResp::new).collect(Collectors.toList()));
    }

    /**
     * 连续执行工作流
     *
     * @param loginUser   当前登录用户
     * @param requisition 请购单实体
     * @param agree       是否同意
     * @param policy      决策结果
     * @return 操作结果
     */
    @Override
    public PurchaseProcessTaskResp executeWorkSerialTasks(UserLoginCacheDTO loginUser, PurchaseRequisitionDO requisition, Integer agree, PolicyResultResp policy) {
        SerialTaskExecuteReq executeVO = new SerialTaskExecuteReq();
        executeVO.setProcessKey(requisition.getTask().getProcessKey());
        executeVO.setTaskId(requisition.getTask().getTaskId());
        executeVO.setMemberId(loginUser.getMemberId());
        executeVO.setRoleId(loginUser.getMemberRoleId());
        executeVO.setExecTimes(policy.getStepWidth());
        //根据跳转步骤填充条件列表
        List<Integer> agrees = new ArrayList<>();
        for (int i = 0; i < policy.getStepWidth(); i++) {
            agrees.add(1);
        }
        executeVO.setAgrees(agrees);
        executeVO.setDataId(requisition.getId());
        WrapperResp<ComplexTaskCompleteResp> result = taskExecuteControllerFeign.completeSerialTasks(executeVO);
        if (result.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            throw new BusinessException(result.getCode(),result.getMessage());
        }

        PurchaseProcessTaskResp taskResult = new PurchaseProcessTaskResp(result.getData());

        requisition.setBuyerInnerStatus(taskResult.getBuyerInnerStatus());
        requisition.setBuyerInnerStep(taskResult.getBuyerInnerStep());
        requisition.getTask().setTaskId(taskResult.getTaskId());
        return taskResult;
    }

    /**
     * 新增请购单合同数据
     *
     * @param requisition 请购单实体
     */
    @Override
    public void sendAsyncPurchaseRequisition(PurchaseRequisitionDO requisition) {
        List<PurchaseRequisitionAddReq> addVOList = new ArrayList<>();
        PurchaseRequisitionAddReq vo = new PurchaseRequisitionAddReq();
        BeanUtils.copyProperties(requisition, vo);
        vo.setRequisitionId(requisition.getId());
        vo.setPurchaseRequisitionProcessKey(requisition.getTask().getProcessKey());
        if (requisition.getAdvanceDeliveryDate() != null) {
            long timestamp = requisition.getAdvanceDeliveryDate().toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
            vo.setAdvanceDeliveryDate(String.valueOf(Instant.ofEpochMilli(timestamp).atZone(ZoneOffset.ofHours(8)).toLocalDate()));
        }
        Set<PurchaseRequisitionProductReq> products = new HashSet<>();
        Set<PurchaseRequisitionProductDO> purchaseProductSet = requisition.getProducts();
        if (!CollectionUtils.isEmpty(purchaseProductSet)) {
            products = purchaseProductSet.stream().map(p -> {
                PurchaseRequisitionProductReq productVO = new PurchaseRequisitionProductReq();
                BeanUtils.copyProperties(p, productVO);
                productVO.setRequisitionProductId(p.getId());
                return productVO;
            }).collect(Collectors.toSet());
        }
        vo.setProducts(products);
        addVOList.add(vo);
        WrapperUtil.throwWhenFail(purchaseRequisitionControllerFeign.addList(addVOList));
    }

    /**
     * 校验物料是否同一个引擎
     *
     * @param purchaseRequisition 请购单
     * @param memberId            会员ID
     * @param memberRoleId        会员角色ID
     * @param type                流程类型
     */
    @Override
    public void checkEngine(PurchaseRequisitionDO purchaseRequisition, Long memberId, Long memberRoleId, Integer type) {

        EngineRuleQueryReq query = new EngineRuleQueryReq(type, memberId, memberRoleId);
        WrapperResp<List<ProcessEngineRuleResp>> wrapperResp = processEngineRuleFeign.getEngineRuleList(query);
        if (WrapperUtil.isFail(wrapperResp.getCode())) {
            throw new BusinessException(wrapperResp.getCode(), wrapperResp.getMessage());
        }
        //获取全部物料的流程
        List<EngineResultDTO> engineResultDTOS = new ArrayList<>();
        Set<PurchaseRequisitionProductDO> products = new HashSet<>();

        Set<PurchaseRequisitionProductDO> productSet = purchaseRequisition.getProducts();
        if (CollectionUtils.isEmpty(products)) {
            return;
        }
        productSet.forEach(p -> {
            products.clear();
            products.add(p);
            purchaseRequisition.setProducts(products);
            EngineResultDTO engineResultDTO = new EngineRuleUtil<>(purchaseRequisition, wrapperResp.getData()).meta();
            Optional.ofNullable(engineResultDTO).map(EngineResultDTO::getProcessKey).orElseThrow(() -> new BusinessException(ResponseCodeEnum.PURCHASE_THE_REQUISITION_MATERIALS_PROCESS_ASSOCIATES_DOES_NOT_EXIST));
            engineResultDTOS.add(engineResultDTO);
        });
        //判断流程是否存在
        if (CollectionUtils.isEmpty(engineResultDTOS)) {
            throw new BusinessException(ResponseCodeEnum.PURCHASE_THE_REQUISITION_MATERIALS_PROCESS_ASSOCIATES_DOES_NOT_EXIST);
        }
        //判断全部流程是否一致
        if (engineResultDTOS.stream().map(EngineResultDTO::getProcessRuleId).distinct().count() != 1) {
            throw new BusinessException(ResponseCodeEnum.PURCHASE_THE_REQUISITION_MATERIALS_PROCESS_ASSOCIATES_DOES_NOT_UNANIMOUS);
        }
        //回补物料数据
        purchaseRequisition.getProducts().clear();
        purchaseRequisition.setProducts(productSet);
    }

    /**
     * 构造 MemberLifeCycleRuleCheckVO 对象
     */
    private MemberLifeCycleRuleCheckReq buildMemberLifeCycleRuleCheckVO(UserLoginCacheDTO loginUser, Integer lifeCycleStageRuleId) {
        MemberLifeCycleRuleCheckReq vo = new MemberLifeCycleRuleCheckReq();
        vo.setRoleId(loginUser.getMemberRoleId());
        vo.setMemberId(loginUser.getMemberId());
        vo.setLifeCycleStageRuleId(lifeCycleStageRuleId);
        return vo;
    }

    /**
     * 构造 MemberFeignWithLifeCycleRuleVO 对象
     */
    private MemberFeignWithLifeCycleRuleReq buildMemberFeignWithLifeCycleRuleVO(UserLoginCacheDTO loginUser, Integer lifeCycleStageRuleId) {
        return MemberFeignWithLifeCycleRuleReq.builder()
                .memberId(loginUser.getMemberId())
                .roleId(loginUser.getMemberRoleId())
                .roleTag(loginUser.getRoleTag())
                .lifeCycleStageRuleId(lifeCycleStageRuleId)
                .build();
    }
}