package com.ssy.lingxi.purchase.service.purchase.quote;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.ReportItemResp;
import com.ssy.lingxi.purchase.api.model.req.*;
import com.ssy.lingxi.purchase.api.model.req.purchase.*;
import com.ssy.lingxi.purchase.api.model.resp.BiddingExternalWorkStateEnumResp;
import com.ssy.lingxi.purchase.api.model.resp.purchase.*;
import com.ssy.lingxi.purchase.model.req.PurchaseReportReq;

import java.util.List;

/**
*采购报价业务处理
* <AUTHOR>
* @since 2021/1/26
*/
public interface IPurchaseQuoteService {

    /**
     *采购报价-采购需求单查询
     */
    PageDataResp<PurchaseInquiryListResp> purchaseInquiryList(PurchaseInquiryListReq request, UserLoginCacheDTO sysUser);

    PurchaseInquiryDetailsResp purchaseInquiryDetails(BaseDetailsReq request, UserLoginCacheDTO sysUser);


    /**
     * 采购询价-采购物料-分页查询
     */
    PageDataResp<PurchaseInquiryProductReq> purchaseInquiryProductlist(BaseDetailsReq request);

    /**
     *添加报价单
     */
    void quotedPriceAdd(QuotedPriceAddReq request, UserLoginCacheDTO sysUser);


    /**
     *修改报价单
     */
     void quotedPriceUpdate(QuotedPriceUpdateReq request, UserLoginCacheDTO sysUser);


    /**
     *删除报价单
     */
     void quotedPriceDelete(BaseReq request, UserLoginCacheDTO sysUser);

    /**
     *批量删除报价单
     */
    void quotedPriceDeleteBatch(BaseAllReq request, UserLoginCacheDTO sysUser);

    /**
     *采购报价单列表查询
     */
    PageDataResp<QuotedPriceListResp> quotedPriceList(QuotedPriceListReq request, UserLoginCacheDTO sysUser);

    QuotedPriceDetailsResp quotedPriceDetails(BaseDetailsReq request, UserLoginCacheDTO sysUser);


    /**
     待新增报价单
     */
     PageDataResp<QuotedPriceListResp> quotedPriceAddList(QuotedPriceListReq request, UserLoginCacheDTO sysUser);



    /**
     *采购询价-提交审核采购报价单
     */
    void quotedPriceExamine(BaseReq request, UserLoginCacheDTO sysUser);



    /**
     *采购询价-提交审核采购报价单批量
     */
    void quotedPriceExamineBatch(BaseAllReq request, UserLoginCacheDTO sysUser);



    /**
     *采购询价-待审核报价单(一级)
     */
    PageDataResp<QuotedPriceListResp> quotedPricestayExamineList1(QuotedPriceListReq request, UserLoginCacheDTO sysUser);


    /**
     *采购询价-提交审核采购报价单一级
     */
    void quotedPriceExamine1(BaseExamineReq request, UserLoginCacheDTO sysUser);





    /**
     *采购询价-提交审核采购报价单批量一级
     */
    void quotedPriceExamine1Batch(BaseAllReq request, UserLoginCacheDTO sysUser);



    /**
     *采购询价-待审核报价单(二级)
     */
    PageDataResp<QuotedPriceListResp> quotedPricestayExamineList2(QuotedPriceListReq request, UserLoginCacheDTO sysUser);


    /**
     *采购询价-提交审核采购报价单二级
     */
    void quotedPriceExamine2(BaseExamineReq request, UserLoginCacheDTO sysUser);





    /**
     *采购询价-提交审核采购报价单批量二级
     */
    void quotedPriceExamine2Batch(BaseAllReq request, UserLoginCacheDTO sysUser);


    /**
     *采购询价-待提交报价单
     */
    PageDataResp<QuotedPriceListResp> quotedPricestayStayCommitList(QuotedPriceListReq request, UserLoginCacheDTO sysUser);

    /**
     *采购询价-提交报价单
     */
    void quotedPriceStayCommit(BaseReq request, UserLoginCacheDTO sysUser);

    /**
     *采购询价-提交报价单批量
     */
    void quotedPriceStayCommitBatch(BaseAllReq request, UserLoginCacheDTO sysUser);

    /**
     * 采购合同-采购物料-分页查询
     */
    PageDataResp<QuotedMaterielResp> purchaseInquiryProductlistContract(ContractDetailsReq request);


    /**
     *平台后台报价列表查询
     */
    PageDataResp<QuotedPriceListResp> platformQuotedPriceList(QuotedPriceListReq request, UserLoginCacheDTO sysUser);



    /**
     *平台后台报价单详情查询
     */
    QuotedPriceDetailsResp platformQuotedPriceDetails(BaseDetailsReq request, UserLoginCacheDTO sysUser);


    /**
     * 报价单详情-物料详情列表查询-分页
     */
    PageDataResp<QuotedMaterielResp> materielDetailed(MaterielDetailedReq request, UserLoginCacheDTO sysUser);


    /**
     *报价详情
     */
    ConfirmQuotedPriceResp platformDetails(ConfirmQuotedPriceDetailsReq request, UserLoginCacheDTO sysUser);


    /**
     * 获取采购报价单内部流程状态
     */
     List<BiddingExternalWorkStateEnumResp> interiorStatus();

    /**
     * 询价首页统计
     * @param purchaseReportReq 请求参数
     * @return 返回结果
     */
    List<ReportItemResp> getPurchaseTallyReport(PurchaseReportReq purchaseReportReq);

    /**
     * 通过采购询价单号查询已授标的报价单列表
     * @param purchaseQuoteReq 参数
     */
    PageDataResp<PurchaseQuotedResp> getQuotedList(UserLoginCacheDTO sysUser, PurchaseQuoteReq purchaseQuoteReq);

    /**
     * 采购商--查询物料和商品信息
     * @param purchaseQuoteCommodityReq 参数
     */
    PageDataResp<PurchaseQuotedMaterielResp> getQuoteCommodityList(UserLoginCacheDTO sysUser, PurchaseQuoteCommodityReq purchaseQuoteCommodityReq);

    /**
     * 采购商--查询物料和商品信息
     * @param id 报价单id
     */
    List<QuotedMaterielResp> getQuoteDetails(Long id);
}



