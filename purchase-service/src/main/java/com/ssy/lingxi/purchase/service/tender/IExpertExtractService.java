package com.ssy.lingxi.purchase.service.tender;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.purchase.api.model.req.tender.ExpertExtractQueryReq;
import com.ssy.lingxi.purchase.entity.do_.tender.ExpertExtractDO;
import org.springframework.data.domain.Page;

/**
 * 评标专家库管理类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/22
 */
public interface IExpertExtractService {

    /**
     * 查询评标专家库列表
     * @param pageDataReq 分页实体
     * @param expertExtractQueryReq 评标专家库
     * @return
     */
    Page<ExpertExtractDO> getExpertExtractList(UserLoginCacheDTO sysUser, PageDataReq pageDataReq, ExpertExtractQueryReq expertExtractQueryReq);

    /**
     * 查询评标专家库
     * @param expertExtractId
     * @return Wrapper<?>
     */
    ExpertExtractDO getExpertExtract(UserLoginCacheDTO sysUser, Long expertExtractId);

    /**
     * 添加/修改评标专家库
     * @param expertExtract
     * @return
     * @throws Exception
     */
    String saveOrUpdateExpertExtract(UserLoginCacheDTO sysUser, ExpertExtractDO expertExtract);

    /**
     * 发送抽取通知
     * @param sysUser
     * @param commonIdReq
     * @return
     */
    Boolean sendExpertExtract(UserLoginCacheDTO sysUser, CommonIdReq commonIdReq);

    /**
     * 再次发送抽取通知
     * @param sysUser
     * @param commonIdReq
     * @return
     */
    Boolean sendExpertExtractAgain(UserLoginCacheDTO sysUser, CommonIdReq commonIdReq);

    /**
     * 删除
     * @param sysUser
     * @param commonIdReq
     * @return
     */
    Boolean deleteExpertExtract(UserLoginCacheDTO sysUser, CommonIdReq commonIdReq);
}
