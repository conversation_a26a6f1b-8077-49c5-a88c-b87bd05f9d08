package com.ssy.lingxi.purchase.entity.do_.tender;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ssy.lingxi.common.constant.TableNameConstant;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 招标评标要求附件表
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/3/1
 */
@Getter
@Setter
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_PURCHASE_SERVICE + "invite_tender_evaluation_tender_file")
public class InviteTenderEvaluationTenderFileDO implements Serializable {
    private static final long serialVersionUID = 646036123673059513L;
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 招标id
     */
    @JsonIgnore
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "inviteTenderId", referencedColumnName = "id")
    private InviteTenderDO inviteTender;

    /**
     * 文件名
     */
    @Column(columnDefinition = "varchar(100)", nullable = false)
    private String name;

    /**
     * 文件路径
     */
    @Column(columnDefinition = "varchar(500)", nullable = false)
    private String url;

}
