package com.ssy.lingxi.purchase.entity.do_.tender;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.ssy.lingxi.common.constant.TableNameConstant;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.List;

/**
 * 评标专家抽取
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/3/1
 */
@Getter
@Setter
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_PURCHASE_SERVICE + "evaluation_tender_expert_extract")
public class ExpertExtractDO implements Serializable {
    private static final long serialVersionUID = 6316615343293320067L;
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 名称
     */
    @Column(columnDefinition = "varchar(50)", nullable = false)
    private String name;

    /**
     * 招标id
     */
    @JsonIgnore
    @ManyToOne(cascade = {CascadeType.PERSIST})
    @JoinColumn(name = "inviteTenderId", referencedColumnName = "id")
    private InviteTenderDO inviteTender;

    /**
     * 评标专家抽取条件
     */
    @JsonManagedReference
    @OneToMany(mappedBy = "expertExtract", cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE})
    private List<ExpertExtractQueryDO> expertExtractQueryList;

    /**
     * 评标专家抽取记录
     */
    @JsonManagedReference
    @OneToMany(mappedBy = "expertExtract", cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE})
    private List<ExpertExtractRecordDO> expertExtractRecordList;

    /**
     * 状态: false-待发送; true-已发送;
     */
    @Column(columnDefinition = "boolean", nullable = false)
    private Boolean status = false;

    /**
     * 会员id
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long memberId;

    /**
     * 会员名称
     */
    @Column(columnDefinition = "varchar(50)", nullable = false)
    private String memberName;

    /**
     * 会员角色id
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long memberRoleId;

    /**
     * 会员角色名称
     */
    @Column(columnDefinition = "varchar(50)", nullable = false)
    private String memberRoleName;

    /**
     * 用户id
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long userId;

    /**
     * 用户名称
     */
    @Column(columnDefinition = "varchar(50)", nullable = false)
    private String userName;

    /**
     * 创建时间
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long createTime = System.currentTimeMillis();
}
