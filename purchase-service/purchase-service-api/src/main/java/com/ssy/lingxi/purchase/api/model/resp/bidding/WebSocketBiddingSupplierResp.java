package com.ssy.lingxi.purchase.api.model.resp.bidding;

import com.ssy.lingxi.purchase.api.model.resp.online.OfferLogsResp;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 发送websocker给前端
 */
@Data
public class WebSocketBiddingSupplierResp {

    /**
     *竞价单ID
     */
    private Long id;

    /**
     *报价台
     */
    private List<WebSocketAwardProcessResp> awardProcesss;

    // *************** 报价历史信息 ************** //

    /**
     *报价次数
     */
    private Long offerCount;

    /**
     *本次报价金额
     */
    private BigDecimal offerPrice;

    /**
     *对比上次报价比例
     */
    private BigDecimal offerRatio;

    /**
     *当前最低价
     */
    private BigDecimal minLowPrice = BigDecimal.ZERO;

    /**
     *当前报价排名
     */
    private Long ranking;

    /**
     *报价历史
     */
    private List<OfferLogsResp> offerLogs;
}
