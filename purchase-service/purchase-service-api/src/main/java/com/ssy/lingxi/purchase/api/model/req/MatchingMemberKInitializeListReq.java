package com.ssy.lingxi.purchase.api.model.req;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class MatchingMemberKInitializeListReq extends PageDataReq {

    /**
     * 品类id
     */
    private List<Long> categoryIds;

    /**
     * 区域
     */
    private List<AreaReq> areaRequestList;

    /**
     * 属性
     */
    private List<AttributeReq> attributeList;



}
