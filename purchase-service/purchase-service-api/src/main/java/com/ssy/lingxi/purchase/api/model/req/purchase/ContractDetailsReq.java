package com.ssy.lingxi.purchase.api.model.req.purchase;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
* 采购合同-采购报价物料详情查询
* <AUTHOR>
* @since 2021/1/20
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class ContractDetailsReq extends PageDataReq {
    private static final long serialVersionUID = -1983996873553764896L;

    /**
     *需求单ID
     */
    @NotNull(message = "需求单ID不能为空")
    private Long id;

    /**
     * 供应商会员ID
     */
    @NotNull(message = "会员ID不能为空")
    private Long memberId;

    /**
     * 供应商会员ID
     */
    @NotNull(message = "会员角色ID不能为空")
    private Long memberRoleId;

}
