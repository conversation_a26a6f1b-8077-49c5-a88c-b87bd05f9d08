package com.ssy.lingxi.purchase.api.model.resp;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.List;

/**
 *报价会员中标信息
 */
@Data
public class PriceParityDetailsResp {

    /**
     *报价单ID
     */
    private Long id;

    /**
     *报价会员ID
     */
    private Long memberId;

    /**
     *报价会员角色ID
     */
    private Long memberRoleId;

    /**
     *报价会员名字
     */
    private String memberName;



    /**
     * 报价小计(含税)
     */
    private BigDecimal subtotal;


    /**
     * 报价排名
     */
    private Integer ranking;


    /**
     * 最低价标数量
     */
    private Integer minimum;

    /**
     * 授标数量
     */
    private Integer awardCount;

    /**
     * 授标含税总额
     */
    private BigDecimal sumPrice;


    /**
     *物料授标结果信息
     */
    @Valid
    @NotEmpty(message ="物料授标结果信息不能为空")
    private List<AwardInfoResp> awardInfoResponses;
}
