package com.ssy.lingxi.purchase.api.enums.tender;

import com.ssy.lingxi.component.base.language.LanguageHolder;

import java.util.Arrays;
import java.util.Objects;

/**
 * 招标外部状态枚举类型
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/29
 */
public enum InviteTenderOutStatusEnum {

    SUBMITTED_INVITE_TENDER(1, "待提交招标"),
    /*Platform_Not_Check_Invite_Tender(2, "待平台审核招标"),22.1.20版本去掉
    Platform_Check_Not_Pass(3, "平台审核不通过"),*/
    NOT_INVITE_TENDER_REGISTER(4, "待招标报名"),
    NOT_CHECK_REGISTER_CHECK(5, "待审核报名"),
    NOT_SUBMITTED_QUALIFICATIONS_CHECK(6, "待提交资格预审"),
    NOT_QUALIFICATIONS_CHECK(7, "待资格预审"),
    NOT_SUBMIT_TENDER(8, "待投标"),
    NOT_OPEN_TENDER(9, "待开标"),
    NOT_SELECTION_TENDER(10, "待评标"),
    NOT_FINISH_NOTICE(11, "待定标"),
    NOT_WIN_NOTICE(12, "待中标公示"),
    FINISH_INVITE_TENDER(13, "完成招标"),
    DISCARD_TENDER(14, "已废标");

    private final Integer code;
    private final String message;

    InviteTenderOutStatusEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static String getMessage(Integer code){
        InviteTenderOutStatusEnum inviteTenderOutStatusEnum = Arrays.stream(InviteTenderOutStatusEnum.values()).filter(v -> v.getCode().equals(code)).findAny().orElse(null);
        return Objects.nonNull(inviteTenderOutStatusEnum) ? inviteTenderOutStatusEnum.getMessage() : null;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return LanguageHolder.getTranslation(this.getClass(), this.message, this.code);
    }
}
