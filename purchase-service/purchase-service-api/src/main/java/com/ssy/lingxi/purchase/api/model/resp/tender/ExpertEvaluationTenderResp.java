package com.ssy.lingxi.purchase.api.model.resp.tender;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 评标专家抽取记录
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/3/1
 */
@Getter
@Setter
public class ExpertEvaluationTenderResp implements Serializable {
    private static final long serialVersionUID = 4937229716319148203L;
    /**
     * 专家抽取记录id
     */
    private Long id;

    /**
     * 招标详情
     */
    private ExpertExtractRecordInviteTenderResp inviteTender;

    /**
     * 评分
     */
    private List<EvaluationTenderResp> evaluationTenderList;

    /**
     * 推荐
     */
    private List<EvaluationTenderRecommendResp> evaluationTenderRecommendList;
}
