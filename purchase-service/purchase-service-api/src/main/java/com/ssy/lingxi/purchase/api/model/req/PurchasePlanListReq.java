package com.ssy.lingxi.purchase.api.model.req;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 采购计划 - 列表搜索
 * <AUTHOR>
 * @since 2021/03/03
 * @version 2.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PurchasePlanListReq extends PageDataReq {
    private static final long serialVersionUID = 5956237855307869805L;

    /**
     * 采购计划编号
     */
    private String purchasePlanNo;

    /**
     * 采购计划摘要
     */
    private String summary;

    /**
     * 采购计划开始周期
     */
    private Long startTime;

    /**
     * 采购计划结束周期
     */
    private Long endTime;

    /**
     * 内部状态集合
     */
    private List<Integer> innerStatusList;
}
