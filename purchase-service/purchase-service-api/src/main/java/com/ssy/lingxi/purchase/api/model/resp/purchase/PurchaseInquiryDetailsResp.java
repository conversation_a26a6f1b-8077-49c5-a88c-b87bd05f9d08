package com.ssy.lingxi.purchase.api.model.resp.purchase;


import com.ssy.lingxi.purchase.api.model.req.EnclosureUrlsReq;
import com.ssy.lingxi.purchase.api.model.req.purchase.PurchaseInquiryProductReq;
import com.ssy.lingxi.purchase.api.model.resp.CountryAreaResp;
import com.ssy.lingxi.purchase.api.model.resp.LogStateResp;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
* 采购询价单
* <AUTHOR>
* @since 2021/1/20
*/
@Data
public class PurchaseInquiryDetailsResp {

    private Long id;

    /**
     *需求单号
     */
    private String purchaseInquiryNo;

    /**
     * 会员名字
     */
    private String memberName;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 会员角色ID
     */
    private Long memberRoleId;

    /**
     *需求摘要
     */
    private String details;

    /**
     *交付日期
     */
    private Long deliveryTime;

    /**
     *报价截止时间
     */
    private Long offerEndTime;

    /**
     *报价截止时间
     */
    private Long days;

    /**
     *报价截止时间
     */
    private Long hours;

    /**
     *报价截止时间
     */
    private Long minutes;

    /**
     *外部状态:0.已完成 1.待提交需求单 2.待审核需求单 3.待提交报价单 4.待确认授标结果 5.审核不通过需求单 6.发下轮报价
     */
    private Integer externalState;

    private String externalStateName;

    /**
     *内部状态:0.已完成 1.待提交审核采购需求单 2.待审核采购需求单(一级) 3.待审核采购需求单(二级) 4.待提交采购需求单 5.待比价 6.审核通过(一级)  7.审核通过(二级) 8.审核不通过(一级) 9.审核通过(二级)
     */
    private Integer interiorState;

    private String interiorStateName;

    /**
     * 外部工作流流程taskId
     */
    private String externalTaskId;

    /**
     * 内部工作流流程taskId
     */
    private String interiorTaskId;

    /**
     *适用地市：1.默认全部省市区 0.选中项
     */
    private Integer defaultAddress;

    /**
     *交付地址
     */
    private String address;

    /**
     *交付地址Id
     */
    private Long addressId;

    /**
     *报价要求
     */
    private String offer;

    /**
     *付款方式
     */
    private String paymentType;

    /**
     * 税费要求
     */
    private String taxes;

    /**
     *物流要求
     */
    private String logistics;

    /**
     *包装要求
     */
    private String packRequire;

    /**
     *需求单状态
     */
    private Integer state;

    /**
     *其他要求
     */
    private String otherRequire;

    /**
     *需求对接类型：1.发布至平台门户 2.系统匹配 3.指定供应会员
     */
    private Integer type;

    /**
     *发布时间
     */
    private Long createTime;

    /**
     *更新时间
     */
    private Long updateTime;

    /**
     * 创建人ID
     */
    private Long createMemberRoleId;

    /**
     * 创建会员名字
     */
    private String createMemberName;

    /**
     * 创建会员ID
     */
    private Long createMemberId;

    /**
     * 会员信用积分
     */
    private Integer integral;

    /**
     *采购类型:1.单词采购 2.协议采购
     */
    private Integer purchaseType;

    /**
     *比价方式:1.密封比价 2.非密封比价
     */
    private Integer priceContrast;

    /**
     *添加采购物料方式：1.选择货品生成 2.导入货品生成 3.通过平台属性生成
     */
    private  Integer materielMode;

    /**
     * 适用地市
     */
    private List<CountryAreaResp> areas;

    /**
     *是否中标: true 中标，false 不中标
     */
    private Boolean isPrize;

    /**
     *是否使用使用地市：true-是，false-否
     */
    private Boolean isAreas;

    /**
     * 采购询价单物料信息列表
     */
    private List<PurchaseInquiryProductReq> materiels;

    /**
     *采购需求单内部流程状态图
     */
    private  List<LogStateResp> interiorLogStates;

    /**
     *采购需求单内部操作记录
     */
    private  List<InteriorPurchaseInquiryLogResp> interiorLogs;

    /**
     *采购需求单外部流程状态图
     */
    private  List<LogStateResp> externalLogStates;

    /**
     *采购需求单外部操作记录
     */
    private  List<ExternalPurchaseInquiryLogResp> externalLogs;

    /**
     *采购需求单需求对接会员列表
     */
    private List<DemandMemberResp> demandMembers;

    /**
     * 发布需求 绑定的商城Id
     */
    private List<Long> shopIds;

    /**
     * 报价轮次
     */
    private Integer turn;

    /**
     * 总中标金额
     */
    private BigDecimal sumImMark;

    /**
     * 报价次数
     */
    private Integer count;

    /**
     *报价单ID
     */
    private Long quotedPriceId;

    /**
     * 附件
     */
    private List<EnclosureUrlsReq> transactionUurls;

    /**
     * 是否已报过价
     */
    private Integer isRegister = 0;

    /**
     * 是否能报价(采购商不能报价、供应商不能报价自己会员下的采购商发布的询价需求单)
     * */
    private Boolean canRegister = Boolean.FALSE;

    /**
     * 是否下级会员
     */
    private Boolean isSubMember = false;

    /**
     * 报价单轮次
     * */
    private List<QuotedPriceTurnResp> quotedPriceTurnList = new ArrayList<>();
}
