package com.ssy.lingxi.purchase.api.model.resp.tender;

import com.ssy.lingxi.purchase.api.model.req.tender.InviteTenderFileReq;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 投标会员
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/3/11
 */
@Getter
@Setter
public class InviteTenderMemberDetailResp implements Serializable {
    private static final long serialVersionUID = -6588541848478040629L;
    /**
     * 主键id
     */
    private Long id;

    /**
     * 投标外部状态: SubmitTenderOutStatusEnum.class
     */
    private String submitTenderOutStatusValue;

    /**
     * 投标内部状态: SubmitTenderInStatusEnum.class
     */
    private String submitTenderInStatusValue;

    /**
     * 投标报名
     */
    private SubmitTenderRegisterDetailResp submitTenderRegister;

    /**
     * 资格证明附件
     */
    private List<InviteTenderFileReq> qualificationsFile;

    /**
     * 投标信息
     */
    private SubmitTenderResp submitTender;

    /**
     * 投标编号
     */
    private String code;

    /**
     * 投标时间
     */
    private Long submitTenderTime;

    /**
     * 会员id
     */
    private Long memberId;

    /**
     * 会员名称
     */
    private String memberName;

    /**
     * 会员类型名称
     */
    private String memberTypeName;

    /**
     * 会员角色id
     */
    private Long memberRoleId;

    /**
     * 会员角色名称
     */
    private String memberRoleName;

    /**
     * 是否下属会员
     */
    private Boolean isSubMember;

    /**
     * 是否发送招标
     */
    private Boolean isSend;

    /**
     * 是否中标
     */
    private Boolean isWin;

    /**
     * 会员等级
     */
    private Integer level;

    /**
     * 会员等级名称
     */
    private String levelTag;
}
