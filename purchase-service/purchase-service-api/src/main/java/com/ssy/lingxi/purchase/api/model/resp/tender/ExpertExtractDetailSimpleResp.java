package com.ssy.lingxi.purchase.api.model.resp.tender;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 评标专家抽取
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/3/1
 */
@Getter
@Setter
public class ExpertExtractDetailSimpleResp implements Serializable {
    private static final long serialVersionUID = 2854102820744473784L;
    /**
     * 主键id
     */
    private Long id;

    /**
     * 招标信息
     */
    private ExpertExtractInviteTenderDetailResp inviteTender;

    /**
     * 状态: false-待发送; true-已发送;
     */
    private Boolean status;

    /**
     * 创建时间
     */
    private Long createTime;
}
