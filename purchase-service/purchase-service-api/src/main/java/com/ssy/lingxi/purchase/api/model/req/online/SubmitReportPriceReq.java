package com.ssy.lingxi.purchase.api.model.req.online;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 *提交报价请求实体
 */
@Data
public class SubmitReportPriceReq {

    /**
     *竞价单ID
     */
    @NotNull(message = "竞价单ID不能为空")
    private Long biddingId;

    /**
     *报价单ID
     */
    @NotNull(message = "报价单ID不能为空")
    private Long onlineId;

    /**
     * 采购竞价单物料信息列表
     */
    @Valid
    @NotEmpty(message = "物料信息列表不能为空")
    private List<OnlineProductReq> materiels;
}
