package com.ssy.lingxi.purchase.api.model.req;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 *解密报价单请求条件
 */
@Data
public class DecryptQuotedPriceReq {


    /**
     *采购需求单ID
     */
    @NotNull(message = "采购需求单ID不能为空")
    private Long id;


    /**
     *解密密码
     */
    @NotEmpty(message = "解密密码不能为空")
    private String password;
}
