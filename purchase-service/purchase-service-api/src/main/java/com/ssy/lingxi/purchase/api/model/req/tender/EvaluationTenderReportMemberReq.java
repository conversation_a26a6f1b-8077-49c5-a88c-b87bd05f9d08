package com.ssy.lingxi.purchase.api.model.req.tender;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 会员修正总分
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/3/1
 */
@Getter
@Setter
public class EvaluationTenderReportMemberReq implements Serializable {
    private static final long serialVersionUID = 5009310211197175584L;
    /**
     * 会员id
     */
    private Long memberId;

    /**
     * 修正总分
     */
    private BigDecimal correctScore;

    /**
     * 评分项
     */
    private List<EvaluationTenderOfflineReq> evaluationTenderOfflineList;
}
