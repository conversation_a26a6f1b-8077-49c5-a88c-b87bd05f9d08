package com.ssy.lingxi.purchase.api.enums.purchase;

import com.ssy.lingxi.component.base.language.LanguageHolder;

/**
* 采购报价单查询类型枚举类
* <AUTHOR>
* @since 2021/1/19
*/
public enum QuotedPriceQueryEnum {
    PURCHASE_INQUIRY_LIST(1, "采购报价单查询"),
    ADD_PURCHASE_INQUIRY_LIST(2, "待新增采购报价单查询"),
    EXAMINE_PURCHASE_INQUIRY_LIST1(3, "待审核采购报价单查询(一级)"),
    EXAMINE_PURCHASE_INQUIRY_LIST2(4, "待审核采购报价单查询(二级)"),
    SUBMIT_PURCHASE_INQUIRY_LIST(5, "待提交采购报价单查询"),
    CONFIRM_QUOTED_PRICE_LIST(6, "确认报价报价查询");

    private Integer type;
    private String name;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getName() {
        return LanguageHolder.getTranslation(this.getClass(), this.name, this.type);
    }

    public void setName(String name) {
        this.name = name;
    }

    QuotedPriceQueryEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }
}
