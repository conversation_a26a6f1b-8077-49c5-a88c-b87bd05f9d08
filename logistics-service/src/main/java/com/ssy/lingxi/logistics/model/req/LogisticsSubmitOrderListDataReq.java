package com.ssy.lingxi.logistics.model.req;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 物流单请求实体
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LogisticsSubmitOrderListDataReq extends PageDataReq implements Serializable {

    private static final long serialVersionUID = -6047578474358149894L;

    /**
     * 物流单号
     */
    private String logisticsOrderNo;

    /**
     * 对应订单号
     */
    private String relevanceOrderCode;

    /**
     * 物流服务商id
     */
    private Long companyId;

    /**
     * 单据时间开始
     */
    private Long invoicesTimeStart;

    /**
     * 单据时间结束
     */
    private Long invoicesTimeEnd;

    /**
     * 外部状态 1-待提交 2-待确认 3-不接受物流单 4-接受物流单
     */
    private Integer status;
}
