package com.ssy.lingxi.logistics.enums;

import com.ssy.lingxi.component.base.annotation.validator.IntArrayValuable;
import com.ssy.lingxi.component.base.language.LanguageHolder;

import java.util.Arrays;
import java.util.Objects;

/**
 * 物流单外部状态枚举
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/14
 */
public enum LogisticsOrderStatusEnum implements IntArrayValuable {

    /**
     * 待提交 - 1
     */
    WAIT_SUBMIT(1, "待提交"),

    /**
     * 待确认 - 2
     */
    WAIT_CONFIRMED(2, "待确认"),

    /**
     * 不接受物流单 - 3
     */
    NO_RECEIVE(3, "不接受物流单"),

    /**
     * 接受物流单 - 4
     */
    RECEIVE(4, "接受物流单");


    private final Integer code;
    private final String message;

    @Override
    public int[] array() {
        return Arrays.stream(values()).mapToInt(LogisticsOrderStatusEnum::getCode).toArray();
    }

    public static String getCodeMsg(Integer code) {
        LogisticsOrderStatusEnum logisticsOrderStatusEnum = Arrays.stream(LogisticsOrderStatusEnum.values()).filter(e -> e.getCode().equals(code)).findFirst().orElse(null);
        return Objects.nonNull(logisticsOrderStatusEnum) ? logisticsOrderStatusEnum.getMessage() : null;
    }

    LogisticsOrderStatusEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return LanguageHolder.getTranslation(this.getClass(), this.message, this.code);
    }

}
