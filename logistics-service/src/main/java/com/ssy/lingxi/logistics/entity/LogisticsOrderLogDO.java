package com.ssy.lingxi.logistics.entity;

import com.ssy.lingxi.common.constant.TableNameConstant;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 物流单历史流转记录实体
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/20
 */
@Data
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_LOGISTICS_SERVICE + "logistics_order_log",
        indexes = {@Index(name = TableNameConstant.TABLE_PRE_LOGISTICS_SERVICE + "logistics_order_log_order_id_idx", columnList = "orderId")})
public class LogisticsOrderLogDO implements Serializable {

    private static final long serialVersionUID = -4726804123622592757L;

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 物流单id
     */
    @Column
    private Long orderId;

    /**
     * 操作角色id
     */
    @Column
    private Long operatorRoleId;

    /**
     * 操作角色名称
     */
    @Column
    private String operatorRoleName;

    /**
     * 状态 1-待提交 2-待确认 3-接收物流单 4-不接收物流单
     */
    @Column
    private Integer status;

    /**
     * 状态名称
     */
    @Column
    private String statusName;

    /**
     * 操作方法
     */
    @Column
    private String operation;

    /**
     * 操作时间
     */
    @Column
    private Long operateTime;

    /**
     * 审核意见
     */
    @Column(columnDefinition = "varchar(120)")
    private String remark;

    /**
     * 数据关联会员id
     */
    @Column
    private Long memberId;

    /**
     * 数据关联角色id
     */
    @Column
    private Long roleId;
}
