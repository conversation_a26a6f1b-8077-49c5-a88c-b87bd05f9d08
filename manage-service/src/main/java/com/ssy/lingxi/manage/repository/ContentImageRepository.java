package com.ssy.lingxi.manage.repository;


import com.ssy.lingxi.manage.entity.content.ContentImageDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ContentImageRepository extends JpaRepository<ContentImageDO, Long>, JpaSpecificationExecutor<ContentImageDO> {

    List<ContentImageDO> findAllByUseSceneAndPositionAndStatus(Integer useScene, Integer position, Integer status);

    boolean existsBySortAndPosition(Integer sort, Integer position);

    boolean existsBySortAndPositionAndIdNot(Integer sort, Integer position, Long id);

    ContentImageDO findBySort(Integer sort);
}
