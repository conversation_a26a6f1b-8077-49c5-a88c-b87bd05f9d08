package com.ssy.lingxi.manage.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Component
@Configuration
@RefreshScope
public class YapiDeployConfig {

    //yapi访问信息
    @Value("${yapi.serverUrl}")
    private String serverUrl;

    @Value("${yapi.email}")
    private String email;

    @Value("${yapi.password}")
    private String password = "ssy123456";

    @Value("${yapi.groupId}")
    private String groupId;

    public String getServerUrl() {
        return serverUrl;
    }

    public void setServerUrl(String serverUrl) {
        this.serverUrl = serverUrl;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public List<String> getConfigList() {
        return Arrays.asList(serverUrl, email, password, groupId);
    }
}
