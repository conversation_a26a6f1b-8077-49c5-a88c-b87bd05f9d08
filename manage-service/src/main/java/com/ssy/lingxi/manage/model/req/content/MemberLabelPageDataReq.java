package com.ssy.lingxi.manage.model.req.content;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 标签管理 - 列表 - 请求实体类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/9/18
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MemberLabelPageDataReq extends PageDataReq {
    private static final long serialVersionUID = -2737790289010514765L;

    /**
     * 标签名称
     */
    private String name;

}
