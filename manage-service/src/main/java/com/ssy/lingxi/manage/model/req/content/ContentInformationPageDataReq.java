package com.ssy.lingxi.manage.model.req.content;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 资讯管理 - 列表 - 请求实体类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/9/18
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ContentInformationPageDataReq extends PageDataReq {
    private static final long serialVersionUID = -7382426841447417074L;

    /**
     * 标题
     */
    private String title;

    /**
     * 栏目分类：1.市场行情；2.资讯；
     */
    private Integer type;

    /**
     * 栏目Id
     */
    private Long columnId;

    /**
     * 三级分类ID
     */
    private Long thirdlyCategoryId;

    /**
     * 推荐标签 1-头条文章 2-轮播新闻 3-图片新闻 4-推荐阅读 5-行情推荐 6-本栏推荐
     */
    private Integer recommendLabel;

    /**
     * 状态 1-待上架 2-已上架 3-已下架
     */
    private Integer status;

    /**
     * 标签Id
     */
    private Long labelId;

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 结束时间
     */
    private Long endTime;
}
