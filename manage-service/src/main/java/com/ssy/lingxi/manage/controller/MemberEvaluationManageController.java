package com.ssy.lingxi.manage.controller;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.manage.service.IPlatformParameterService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 订单能力-会员评价管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-09-26
 */
@RestController
@RequestMapping(ServiceModuleConstant.MANAGE_PATH_PREFIX + "/activity/member/evaluation")
public class MemberEvaluationManageController extends BaseController {
    @Resource
    private IPlatformParameterService platformParameterManageService;


    /**
     * 获取商家审核评价内容平台参数
     * @return 参数值
     */
    @GetMapping("/find")
    public WrapperResp<Integer> findMemberEvaluation() {
        getSysUser();
        return WrapperUtil.success(platformParameterManageService.findMemberEvaluation());
    }
}
