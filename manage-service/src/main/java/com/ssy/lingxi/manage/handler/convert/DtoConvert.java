package com.ssy.lingxi.manage.handler.convert;

import com.ssy.lingxi.manage.model.req.content.MaterialLibraryDirTreeReq;
import com.ssy.lingxi.manage.model.req.content.MaterialLibraryJpaWhereDTO;
import com.ssy.lingxi.manage.model.req.content.MaterialLibraryPageReq;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * dto类转换
 *
 * <AUTHOR>
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DtoConvert {

    /**
     * 实例
     */
    DtoConvert INSTANCE = Mappers.getMapper(DtoConvert.class);

    /**
     * 类装换
     */
    MaterialLibraryJpaWhereDTO toMaterialLibraryJpaWhereDTO(MaterialLibraryPageReq req);

    /**
     * 类装换
     */
    MaterialLibraryJpaWhereDTO toMaterialLibraryJpaWhereDTO(MaterialLibraryDirTreeReq req);

}
