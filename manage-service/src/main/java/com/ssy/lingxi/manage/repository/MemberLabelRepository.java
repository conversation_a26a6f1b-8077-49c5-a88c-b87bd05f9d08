package com.ssy.lingxi.manage.repository;


import com.ssy.lingxi.manage.entity.content.MemberLabelDO;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MemberLabelRepository extends JpaRepository<MemberLabelDO, Long>, JpaSpecificationExecutor<MemberLabelDO> {

    List<MemberLabelDO> findAllByMemberIdAndMemberRoleIdAndStatus(Long memberId, Long memberRoleId, Integer status, Sort createTime);

    List<MemberLabelDO> findAllByMemberIdAndMemberRoleId(Long memberId, Long memberRoleId);
}
