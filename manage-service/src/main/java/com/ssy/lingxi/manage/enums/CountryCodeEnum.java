package com.ssy.lingxi.manage.enums;

import java.util.Arrays;

/**
 * 国家代码枚举
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-14
 */
public enum CountryCodeEnum {

    /**
     * 中国 - China
     */
    China(1, "中国", "China", "+86"),

    /**
     * 美国 - USA
     */
    USA(2, "美国", "USA", "+1"),

    /**
     * 阿富汗 - Afghanistan
     */
    Afghanistan(3, "阿富汗", "Afghanistan", "+93"),

    /**
     * 阿尔巴尼亚 - Albania
     */
    Albania(4, "阿尔巴尼亚", "Albania", "+355"),

    /**
     * 阿尔格拉 - Algera
     */
    Algera(5, "阿尔格拉", "Algera", "+213"),

    /**
     * 安道尔 - Andorra
     */
    Andorra(6, "安道尔", "Andorra", "+376"),

    /**
     * 安哥拉 - Angola
     */
    Angola(7, "安哥拉", "Angola", "+244"),

    /**
     * 安圭拉 - Anguilla
     */
    Anguilla(8, "安圭拉", "Anguilla", "+1264"),

    /**
     * 阿森松岛 - Ascension
     */
    Ascension(9, "阿森松岛", "Ascension", "+247"),

    /**
     * 安提瓜和巴布达 - Antigua and Barbuda
     */
    Antigua_and_Barbuda(10, "安提瓜和巴布达", "Antigua and Barbuda", "+1268"),

    /**
     * 阿根廷 - Argentina
     */
    Argentina(11, "阿根廷", "Argentina", "+54"),

    /**
     * 亚美尼亚 - Armenia
     */
    Armenia(12, "亚美尼亚", "Armenia", "+374"),

    /**
     * 阿鲁巴 - Aruba
     */
    Aruba(13, "阿鲁巴", "Aruba", "+297"),

    /**
     * 澳大利亚 - Australia
     */
    Australia(14, "澳大利亚", "Australia", "+61"),

    /**
     * 奥地利 - Austria
     */
    Austria(15, "奥地利", "Austria", "+43"),

    /**
     * 阿塞拜疆 - Azerbaijan
     */
    Azerbaijan(16, "阿塞拜疆", "Azerbaijan", "+994"),

    /**
     * 巴哈马 - Bahamas
     */
    Bahamas(17, "巴哈马", "Bahamas", "+1242"),

    /**
     * 巴林 - Bahrain
     */
    Bahrain(18, "巴林", "Bahrain", "+973"),

    /**
     * 孟加拉国 - Bangladesh
     */
    Bangladesh(19, "孟加拉国", "Bangladesh", "+880"),

    /**
     * 巴巴多斯 - Barbados
     */
    Barbados(20, "巴巴多斯", "Barbados", "+1246"),

    /**
     * 白俄罗斯 - Belarus
     */
    Belarus(21, "白俄罗斯", "Belarus", "+375"),

    /**
     * 比利时 - Belgium
     */
    Belgium(22, "比利时", "Belgium", "+32"),

    /**
     * 伯利兹 - Belize
     */
    Belize(23, "伯利兹", "Belize", "+501"),

    /**
     * 贝宁 - Benin
     */
    Benin(24, "贝宁", "Benin", "+229"),

    /**
     * 百慕大 - Bermuda
     */
    Bermuda(25, "百慕大", "Bermuda", "+1441"),

    /**
     * 不丹 - Bhutan
     */
    Bhutan(26, "不丹", "Bhutan", "+975"),

    /**
     * 玻利维亚 - Bolivia
     */
    Bolivia(27, "玻利维亚", "Bolivia", "+591"),

    /**
     * 波斯尼亚和黑塞哥维那 - Bosnia and Herzegovina
     */
    Bosnia_and_Herzegovina(28, "波斯尼亚和黑塞哥维那", "Bosnia and Herzegovina", "+387"),

    /**
     * 博茨瓦纳 - Botwana
     */
    Botwana(29, "博茨瓦纳", "Botwana", "+267"),

    /**
     * 巴西 - Brazill
     */
    Brazill(30, "巴西", "Brazill", "+55"),

    /**
     * 文莱 - Brunei
     */
    Brunei(31, "文莱", "Brunei", "+673"),

    /**
     * 保加利亚 - Bulgaria
     */
    Bulgaria(32, "保加利亚", "Bulgaria", "+359"),

    /**
     * 布基纳法索 - Burkina Faso
     */
    Burkina_Faso(33, "布基纳法索", "Burkina Faso", "+226"),

    /**
     * 布隆迪 - Burundi
     */
    Burundi(34, "布隆迪", "Burundi", "+257"),

    /**
     * 柬埔寨 - Cambodia
     */
    Cambodia(35, "柬埔寨", "Cambodia", "+855"),

    /**
     * 喀麦隆 - Cameroon
     */
    Cameroon(36, "喀麦隆", "Cameroon", "+237"),

    /**
     * 加拿大 - Canada
     */
    Canada(37, "加拿大", "Canada", "+1"),

    /**
     * 佛得角 - Cape Verde
     */
    Cape_Verde(38, "佛得角", "Cape Verde", "+238"),

    /**
     * 开曼群岛 - Cayman Islands
     */
    Cayman_Islands(39, "开曼群岛", "Cayman Islands", "+1345"),

    /**
     * 中非共和国 - Central African Republic
     */
    Central_African_Republic(40, "中非共和国", "Central African Republic", "+236"),

    /**
     * 乍得 - Chad
     */
    Chad(41, "乍得", "Chad", "+235"),

    /**
     * 智利 - Chile
     */
    Chile(42, "智利", "Chile", "+56"),

    /**
     * 哥伦比亚 - Colombia
     */
    Colombia(43, "哥伦比亚", "Colombia", "+57"),

    /**
     * 科摩罗 - Comoros
     */
    Comoros(44, "科摩罗", "Comoros", "+269"),

    /**
     * 刚果共和国 - Republic of the Congo
     */
    Republic_of_the_Congo(45, "刚果共和国", "Republic of the Congo", "+242"),

    /**
     * 刚果民主共和国 - Democratic Republic of the Congo
     */
    Democratic_Republic_of_the_Congo(46, "刚果民主共和国", "Democratic Republic of the Congo", "+243"),

    /**
     * 库克群岛 - Cook Islands
     */
    Cook_Islands(47, "库克群岛", "Cook Islands", "+682"),

    /**
     * 哥斯达黎加 - Costa Rica
     */
    Costa_Rica(48, "哥斯达黎加", "Costa Rica", "+506"),

    /**
     * 科特迪沃 - Cote divoire
     */
    Cote_divoire(49, "科特迪沃", "Cote divoire", "+225"),

    /**
     * 克罗地亚 - Croatia
     */
    Croatia(50, "克罗地亚", "Croatia", "+385"),

    /**
     * 古巴 - Cuba
     */
    Cuba(51, "古巴", "Cuba", "+53"),

    /**
     * 塞浦路斯 - Cyprus
     */
    Cyprus(52, "塞浦路斯", "Cyprus", "+357"),

    /**
     * 捷克共和国 - Czech Republic
     */
    Czech_Republic(53, "捷克共和国", "Czech Republic", "+420"),

    /**
     * 丹麦 - Denmark
     */
    Denmark(54, "丹麦", "Denmark", "+45"),

    /**
     * 吉布提 - Djibouti
     */
    Djibouti(55, "吉布提", "Djibouti", "+253"),

    /**
     * 多米尼加 - Dominica
     */
    Dominica(56, "多米尼加", "Dominica", "+1767"),

    /**
     * 多米尼加共和国 - Dominican Republic
     */
    Dominican_Republic(57, "多米尼加共和国", "Dominican Republic", "+1809"),

    /**
     * 厄瓜多尔 - Ecuador
     */
    Ecuador(58, "厄瓜多尔", "Ecuador", "+593"),

    /**
     * 埃及 - Egypt
     */
    Egypt(59, "埃及", "Egypt", "+20"),

    /**
     * 艾萨尔瓦多 - EISalvador
     */
    EISalvador(60, "艾萨尔瓦多", "EISalvador", "+503"),

    /**
     * 爱沙尼亚 - Estonia
     */
    Estonia(61, "爱沙尼亚", "Estonia", "+372"),

    /**
     * 埃塞俄比亚 - Ethiopia
     */
    Ethiopia(62, "埃塞俄比亚", "Ethiopia", "+251"),

    /**
     * 法罗群岛 - Faroe Islands
     */
    Faroe_Islands(63, "法罗群岛", "Faroe Islands", "+298"),

    /**
     * 斐济 - Fiji
     */
    Fiji(64, "斐济", "Fiji", "+679"),

    /**
     * 芬兰 - Finland
     */
    Finland(65, "芬兰", "Finland", "+358"),

    /**
     * 法国 - France
     */
    France(66, "法国", "France", "+33"),

    /**
     * 法属圭亚那 - French Guiana
     */
    French_Guiana(67, "法属圭亚那", "French Guiana", "+594"),

    /**
     * 法属波利尼西亚 - French Polynesia
     */
    French_Polynesia(68, "法属波利尼西亚", "French Polynesia", "+689"),

    /**
     * 加蓬 - Gabon
     */
    Gabon(69, "加蓬", "Gabon", "+241"),

    /**
     * 冈比亚 - Gambia
     */
    Gambia(70, "冈比亚", "Gambia", "+220"),

    /**
     * 格鲁吉亚 - Georgia
     */
    Georgia(71, "格鲁吉亚", "Georgia", "+995"),

    /**
     * 德国 - Germany
     */
    Germany(72, "德国", "Germany", "+94"),

    /**
     * 加纳 - Ghana
     */
    Ghana(73, "加纳", "Ghana", "+233"),

    /**
     * 直布罗陀 - Gibraltar
     */
    Gibraltar(74, "直布罗陀", "Gibraltar", "+350"),

    /**
     * 希腊 - Greece
     */
    Greece(75, "希腊", "Greece", "+30"),

    /**
     * 格陵兰 - Greenland
     */
    Greenland(76, "格陵兰", "Greenland", "+299"),

    /**
     * 格林纳达 - Grenada
     */
    Grenada(77, "格林纳达", "Grenada", "+1473"),

    /**
     * 瓜德罗普 - Guadeloupe
     */
    Guadeloupe(78, "瓜德罗普", "Guadeloupe", "+590"),

    /**
     * 关岛 - Guam
     */
    Guam(79, "关岛", "Guam", "+1671"),

    /**
     * 危地马拉 - Guatemala
     */
    Guatemala(80, "危地马拉", "Guatemala", "+502"),

    /**
     * 几内亚 - Guinea
     */
    Guinea(81, "几内亚", "Guinea", "+240"),

    /**
     * 根西 - Guernsey
     */
    Guernsey(82, "根西", "Guernsey", "+44"),

    /**
     * 圭亚那 - Guyana
     */
    Guyana(83, "圭亚那", "Guyana", "+592"),

    /**
     * 海地 - Haiti
     */
    Haiti(84, "海地", "Haiti", "+509"),

    /**
     * 洪都拉斯 - Honduras
     */
    Honduras(85, "洪都拉斯", "Honduras", "+504"),

    /**
     * 香港 - Hong Kong
     */
    Hong_Kong(86, "香港", "Hong Kong", "+852"),

    /**
     * 缅甸 - Myanmar
     */
    Myanmar(87, "缅甸", "Myanmar", "+95"),

    /**
     * 匈牙利 - Hungary
     */
    Hungary(88, "匈牙利", "Hungary", "+36"),

    /**
     * 冰岛 - Iceland
     */
    Iceland(89, "冰岛", "Iceland", "+354"),

    /**
     * 印度 - Indea
     */
    Indea(90, "印度", "Indea", "+91"),

    /**
     * 印度尼西亚 - Indonesia
     */
    Indonesia(91, "印度尼西亚", "Indonesia", "+62"),

    /**
     * 伊朗 - Iran
     */
    Iran(92, "伊朗", "Iran", "+98"),

    /**
     * 伊拉克 - Iraq
     */
    Iraq(93, "伊拉克", "Iraq", "+964"),

    /**
     * 爱尔兰 - Ireland
     */
    Ireland(94, "爱尔兰", "Ireland", "+353"),

    /**
     * 马恩岛 - Isle of Man
     */
    Isle_of_Man(95, "马恩岛", "Isle of Man", "+44"),

    /**
     * 以色列 - Israel
     */
    Israel(96, "以色列", "Israel", "+972"),

    /**
     * 意大利 - Italy
     */
    Italy(97, "意大利", "Italy", "+93"),

    /**
     * 牙买加 - Jamaica
     */
    Jamaica(98, "牙买加", "Jamaica", "+1876"),

    /**
     * 日本 - Japan
     */
    Japan(99, "日本", "Japan", "+81"),

    /**
     * 泽西岛 - Jersey
     */
    Jersey(100, "泽西岛", "Jersey", "+44"),

    /**
     * 约旦 - Jordan
     */
    Jordan(101, "约旦", "Jordan", "+962"),

    /**
     * 哈萨克斯坦 - Kazeakhstan
     */
    Kazeakhstan(102, "哈萨克斯坦", "Kazeakhstan", "+7"),

    /**
     * 肯尼亚 - Kenya
     */
    Kenya(103, "肯尼亚", "Kenya", "+254"),

    /**
     * 科索沃 - Kosovo
     */
    Kosovo(104, "科索沃", "Kosovo", "+383"),

    /**
     * 科威特 - Kuwait
     */
    Kuwait(105, "科威特", "Kuwait", "+965"),

    /**
     * 吉尔吉斯斯坦 - Kyrgyzstan
     */
    Kyrgyzstan(106, "吉尔吉斯斯坦", "Kyrgyzstan", "+996"),

    /**
     * 老挝 - Laos
     */
    Laos(107, "老挝", "Laos", "+856"),

    /**
     * 拉脱维亚 - Latvia
     */
    Latvia(108, "拉脱维亚", "Latvia", "+371"),

    /**
     * 黎巴嫩 - Lebanon
     */
    Lebanon(109, "黎巴嫩", "Lebanon", "+961"),

    /**
     * 莱索托 - Lesotho
     */
    Lesotho(110, "莱索托", "Lesotho", "+266"),

    /**
     * 利比里亚 - Liberia
     */
    Liberia(111, "利比里亚", "Liberia", "+231"),

    /**
     * 利比亚 - Libya
     */
    Libya(112, "利比亚", "Libya", "+218"),

    /**
     * 列支敦士登 - Liechtenstein
     */
    Liechtenstein(113, "列支敦士登", "Liechtenstein", "+423"),

    /**
     * 立陶宛 - Lithuania
     */
    Lithuania(114, "立陶宛", "Lithuania", "+370"),

    /**
     * 卢森堡 - Luxembourg
     */
    Luxembourg(115, "卢森堡", "Luxembourg", "+352"),

    /**
     * 澳门 - Macao
     */
    Macao(116, "澳门", "Macao", "+853"),

    /**
     * 马其顿 - Macedonia
     */
    Macedonia(117, "马其顿", "Macedonia", "+389"),

    /**
     * 马达加斯加 - Madagascar
     */
    Madagascar(118, "马达加斯加", "Madagascar", "+261"),

    /**
     * 马拉维 - Malawi
     */
    Malawi(119, "马拉维", "Malawi", "+265"),

    /**
     * 马来西亚 - Malaysia
     */
    Malaysia(120, "马来西亚", "Malaysia", "+60"),

    /**
     * 马尔代夫 - Maldives
     */
    Maldives(121, "马尔代夫", "Maldives", "+960"),

    /**
     * 马里 - Mali
     */
    Mali(122, "马里", "Mali", "+223"),

    /**
     * 马耳他 - Malta
     */
    Malta(123, "马耳他", "Malta", "+356"),

    /**
     * 马提尼克 - Martinique
     */
    Martinique(124, "马提尼克", "Martinique", "+596"),

    /**
     * 毛里塔尼亚 - Mauritania
     */
    Mauritania(125, "毛里塔尼亚", "Mauritania", "+222"),

    /**
     * 毛里求斯 - Mauritius
     */
    Mauritius(126, "毛里求斯", "Mauritius", "+230"),

    /**
     * 马约特 - Mayotte
     */
    Mayotte(127, "马约特", "Mayotte", "+262"),

    /**
     * 墨西哥 - Mexico
     */
    Mexico(128, "墨西哥", "Mexico", "+52"),

    /**
     * 摩尔多瓦 - Moldova
     */
    Moldova(129, "摩尔多瓦", "Moldova", "+373"),

    /**
     * 摩纳哥 - Monaco
     */
    Monaco(130, "摩纳哥", "Monaco", "+377"),

    /**
     * 蒙古 - Mongolia
     */
    Mongolia(131, "蒙古", "Mongolia", "+976"),

    /**
     * 黑山 - Montenegro
     */
    Montenegro(132, "黑山", "Montenegro", "+382"),

    /**
     * 蒙特塞拉特 - Montserrat
     */
    Montserrat(133, "蒙特塞拉特", "Montserrat", "+1664"),

    /**
     * 摩洛哥 - Morocco
     */
    Morocco(134, "摩洛哥", "Morocco", "+212"),

    /**
     * 莫桑比克 - Mozambique
     */
    Mozambique(135, "莫桑比克", "Mozambique", "+258"),

    /**
     * 纳米比亚 - Namibia
     */
    Namibia(136, "纳米比亚", "Namibia", "+264"),

    /**
     * 尼泊尔 - Nepal
     */
    Nepal(137, "尼泊尔", "Nepal", "+977"),

    /**
     * 荷兰 - Netherlands
     */
    Netherlands(138, "荷兰", "Netherlands", "+31"),

    /**
     * 荷属安的列斯 - Netherlands Antillse
     */
    Netherlands_Antillse(139, "荷属安的列斯", "Netherlands Antillse", "+599"),

    /**
     * 新喀里多尼亚 - New Caledonia
     */
    New_Caledonia(140, "新喀里多尼亚", "New Caledonia", "+687"),

    /**
     * 新西兰 - NewZealand
     */
    NewZealand(141, "新西兰", "NewZealand", "+64"),

    /**
     * 尼加拉瓜 - Nicaragua
     */
    Nicaragua(142, "尼加拉瓜", "Nicaragua", "+505"),

    /**
     * 尼日尔 - Niger
     */
    Niger(143, "尼日尔", "Niger", "+227"),

    /**
     * 尼日利亚 - Nigeria
     */
    Nigeria(144, "尼日利亚", "Nigeria", "+234"),

    /**
     * 挪威 - Norway
     */
    Norway(145, "挪威", "Norway", "+47"),

    /**
     * 阿曼 - Oman
     */
    Oman(146, "阿曼", "Oman", "+968"),

    /**
     * 巴基斯坦 - Pakistan
     */
    Pakistan(147, "巴基斯坦", "Pakistan", "+92"),

    /**
     * 巴勒斯坦 - Palestinian
     */
    Palestinian(148, "巴勒斯坦", "Palestinian", "+970"),

    /**
     * 巴拿马 - Panama
     */
    Panama(149, "巴拿马", "Panama", "+507"),

    /**
     * 巴布亚新几内亚 - Papua New Guinea
     */
    Papua_New_Guinea(150, "巴布亚新几内亚", "Papua New Guinea", "+675"),

    /**
     * 巴拉圭 - Paraguay
     */
    Paraguay(151, "巴拉圭", "Paraguay", "+595"),

    /**
     * 秘鲁 - Peru
     */
    Peru(152, "秘鲁", "Peru", "+51"),

    /**
     * 菲律宾 - Philippines
     */
    Philippines(153, "菲律宾", "Philippines", "+63"),

    /**
     * 波兰 - Poland
     */
    Poland(154, "波兰", "Poland", "+48"),

    /**
     * 葡萄牙 - Portugal
     */
    Portugal(155, "葡萄牙", "Portugal", "+351"),

    /**
     * 波多黎各 - PuertoRico
     */
    PuertoRico(156, "波多黎各", "PuertoRico", "+1"),

    /**
     * 库塔 - Qotar
     */
    Qotar(157, "库塔", "Qotar", "+974"),

    /**
     * 留尼汪 - Reunion
     */
    Reunion(158, "留尼汪", "Reunion", "+262"),

    /**
     * 罗马尼亚 - Romania
     */
    Romania(159, "罗马尼亚", "Romania", "+40"),

    /**
     * 俄罗斯 - Russia
     */
    Russia(160, "俄罗斯", "Russia", "+7"),

    /**
     * 卢旺达 - Rwanda
     */
    Rwanda(161, "卢旺达", "Rwanda", "+250"),

    /**
     * 萨摩亚东部 - Samoa Eastern
     */
    Samoa_Eastern(162, "萨摩亚东部", "Samoa Eastern", "+684"),

    /**
     * 萨摩亚西部 - Samoa Western
     */
    Samoa_Western(163, "萨摩亚西部", "Samoa Western", "+685"),

    /**
     * 圣马力诺 - San Marino
     */
    San_Marino(164, "圣马力诺", "San Marino", "+378"),

    /**
     * 圣多美和普林西比 - Sao Tome and Principe
     */
    Sao_Tome_and_Principe(165, "圣多美和普林西比", "Sao Tome and Principe", "+239"),

    /**
     * 沙特阿拉伯 - Saudi Arabia
     */
    Saudi_Arabia(166, "沙特阿拉伯", "Saudi Arabia", "+966"),

    /**
     * 塞内加尔 - Senegal
     */
    Senegal(167, "塞内加尔", "Senegal", "+221"),

    /**
     * 塞尔维亚 - Serbia
     */
    Serbia(168, "塞尔维亚", "Serbia", "+381"),

    /**
     * 塞舌尔 - Seychelles
     */
    Seychelles(169, "塞舌尔", "Seychelles", "+248"),

    /**
     * 塞拉利昂 - Sierra Leone
     */
    Sierra_Leone(170, "塞拉利昂", "Sierra Leone", "+232"),

    /**
     * 新加坡 - Singapore
     */
    Singapore(171, "新加坡", "Singapore", "+65"),

    /**
     * 斯洛伐克 - Slovakia
     */
    Slovakia(172, "斯洛伐克", "Slovakia", "+421"),

    /**
     * 斯洛文尼亚 - Slovenia
     */
    Slovenia(173, "斯洛文尼亚", "Slovenia", "+386"),

    /**
     * 南非 - South Africa
     */
    South_Africa(174, "南非", "South Africa", "+27"),

    /**
     * 韩国 - Korea
     */
    Korea(175, "韩国", "Korea", "+82"),

    /**
     * 西班牙 - Spain
     */
    Spain(176, "西班牙", "Spain", "+34"),

    /**
     * 斯里兰卡 - SriLanka
     */
    SriLanka(177, "斯里兰卡", "SriLanka", "+94"),

    /**
     * 圣基茨和尼维斯 - St Kitts and Nevis
     */
    St_Kitts_and_Nevis(178, "圣基茨和尼维斯", "St Kitts and Nevis", "+1869"),

    /**
     * 圣卢西亚 - St.Lucia
     */
    St_Lucia(179, "圣卢西亚", "St.Lucia", "+1758"),

    /**
     * 圣文森特 - St.Vincent
     */
    St_Vincent(180, "圣文森特", "St.Vincent", "+1784"),

    /**
     * 苏丹 - Sudan
     */
    Sudan(181, "苏丹", "Sudan", "+249"),

    /**
     * 苏里南 - Suriname
     */
    Suriname(182, "苏里南", "Suriname", "+597"),

    /**
     * 斯威士兰 - Swaziland
     */
    Swaziland(183, "斯威士兰", "Swaziland", "+268"),

    /**
     * 瑞典 - Sweden
     */
    Sweden(184, "瑞典", "Sweden", "+46"),

    /**
     * 瑞士 - Switzerland
     */
    Switzerland(185, "瑞士", "Switzerland", "+41"),

    /**
     * 叙利亚 - Syria
     */
    Syria(186, "叙利亚", "Syria", "+963"),

    /**
     * 台湾 - Taiwan
     */
    Taiwan(187, "台湾", "Taiwan", "+886"),

    /**
     * 塔吉克斯坦 - Tajikistan
     */
    Tajikistan(188, "塔吉克斯坦", "Tajikistan", "+992"),

    /**
     * 坦桑尼亚 - Tanzania
     */
    Tanzania(189, "坦桑尼亚", "Tanzania", "+255"),

    /**
     * 泰国 - Thailand
     */
    Thailand(190, "泰国", "Thailand", "+66"),

    /**
     * 东帝汶 - Timor Leste
     */
    Timor_Leste(191, "东帝汶", "Timor Leste", "+670"),

    /**
     * 多哥 - Togo
     */
    Togo(192, "多哥", "Togo", "+228"),

    /**
     * 汤加 - Tonga
     */
    Tonga(193, "汤加", "Tonga", "+676"),

    /**
     * 特立尼达和多巴哥 - Trinidad and Tobago
     */
    Trinidad_and_Tobago(194, "特立尼达和多巴哥", "Trinidad and Tobago", "+1868"),

    /**
     * 突尼斯 - Tunisia
     */
    Tunisia(195, "突尼斯", "Tunisia", "+216"),

    /**
     * 土耳其 - Turkey
     */
    Turkey(196, "土耳其", "Turkey", "+90"),

    /**
     * 土库曼斯坦 - Turkmenistan
     */
    Turkmenistan(197, "土库曼斯坦", "Turkmenistan", "+993"),

    /**
     * 特克斯和凯科斯群岛 - Turks and Caicos Islands
     */
    Turks_and_Caicos_Islands(198, "特克斯和凯科斯群岛", "Turks and Caicos Islands", "+1649"),

    /**
     * 乌干达 - Uganda
     */
    Uganda(199, "乌干达", "Uganda", "+256"),

    /**
     * 乌克兰 - Ukraine
     */
    Ukraine(200, "乌克兰", "Ukraine", "+380"),

    /**
     * 阿拉伯联合酋长国 - United Arab Emirates
     */
    United_Arab_Emirates(201, "阿拉伯联合酋长国", "United Arab Emirates", "+971"),

    /**
     * 英国 - United Kingdom
     */
    United_Kingdom(202, "英国", "United Kingdom", "+44"),

    /**
     * 乌拉圭 - Uruguay
     */
    Uruguay(203, "乌拉圭", "Uruguay", "+598"),

    /**
     * 乌兹别克斯坦 - Uzbekistan
     */
    Uzbekistan(204, "乌兹别克斯坦", "Uzbekistan", "+998"),

    /**
     * 瓦努阿图 - Vanuatu
     */
    Vanuatu(205, "瓦努阿图", "Vanuatu", "+678"),

    /**
     * 委内瑞拉 - Venezuela
     */
    Venezuela(206, "委内瑞拉", "Venezuela", "+58"),

    /**
     * 越南 - Vietnam
     */
    Vietnam(207, "越南", "Vietnam", "+84"),

    /**
     * 维尔京群岛 - Virgin Islands
     */
    Virgin_Islands(208, "维尔京群岛", "Virgin Islands", "+1340"),

    /**
     * 也门 - Yemen
     */
    Yemen(209, "也门", "Yemen", "+967"),

    /**
     * 赞比亚 - Zambia
     */
    Zambia(210, "赞比亚", "Zambia", "+260"),

    /**
     * 津巴布韦 - Zimbabwe
     */
    Zimbabwe(211, "津巴布韦", "Zimbabwe", "+263");

    /**
     * 枚举值
     */
    private Integer typeEnum;

    /**
     * 国家中文名称
     */
    private String cn;

    /**
     * 国家英文名称
     */
    private String en;

    /**
     * 国家电话号码前缀
     */
    private String code;

    CountryCodeEnum(Integer typeEnum, String cn, String en, String code) {
        this.typeEnum = typeEnum;
        this.cn = cn;
        this.en = en;
        this.code = code;
    }

    public Integer getTypeEnum() {
        return typeEnum;
    }

    public void setTypeEnum(Integer typeEnum) {
        this.typeEnum = typeEnum;
    }

    public String getCn() {
        return cn;
    }

    public void setCn(String cn) {
        this.cn = cn;
    }

    public String getEn() {
        return en;
    }

    public void setEn(String en) {
        this.en = en;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public static CountryCodeEnum findByCode(String code) {
        return Arrays.stream(CountryCodeEnum.values()).filter(e -> e.getCode().equals(code)).findFirst().orElse(null);
    }
}
