package com.ssy.lingxi.manage.serviceImpl;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.manage.entity.content.*;
import com.ssy.lingxi.manage.enums.ShelfEnum;
import com.ssy.lingxi.manage.model.req.content.CollectReq;
import com.ssy.lingxi.manage.model.req.content.InformationListDataReq;
import com.ssy.lingxi.manage.model.resp.InformationDetailResp;
import com.ssy.lingxi.manage.model.resp.InformationListResp;
import com.ssy.lingxi.manage.repository.ContentColumnRepository;
import com.ssy.lingxi.manage.repository.ContentInformationCollectRepository;
import com.ssy.lingxi.manage.repository.ContentInformationHistoryRepository;
import com.ssy.lingxi.manage.repository.ContentInformationRepository;
import com.ssy.lingxi.manage.service.IInformationMobileService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * mobile - 资讯 - 业务实现层
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/08/31
 */
@Service
public class InformationMobileServiceImpl implements IInformationMobileService {

    @Resource
    private JPAQueryFactory jpaQueryFactory;
    @Resource
    private ContentColumnRepository contentColumnRepository;
    @Resource
    private ContentInformationRepository contentInformationRepository;
    @Resource
    private ContentInformationCollectRepository contentInformationCollectRepository;
    @Resource
    private ContentInformationHistoryRepository contentInformationHistoryRepository;

    @Override
    public PageDataResp<InformationListResp> list(InformationListDataReq req) {
        //查询表实体 - 资讯
        QContentInformationDO contentInformation = QContentInformationDO.contentInformationDO;

        //查询条件构造 - 必要条件
        BooleanBuilder predicates = new BooleanBuilder();
        predicates.and(contentInformation.status.eq(ShelfEnum.ON_SHELF.getCode()));

        //搜索条件
        if (req.getColumnId() != null) {
            predicates.and(contentInformation.columnId.eq(req.getColumnId()));
        }
        if (!CollectionUtils.isEmpty(req.getRecommendLabel())) {
            predicates.and(contentInformation.recommendLabel.in(req.getRecommendLabel()));
        }
        if(req.getThirdlyCategoryId() != null) {
            predicates.and(contentInformation.thirdlyCategoryId.eq(req.getThirdlyCategoryId()));
        }
        if(StringUtils.isNotEmpty(req.getKeyword())) {
            List<ContentColumnDO> columnList = contentColumnRepository.findAllByNameContainingAndStatus(req.getKeyword(), CommonBooleanEnum.YES.getCode());
            List<Long> columnIdList = columnList.stream().map(ContentColumnDO::getId).collect(Collectors.toList());
            predicates.and(
                    contentInformation.title.like("%" + req.getKeyword() + "%")
                            .or(contentInformation.digest.like("%" + req.getKeyword() + "%")
                                    .or(contentInformation.content.like("%" + req.getKeyword() + "%")
                                            .or(contentInformation.firstCategoryName.like("%" + req.getKeyword() + "%")
                                                    .or(contentInformation.secondCategoryName.like("%" + req.getKeyword() + "%")
                                                            .or(contentInformation.thirdlyCategoryName.like("%" + req.getKeyword() + "%")
                                                                    .or(contentInformation.columnId.in(columnIdList)))))))
            );
        }


        //查询
        JPAQuery<InformationListResp> query = jpaQueryFactory.select(
                Projections.bean(
                        InformationListResp.class,
                        contentInformation.id,
                        contentInformation.columnId,
                        contentInformation.title,
                        contentInformation.readCount,
                        contentInformation.collectCount,
                        contentInformation.imageUrl,
                        contentInformation.digest,
                        contentInformation.createTime
                )
        ).from(contentInformation).where(predicates);

        //总记录数
        long totalCount = query.fetchCount();

        //排序（待添加枚举类）
        if (req.getSortType().equals(1)) {
            query.orderBy(contentInformation.createTime.desc());
        }
        if (req.getSortType().equals(2)) {
            query.orderBy(contentInformation.sort.asc());
        }
        if (req.getSortType().equals(3)) {
            query.orderBy(contentInformation.createTime.desc());
        }
        if (req.getSortType().equals(4)) {
            query.orderBy(contentInformation.readCount.desc());
        }
        if (req.getSortType().equals(5)) {
            query.orderBy(contentInformation.collectCount.desc());
        }

        //列表数据
        List<InformationListResp> list = query.offset((long) (req.getCurrent() - 1) * req.getPageSize()).limit(req.getPageSize()).fetch();

        //封装栏目名称
        List<ContentColumnDO> allColumn = contentColumnRepository.findAll();
        list = list.stream().peek(a -> {
            ContentColumnDO contentColumn = allColumn.stream().filter(b ->
                    b.getId().equals(a.getColumnId())
            ).findFirst().orElse(new ContentColumnDO());
            a.setColumnName(contentColumn.getName());
        }).collect(Collectors.toList());

        return new PageDataResp<>(totalCount, list);
    }

    @Override
    public InformationDetailResp detail(CommonIdReq request, UserLoginCacheDTO user) {
        //根据id获取资讯
        ContentInformationDO information = contentInformationRepository.findById(request.getId()).orElse(null);
        if (information == null) {
            throw new BusinessException(ResponseCodeEnum.MAN_RECORDS_DON_T_EXIST);
        }
        if (information.getStatus().equals(ShelfEnum.OFF_SHELF.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MAN_DISABLE);
        }

        //拷贝
        InformationDetailResp resp = new InformationDetailResp();
        BeanUtils.copyProperties(information, resp);

        //栏目名称
        ContentColumnDO contentColumn = contentColumnRepository.findById(resp.getColumnId()).orElse(new ContentColumnDO());
        resp.setColumnName(contentColumn.getName());

        //收藏状态
        if (user != null) {
            Boolean collectStatus = contentInformationCollectRepository.existsByInformationIdAndMemberIdAndUserId(information.getId(), user.getMemberId(), user.getUserId());
            resp.setCollectStatus(collectStatus);

            //保存当前登录用户的浏览资讯记录
            ContentInformationHistoryDO informationHistory = contentInformationHistoryRepository.findByUserIdAndInformationId(user.getUserId(), information.getId());
            if (informationHistory != null) {
                informationHistory.setCreateTime(System.currentTimeMillis());
            }else {
                informationHistory = new ContentInformationHistoryDO();
                informationHistory.setUserId(user.getUserId());
                informationHistory.setInformationId(information.getId());
            }

            contentInformationHistoryRepository.save(informationHistory);
        }

        //查看详情增加浏览量
        information.setReadCount(information.getReadCount() + 1);
        contentInformationRepository.saveAndFlush(information);

        return resp;
    }

    @Override
    @Transactional
    public void collect(CollectReq request, UserLoginCacheDTO user) {
        if (request.getStatus()) {
            if (contentInformationCollectRepository.existsByInformationIdAndMemberIdAndUserId(request.getInformationId(), user.getMemberId(), user.getUserId())) {
                throw new BusinessException("不能重复收藏，请刷新页面");
            }
            ContentInformationCollectDO collect = new ContentInformationCollectDO();
            collect.setInformationId(request.getInformationId());
            collect.setMemberId(user.getMemberId());
            collect.setUserId(user.getUserId());
            contentInformationCollectRepository.saveAndFlush(collect);
        } else {
            contentInformationCollectRepository.deleteByInformationIdAndMemberIdAndUserId(request.getInformationId(), user.getMemberId(), user.getUserId());
        }

        //更新资讯收藏数
        contentInformationRepository.findById(request.getInformationId()).ifPresent(information ->
                information.setCollectCount(request.getStatus() ?
                        information.getCollectCount() + 1 : information.getReadCount() - 1)
        );

    }

    @Override
    public PageDataResp<InformationListResp> collectList(PageDataReq request, UserLoginCacheDTO user) {
        //分页查询当前登录用户收藏资讯信息
        Pageable page = PageRequest.of(request.getCurrent() - 1, request.getPageSize(), Sort.by("createTime").descending());
        Page<ContentInformationCollectDO> result = contentInformationCollectRepository.findAllByMemberIdAndUserId(user.getMemberId(), user.getUserId(), page);
        List<ContentInformationCollectDO> collectList = result.getContent();

        if (!CollectionUtils.isEmpty(collectList)) {
            List<Long> informationIdList = collectList.stream().map(ContentInformationCollectDO::getInformationId).collect(Collectors.toList());
            List<ContentInformationDO> informationList = contentInformationRepository.findAllById(informationIdList);
            if (!CollectionUtils.isEmpty(informationList)) {
                List<ContentColumnDO> allColumn = contentColumnRepository.findAll();

                List<InformationListResp> responses = informationList.stream().map(a -> {
                    InformationListResp resp = new InformationListResp();
                    BeanUtils.copyProperties(a, resp);

                    ContentColumnDO contentColumn = allColumn.stream().filter(b -> b.getId().equals(a.getColumnId())).findFirst().orElse(null);
                    resp.setColumnName(contentColumn == null ? "" : contentColumn.getName());

                    Long collectTime = collectList.stream().filter(b -> b.getInformationId().equals(a.getId())).mapToLong(ContentInformationCollectDO::getCreateTime).findFirst().orElse(0L);
                    resp.setCollectTime(collectTime);
                    return resp;
                }).sorted(Comparator.comparing(InformationListResp::getCollectTime).reversed()).collect(Collectors.toList());

                return new PageDataResp<>(result.getTotalElements(), responses);
            }
        }

        return new PageDataResp<>(0L, new ArrayList<>());
    }

    @Override
    public List<InformationListResp> history(UserLoginCacheDTO user) {
        List<ContentInformationHistoryDO> historyList = contentInformationHistoryRepository.findByUserId(user.getUserId(), Sort.by("createTime").descending());

        List<Long> informationIdList = historyList.stream().map(ContentInformationHistoryDO::getInformationId).collect(Collectors.toList());

        List<ContentInformationDO> informationList = contentInformationRepository.findAllById(informationIdList);

        List<ContentColumnDO> allColumn = contentColumnRepository.findAll();

        return informationList.stream().map(a -> {
            InformationListResp resp = new InformationListResp();
            BeanUtils.copyProperties(a, resp);

            ContentColumnDO contentColumn = allColumn.stream().filter(b -> b.getId().equals(a.getColumnId())).findFirst().orElse(null);
            resp.setColumnName(contentColumn == null ? "" : contentColumn.getName());

            return resp;
        }).collect(Collectors.toList());

    }
}
