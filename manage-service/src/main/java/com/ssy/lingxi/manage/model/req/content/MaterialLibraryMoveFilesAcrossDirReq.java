package com.ssy.lingxi.manage.model.req.content;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 素材管理 - 跨目录移动文件 - 请求实体类
 *
 * <AUTHOR>
 */
@Data
public class MaterialLibraryMoveFilesAcrossDirReq {

    /**
     * 移动文件Id
     */
    @NotEmpty(message = "移动文件Id集合不能为空")
    private List<Long> moveIdList;
    /**
     * 目标目录Id
     */
    @NotNull(message = "目标目录Id不能为空")
    private Long targetId;

}
