package com.ssy.lingxi.manage.model.req.content;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 广告管理 - 列表 - 请求实体类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/9/17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ContentAdvertPageDataReq extends PageDataReq {
    private static final long serialVersionUID = 3525145360529841443L;

    /**
     * 标题
     */
    private String title;

    /**
     * 状态 1-待上架 2-已上架 3-已下架
     */
    private Integer status;

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 结束时间
     */
    private Long endTime;
}
