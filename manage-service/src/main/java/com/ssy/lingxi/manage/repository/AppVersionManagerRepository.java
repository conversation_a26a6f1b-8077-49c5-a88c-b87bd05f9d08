package com.ssy.lingxi.manage.repository;

import com.ssy.lingxi.manage.entity.AppVersionManagerDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * APP版本管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-11-12
 */
@Repository
public interface AppVersionManagerRepository extends JpaRepository<AppVersionManagerDO, Long>, JpaSpecificationExecutor<AppVersionManagerDO> {
    AppVersionManagerDO findFirstByOrderByIdDesc();
}
