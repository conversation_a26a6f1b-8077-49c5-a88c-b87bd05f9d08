package com.ssy.lingxi.settlement.api.fallback;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.settlement.api.feign.IApplyAmountFeign;
import com.ssy.lingxi.settlement.api.model.req.ApplyAmountDetailSunQueryDataReq;
import com.ssy.lingxi.settlement.api.model.req.ApplyAmountDetailTotalSunQueryReq;
import com.ssy.lingxi.settlement.api.model.req.ApplyAmountSunQueryDataReq;
import com.ssy.lingxi.settlement.api.model.resp.ApplyAmountDetailSunResp;
import com.ssy.lingxi.settlement.api.model.resp.ApplyAmountDetailTotalSunResp;
import com.ssy.lingxi.settlement.api.model.resp.ApplyAmountSunResp;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/3/8 14:02
 */
@Slf4j
public class ApplyAmountFeignFallback implements IApplyAmountFeign {

    private final Throwable throwable;

    public ApplyAmountFeignFallback(Throwable cause) {
        this.throwable = cause;
    }

    @Override
    public WrapperResp<PageDataResp<ApplyAmountDetailSunResp>> pageContractExecuteDetail(ApplyAmountDetailSunQueryDataReq queryVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_SETTLEMENT_ERROR);
    }

    @Override
    public WrapperResp<List<ApplyAmountDetailTotalSunResp>> pageContractExecuteDetailSum(ApplyAmountDetailTotalSunQueryReq queryVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_SETTLEMENT_ERROR);
    }

    @Override
    public WrapperResp<PageDataResp<ApplyAmountSunResp>> pageListForSummaryByParty(ApplyAmountSunQueryDataReq queryVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_SETTLEMENT_ERROR);
    }

    @Override
    public WrapperResp<List<ApplyAmountSunResp>> listForPaySummaryByParty(ApplyAmountSunQueryDataReq queryVO) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_SETTLEMENT_ERROR);
    }
}
