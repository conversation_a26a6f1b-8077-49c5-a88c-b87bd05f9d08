package com.ssy.lingxi.settlement.api.fallback;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.settlement.api.feign.ICorporateAccountConfigFeign;
import com.ssy.lingxi.settlement.api.model.resp.CorporateAccountConfigResp;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CorporateAccountConfigFeignFallback implements ICorporateAccountConfigFeign {

    private final Throwable throwable;

    public CorporateAccountConfigFeignFallback(Throwable cause) {
        this.throwable = cause;
    }

    /**
     * 结算能力-结算规则配置-对公账户配置
     *
     * @param memberId
     * <AUTHOR>
     * @since 2020/8/25
     */
    @Override
    public WrapperResp<CorporateAccountConfigResp> corporateAccountConfig(Long memberId, Long memberRoleId) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_SETTLEMENT_ERROR);
    }
}
