package com.ssy.lingxi.settlement.api.fallback;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.settlement.api.feign.ISettlementProductFeign;
import com.ssy.lingxi.settlement.api.model.req.SettlementOrderProductReq;
import lombok.extern.slf4j.Slf4j;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.02.18
 * @since 2022-06-16
 */
@Slf4j
public class SettlementProductFeignFallback implements ISettlementProductFeign {

    private final Throwable throwable;

    public SettlementProductFeignFallback(Throwable cause) {
        this.throwable = cause;
    }

    /**
     * 根据订单号、收货单号，物料编码修改验退数量
     *
     * @param orderProductVOs
     */
    @Override
    public WrapperResp<Void> updateQuantity(@Valid List<SettlementOrderProductReq> orderProductVOs) {
        log.error(throwable.getMessage());
        return WrapperUtil.fail(ResponseCodeEnum.SERVICE_SETTLEMENT_ERROR);
    }
}
