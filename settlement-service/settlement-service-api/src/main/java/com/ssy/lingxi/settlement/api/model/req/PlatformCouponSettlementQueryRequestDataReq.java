package com.ssy.lingxi.settlement.api.model.req;

import com.ssy.lingxi.common.model.req.PageDataReq;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.PositiveOrZero;

/**
 * 平台优惠券结算查询VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/1/18 13:46
 */
public class PlatformCouponSettlementQueryRequestDataReq extends PageDataReq {
    private static final long serialVersionUID = -1235792507907500479L;
    /**
     * 开始时间 （yyyy-MM-dd）
     */
    private String startTime;

    /**
     * 结束时间 （yyyy-MM-dd）
     */
    private String endTime;

    /**
     * 结算方
     */
    private String settlementName;

    /**
     * 结算状态：0-所有，1-待对账，2-待付款，3-待收款，4-已完成
     */
    @NotNull(message = "结算状态要大于等于0")
    @PositiveOrZero(message = "结算状态要大于等于0")
    private Integer status;

    /**
     * 结算单号
     */
    private String settlementNo;

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getSettlementName() {
        return settlementName;
    }

    public void setSettlementName(String settlementName) {
        this.settlementName = settlementName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getSettlementNo() {
        return settlementNo;
    }

    public void setSettlementNo(String settlementNo) {
        this.settlementNo = settlementNo;
    }
}
