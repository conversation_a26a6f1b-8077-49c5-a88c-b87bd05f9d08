package com.ssy.lingxi.settlement.enums;

import com.ssy.lingxi.component.base.language.LanguageHolder;

import java.util.Arrays;

/**
 * 发票类型
 * <AUTHOR>
 * @since 2020/10/21
 * @version 2.0.0
 */
public enum InvoiceTypeEnum {

    /**
     * 所有
     */
    ALL(0, "所有"),
    /**
     * 企业（默认）
     */
    COMPANY(1, "企业"),
    /**
     * 个人
     */
    PERSONAL(2, "个人");

    private final Integer code;
    private final String message;

    InvoiceTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return LanguageHolder.getTranslation(this.getClass(), this.message, this.code);
    }

    public static String getMessage(Integer code) {
        return Arrays.stream(InvoiceTypeEnum.values()).filter(e -> e.getCode().equals(code)).findFirst().orElse(ALL).getMessage();
    }
}
