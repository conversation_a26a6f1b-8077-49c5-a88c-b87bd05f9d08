package com.ssy.lingxi.settlement.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.ssy.lingxi.common.constant.TableNameConstant;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 业务请款单行信息DO
 * <AUTHOR>
 * @since 2021/12/7
 * @version 2.0.0
 */
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_SETTLEMENT_SERVICE + "apply_amount_row",
        indexes = {@Index(name = TableNameConstant.TABLE_PRE_SETTLEMENT_SERVICE + "apply_amount_row_apply_id_idx",columnList = "apply_id"),
                @Index(name = TableNameConstant.TABLE_PRE_SETTLEMENT_SERVICE + "apply_amount_row_bill_id_idx",columnList = "billId"),
                @Index(name = TableNameConstant.TABLE_PRE_SETTLEMENT_SERVICE + "apply_amount_row_bill_type_idx",columnList = "billType"),
                @Index(name = TableNameConstant.TABLE_PRE_SETTLEMENT_SERVICE + "apply_amount_row_source_contract_id_idx",columnList = "sourceContractId"),
                @Index(name = TableNameConstant.TABLE_PRE_SETTLEMENT_SERVICE + "apply_amount_row_apply_payment_idx",columnList = "applyPayment"),
                @Index(name = TableNameConstant.TABLE_PRE_SETTLEMENT_SERVICE + "apply_amount_row_reconciliation_amount_idx",columnList = "reconciliationAmount"),
                @Index(name = TableNameConstant.TABLE_PRE_SETTLEMENT_SERVICE + "apply_amount_row_bill_tax_rate_idx",columnList = "taxRate"),
})
public class ApplyAmountRowDO implements Serializable {

    private static final long serialVersionUID = -158124681655548741L;

    /**
     * ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 多对一双向关联对账单
     */
    @JsonBackReference
    @ManyToOne(cascade = {CascadeType.MERGE, CascadeType.REFRESH}, optional = false)
    @JoinColumn(name = "apply_id")
    private ApplyAmountDO apply;

    /**
     * 请款单编号
     */
    @Column(columnDefinition = "varchar(50)")
    private String applyNo;

    /**
     * 请款类型,定义在ApplyAmountTypeEnum中
     */
    @Column(columnDefinition = "int")
    private Integer applyType;

    /**
     * 单据id
     */
    @Column
    private Long billId;

    /**
     * 单据编号
     */
    @Column(columnDefinition = "varchar(50)")
    private String billNo;

    /**
     * 单据摘要
     */
    @Column(columnDefinition = "varchar(60)")
    private String billAbstract;

    /**
     * 单据类型，定义在 ApplyAmountRowBillTypeEnum 中
     */
    @Column(columnDefinition = "int")
    private Integer billType;

    /**
     * 单据来源合同id
     */
    @Column
    private Long sourceContractId;

    /**
     * 单据时间
     */
    @Column(name = "expectPayTime")
    private LocalDateTime billTime;

    /**
     * 单据状态,定义在 ApplyAmountRowStatusEnum 中
     */
    @Column(columnDefinition = "varchar(20)")
    private String billStatus;

    /**
     * 请款单状态，定义在 ApplyAmountStatusEnum 中
     */
    @Column(columnDefinition = "int")
    private Integer status;

    /**
     * 单据金额
     */
    @Column(columnDefinition = "decimal")
    private BigDecimal billAmount;

    /**
     * 单据行金额
     */
    @Column(columnDefinition = "decimal")
    private BigDecimal billRowAmount;

    /**
     * 是否含税
     */
    @Column(columnDefinition = "int")
    private Integer hasTax;

    /**
     * 税率（百分比的分子部分）
     */
    @Column(name = "taxRate",columnDefinition = "decimal")
    private BigDecimal taxRate;

    /**
     * 已付款
     */
    @Column(columnDefinition = "decimal")
    private BigDecimal paid;

    /**
     * 已请款待付款
     */
    @Column(columnDefinition = "decimal")
    private BigDecimal appliedUnpaid;

    /**
     * 对账金额
     */
    @Column(columnDefinition = "decimal")
    private BigDecimal reconciliationAmount;

    /**
     * 请款金额
     */
    @Column(columnDefinition = "decimal")
    private BigDecimal applyPayment;

    /**
     * 核销金额
     */
    @Column(columnDefinition = "decimal")
    private BigDecimal writeOffAmount;

    /**
     * 用于核销金额
     */
    @Column(columnDefinition = "decimal")
    private BigDecimal forWriteOffAmount;

    /**
     * 一对多双向关联请款单行明细
     */
    @JsonManagedReference
    @OneToMany(mappedBy = "applyRow", cascade = {CascadeType.PERSIST, CascadeType.REMOVE, CascadeType.MERGE}, fetch = FetchType.LAZY)
    private List<ApplyAmountRowWriteOffRecordDO> writeOffRecords;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public ApplyAmountDO getApply() {
        return apply;
    }

    public void setApply(ApplyAmountDO apply) {
        this.apply = apply;
    }

    public String getApplyNo() {
        return applyNo;
    }

    public void setApplyNo(String applyNo) {
        this.applyNo = applyNo;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public String getBillAbstract() {
        return billAbstract;
    }

    public void setBillAbstract(String billAbstract) {
        this.billAbstract = billAbstract;
    }

    public Integer getBillType() {
        return billType;
    }

    public void setBillType(Integer billType) {
        this.billType = billType;
    }

    public LocalDateTime getBillTime() {
        return billTime;
    }

    public void setBillTime(LocalDateTime billTime) {
        this.billTime = billTime;
    }

    public String getBillStatus() {
        return billStatus;
    }

    public void setBillStatus(String billStatus) {
        this.billStatus = billStatus;
    }

    public Long getBillId() {
        return billId;
    }

    public void setBillId(Long billId) {
        this.billId = billId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public BigDecimal getBillAmount() {
        return billAmount;
    }

    public void setBillAmount(BigDecimal billAmount) {
        this.billAmount = billAmount;
    }

    public Integer getHasTax() {
        return hasTax;
    }

    public void setHasTax(Integer hasTax) {
        this.hasTax = hasTax;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public BigDecimal getPaid() {
        return paid;
    }

    public void setPaid(BigDecimal paid) {
        this.paid = paid;
    }

    public BigDecimal getAppliedUnpaid() {
        return appliedUnpaid;
    }

    public void setAppliedUnpaid(BigDecimal appliedUnpaid) {
        this.appliedUnpaid = appliedUnpaid;
    }

    public BigDecimal getReconciliationAmount() {
        return reconciliationAmount;
    }

    public void setReconciliationAmount(BigDecimal reconciliationAmount) {
        this.reconciliationAmount = reconciliationAmount;
    }

    public BigDecimal getApplyPayment() {
        return applyPayment;
    }

    public void setApplyPayment(BigDecimal applyPayment) {
        this.applyPayment = applyPayment;
    }

    public BigDecimal getWriteOffAmount() {
        return writeOffAmount;
    }

    public void setWriteOffAmount(BigDecimal writeOffAmount) {
        this.writeOffAmount = writeOffAmount;
    }

    public Integer getApplyType() {
        return applyType;
    }

    public void setApplyType(Integer applyType) {
        this.applyType = applyType;
    }

    public List<ApplyAmountRowWriteOffRecordDO> getWriteOffRecords() {
        return writeOffRecords;
    }

    public void setWriteOffRecords(List<ApplyAmountRowWriteOffRecordDO> writeOffRecords) {
        this.writeOffRecords = writeOffRecords;
    }

    public Long getSourceContractId() {
        return sourceContractId;
    }

    public void setSourceContractId(Long sourceContractId) {
        this.sourceContractId = sourceContractId;
    }

    public BigDecimal getForWriteOffAmount() {
        return forWriteOffAmount;
    }

    public void setForWriteOffAmount(BigDecimal forWriteOffAmount) {
        this.forWriteOffAmount = forWriteOffAmount;
    }

    public BigDecimal getBillRowAmount() {
        return billRowAmount;
    }

    public void setBillRowAmount(BigDecimal billRowAmount) {
        this.billRowAmount = billRowAmount;
    }
}
