package com.ssy.lingxi.settlement.model.req;

import com.ssy.lingxi.common.model.req.PageDataReq;

/**
 * 平台结算策略成员分页VO
 * <AUTHOR>
 * @since 2020/11/4
 * @version 2.0.0
 */
public class PlatformSettlementStrategyMemberPageDataReq extends PageDataReq {
    private static final long serialVersionUID = -7905198246243518527L;

    /**
     * 策略id
     */
    private Long strategyId;

    /**
     * 会员名称
     */
    private String memberName;

    public Long getStrategyId() {
        return strategyId;
    }

    public void setStrategyId(Long strategyId) {
        this.strategyId = strategyId;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }
}
