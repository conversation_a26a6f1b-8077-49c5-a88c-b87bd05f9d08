package com.ssy.lingxi.settlement.model.req;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * 平台后台 - 请款单流程规则适用会员参数
 *
 * <AUTHOR>
 * @version 2.05.18
 * @since 2022-04-22
 */
public class PlatformApplyAmountProcessMemberReq implements Serializable {
    private static final long serialVersionUID = -2652827618899957612L;

    /**
     * 会员id
     */
    @NotNull(message = "会员id要大于0")
    @Positive(message = "会员id要大于0")
    private Long memberId;

    /**
     * 会员角色id
     */
    @NotNull(message = "会员角色id要大于0")
    @Positive(message = "会员角色id要大于0")
    private Long roleId;

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    @Override
    public String toString() {
        return "PlatformApplyAmountProcessMemberVO{" +
                "memberId=" + memberId +
                ", roleId=" + roleId +
                '}';
    }
}
