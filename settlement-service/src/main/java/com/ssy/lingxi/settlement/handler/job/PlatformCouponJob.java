package com.ssy.lingxi.settlement.handler.job;

import com.ssy.lingxi.component.xxlJob.annotation.XxlRegister;
import com.ssy.lingxi.settlement.service.IPlatformCouponSettlementService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 定时任务
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/1/25 11:35
 */
@Slf4j
@Component
public class PlatformCouponJob {
    @Resource
    private IPlatformCouponSettlementService platformCouponSettlementService;

    /**
     * 平台优惠券结算任务(每天0:20执行)
     */
    @XxlRegister(cron = "0 20 00 ? * *", jobDesc = "平台优惠券结算任务")
    @XxlJob("PlatformCouponJobHandler")
    public void platformCouponJobHandler() {
        try {
            log.info("开始平台优惠券结算任务");
            platformCouponSettlementService.timingSettlement(LocalDateTime.now());
        } catch (Exception e) {
            log.error("平台优惠券结算任务失败：{}", e.getMessage());
        }
    }
}
