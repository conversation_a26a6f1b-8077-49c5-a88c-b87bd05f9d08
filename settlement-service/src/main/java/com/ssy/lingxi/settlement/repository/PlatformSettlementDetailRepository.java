package com.ssy.lingxi.settlement.repository;

import com.ssy.lingxi.settlement.entity.PlatformSettlementDetailDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.math.BigDecimal;

/**
 * 平台结算明细
 * <AUTHOR>
 * @since 2020/10/29
 * @version 2.0.0
 */
public interface PlatformSettlementDetailRepository extends JpaRepository<PlatformSettlementDetailDO, Long>, JpaSpecificationExecutor<PlatformSettlementDetailDO> {

    /**
     * 查询是否存在大于佣金的数据
     * <AUTHOR>
     * @since 2021/1/21
     * @param brokerage: 佣金
     * @return true/false
     **/
    Boolean existsByBrokerageGreaterThan(BigDecimal brokerage);
}
