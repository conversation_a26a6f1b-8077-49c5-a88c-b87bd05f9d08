package com.ssy.lingxi.settlement.service;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.settlement.model.req.DeletePlatformInvoiceReq;
import com.ssy.lingxi.settlement.model.req.PlatformInvoiceAddReq;
import com.ssy.lingxi.settlement.model.req.PlatformInvoiceUpdateReq;
import com.ssy.lingxi.settlement.model.resp.PlatformInvoiceDetailResp;
import com.ssy.lingxi.settlement.model.resp.PlatformInvoiceResp;

import java.util.List;

/**
 * 平台发票信息接口
 * <AUTHOR>
 * @since 2020/10/20
 * @version 2.0.0
 */
public interface IPlatformInvoiceService {

    /**
     * 获取平台发票列表
     * <AUTHOR>
     * @since 2020/10/20
     * @return com.ssy.lingxi.common.response.Wrapper<java.util.List < com.ssy.lingxi.settle.accounts.mode.vo.response.PlatformInvoiceVO>>
     **/
    List<PlatformInvoiceResp> getList();

    /**
     * 新增平台发票
     * <AUTHOR>
     * @since 2020/10/20
     * @param sysUser: 当前登录用户
     * @param addVO: 新增参数
     * @return com.ssy.lingxi.common.response.Wrapper
     **/
    Long add(UserLoginCacheDTO sysUser, PlatformInvoiceAddReq addVO);

    /**
     * 更新平台发票
     * <AUTHOR>
     * @since 2020/10/20
     * @param sysUser:当前登录用户
     * @param updateVO: 更新参数
     * @return com.ssy.lingxi.common.response.Wrapper
     **/
    void update(UserLoginCacheDTO sysUser, PlatformInvoiceUpdateReq updateVO);

    /**
     * 删除平台发票
     * <AUTHOR>
     * @since 2020/10/20
     * @param deleteVo:删除参数
     * @param sysUser: 当前登录用户
     * @return com.ssy.lingxi.common.response.Wrapper
     **/
    void delete(UserLoginCacheDTO sysUser, DeletePlatformInvoiceReq deleteVo);

    /**
     * 获取平台发票详情
     * <AUTHOR>
     * @since 2020/10/20
     * @param id: 发票id
     * @return com.ssy.lingxi.common.response.Wrapper<com.ssy.lingxi.settle.accounts.mode.vo.response.PlatformInvoiceDetailVO>
     **/
    PlatformInvoiceDetailResp getDetail(Long id);

    /**
     * 获取默认发票
     * <AUTHOR>
     * @since 2021/1/21
     * @return 平台默认发票
     **/
    WrapperResp<PlatformInvoiceDetailResp> getDefaultDetail();
}
