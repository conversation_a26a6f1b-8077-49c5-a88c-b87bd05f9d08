package com.ssy.lingxi.settlement.repository;

import com.ssy.lingxi.settlement.entity.MemberReceiptInvoiceProveDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * 会员收据发票凭证
 * <AUTHOR>
 * @since 2020/10/28
 * @version 2.0.0
 */
public interface MemberReceiptInvoiceProveRepository extends JpaRepository<MemberReceiptInvoiceProveDO, Long>, JpaSpecificationExecutor<MemberReceiptInvoiceProveDO> {

    /**
     * 根据票据id查询开票凭证
     * <AUTHOR>
     * @since 2020/10/28
     * @param receiptInvoiceId: 票据id
     * @return com.ssy.lingxi.settle.accounts.entity.MemberReceiptInvoiceProveDO
     **/
    MemberReceiptInvoiceProveDO findByReceiptInvoiceId(Long receiptInvoiceId);

    /**
     * 根据票据id查询开票凭证列表（售后退货）
     * <AUTHOR>
     * @since 2021/1/27
     * @param receiptInvoiceId 票据id
     * @return 开票凭证列表（售后退货）
     **/
    List<MemberReceiptInvoiceProveDO> findAllByReceiptInvoiceId(Long receiptInvoiceId);
}
