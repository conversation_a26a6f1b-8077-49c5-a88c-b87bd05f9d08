package com.ssy.lingxi.settlement.enums;

import com.ssy.lingxi.component.base.enums.order.OrderPayChannelEnum;
import com.ssy.lingxi.component.base.language.LanguageHolder;
import com.ssy.lingxi.pay.api.enums.EAccountPayChannelEnum;

import java.util.Arrays;

/**
 * 通联支付-支付渠道类型枚举
 * <AUTHOR>
 * @since 2021/12/9
 * @version 2.0.0
 */
public enum PayChannelTypeEnum {

    ALL(-1, "", -1),
    /**
     * 微信支付
     */
    ALLIN_WECHAT(OrderPayChannelEnum.ALLIN_WECHAT.getCode(),OrderPayChannelEnum.ALLIN_WECHAT.getName(), EAccountPayChannelEnum.Platform_Wechat.getCode()),
    /**
     * 支付宝支付
     */
    ALLIN_ALI_PAY(OrderPayChannelEnum.ALLIN_ALI_PAY.getCode(),OrderPayChannelEnum.ALLIN_ALI_PAY.getName(), EAccountPayChannelEnum.Platform_Alipay.getCode()),
    /**
     * 快捷支付
     */
    ALLIN_QUICK(OrderPayChannelEnum.ALLIN_QUICK.getCode(),OrderPayChannelEnum.ALLIN_QUICK.getName(), EAccountPayChannelEnum.Quick_Pay.getCode()),
    /**
     * 网银支付
     */
    ALLIN_UNION(OrderPayChannelEnum.ALLIN_UNION.getCode(),OrderPayChannelEnum.ALLIN_UNION.getName(), EAccountPayChannelEnum.GateWay_Pay.getCode()),
    /**
     * 余额支付
     */
    ALLIN_BALANCE(OrderPayChannelEnum.ALLIN_BALANCE.getCode(),OrderPayChannelEnum.ALLIN_BALANCE.getName(), EAccountPayChannelEnum.Balance.getCode()),;
    /**
     * 订单支付方式枚举
     */
    private final int settlePayChannel;
    private final String message;
    /**
     * 支付渠道
     */
    private final int allInPayChannel;

    PayChannelTypeEnum(int settlePayChannel, String message, int allInPayChannel) {
        this.settlePayChannel = settlePayChannel;
        this.message = message;
        this.allInPayChannel = allInPayChannel;
    }

    public int getSettlePayChannel() {
        return settlePayChannel;
    }

    public String getMessage() {
        return LanguageHolder.getTranslation(this.getClass(), this.message, this.settlePayChannel);
    }

    public int getAllInPayChannel() {
        return allInPayChannel;
    }

    public static int getAllInPayChannel(int settlePayChannel) {
        return Arrays.stream(PayChannelTypeEnum.values())
                .filter(e -> e.getSettlePayChannel() == settlePayChannel)
                .findFirst().orElse(PayChannelTypeEnum.ALL)
                .getAllInPayChannel();
    }
}
