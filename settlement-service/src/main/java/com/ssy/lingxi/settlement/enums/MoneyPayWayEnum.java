package com.ssy.lingxi.settlement.enums;

import com.ssy.lingxi.common.model.resp.select.DropdownItemResp;
import com.ssy.lingxi.component.base.language.LanguageHolder;
import com.ssy.lingxi.settlement.api.enums.SettlementHelpEnum;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 支付方式枚举
 * <AUTHOR>
 * @since 2021/12/14
 * @version 2.0.0
 */
public enum MoneyPayWayEnum {

    /**
     * 线下结算
     */
    OFFLINE(1, "线下结算"),
    /**
     * 通联支付结算
     */
    ALL_IN_PAY(2, "通联支付结算");

    private final Integer code;
    private final String message;

    MoneyPayWayEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return LanguageHolder.getTranslation(this.getClass(), this.message, this.code);
    }

    /**
     * 根据状态枚举值获得状态名称
     * @param code 外部状态枚举值
     * @return 外部状态名称
     */
    public static String getNameByCode(Integer code) {
        MoneyPayWayEnum statusEnum = Arrays.stream(MoneyPayWayEnum.values()).filter(e -> e.getCode().equals(code)).findFirst().orElse(null);
        return statusEnum == null ?  SettlementHelpEnum.UNKNOWN.getMessage() : statusEnum.getMessage();
    }

    /**
     * 获得状态列表
     * @return 前端下拉框内容
     */
    public static List<DropdownItemResp> toDropdownList() {
        return Arrays.stream(MoneyPayWayEnum.values()).sorted(Comparator.comparingInt(MoneyPayWayEnum::getCode)).map(e -> new DropdownItemResp(e.getCode(), e.getMessage())).collect(Collectors.toList());
    }
}
