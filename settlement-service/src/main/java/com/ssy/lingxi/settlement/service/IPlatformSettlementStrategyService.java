package com.ssy.lingxi.settlement.service;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.settlement.model.req.*;
import com.ssy.lingxi.settlement.model.resp.PlatformSettlementStrategyDetailResp;
import com.ssy.lingxi.settlement.model.resp.PlatformSettlementStrategyResp;

/**
 * 平台结算策略接口
 * <AUTHOR>
 * @since 2020/11/4
 * @version 2.0.0
 */
public interface IPlatformSettlementStrategyService {

    /**
     * 分页查询平台结算策略
     * <AUTHOR>
     * @since 2020/11/4
     * @param sysUser:
     * @param pageVO:
     * @return com.ssy.lingxi.common.response.Wrapper<com.ssy.lingxi.common.response.PageData < com.ssy.lingxi.settle.accounts.mode.vo.response.PlatformSettlementStrategyVO>>
     **/
    PageDataResp<PlatformSettlementStrategyResp> pageStrategy(UserLoginCacheDTO sysUser, PlatformSettlementStrategyPageDataReq pageVO);

    /**
     * 设置平台结算策略状态
     * <AUTHOR>
     * @since 2020/11/4
     * @param sysUser:
     * @param setStatusVO:
     * @return com.ssy.lingxi.common.response.Wrapper
     **/
    void setStatus(UserLoginCacheDTO sysUser, PlatformSettlementStrategySetStatusReq setStatusVO);

    /**
     * 查询平台结算策略详情
     * <AUTHOR>
     * @since 2020/11/4
     * @param id:
     * @return com.ssy.lingxi.common.response.Wrapper<com.ssy.lingxi.settle.accounts.mode.vo.response.PlatformSettlementStrategyDetailVO>
     **/
    PlatformSettlementStrategyDetailResp getDetail(Long id);

    /**
     * 新增平台结算策略
     * <AUTHOR>
     * @since 2020/11/4
     * @param addVO:
     * @return com.ssy.lingxi.common.response.Wrapper<java.lang.Long>
     **/
    Long add(UserLoginCacheDTO sysUser, PlatformSettlementStrategyAddReq addVO);

    /**
     * 更新平台结算策略
     * <AUTHOR>
     * @since 2020/11/4
     * @param updateVO:
     * @return com.ssy.lingxi.common.response.Wrapper<java.lang.Long>
     **/
    Long update(UserLoginCacheDTO sysUser, PlatformSettlementStrategyUpdateReq updateVO);

    /**
     * 删除平台结算策略
     * <AUTHOR>
     * @since 2020/12/24
     * @param sysUser:
     * @param deleteVO:
     **/
    void delete(UserLoginCacheDTO sysUser, PlatformSettlementStrategyDeleteReq deleteVO);
}
