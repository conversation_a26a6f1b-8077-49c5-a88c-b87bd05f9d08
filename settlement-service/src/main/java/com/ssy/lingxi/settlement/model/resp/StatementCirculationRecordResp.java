package com.ssy.lingxi.settlement.model.resp;

import java.io.Serializable;

/**
 * 账单流转记录VO
 * <AUTHOR>
 * @since 2021/12/02
 * @version 2.0.0
 */
public class StatementCirculationRecordResp implements Serializable {

    private static final long serialVersionUID = -1590204340655238741L;


    /**
     * 操作角色
     */
    private String operationRole;

    /**
     * 状态
     */
    private String status;

    /**
     * 状态名称
     */
    private String statusName;

    /**
     * 操作
     */
    private String operation;

    /**
     * 操作时间
     */
    private String operationTime;

    /**
     * 审核意见
     */
    private String remark;



    public String getOperationRole() {
        return operationRole;
    }

    public void setOperationRole(String operationRole) {
        this.operationRole = operationRole;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(String operationTime) {
        this.operationTime = operationTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
