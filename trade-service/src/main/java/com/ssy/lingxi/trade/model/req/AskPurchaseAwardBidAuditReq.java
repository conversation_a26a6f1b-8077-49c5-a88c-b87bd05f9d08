package com.ssy.lingxi.trade.model.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 求购单-授标-审核
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AskPurchaseAwardBidAuditReq {

    /**
     * 求购单id
     */
    @NotNull
    private Long id;

    /**
     * 是否同意： 0：不同意，1：同意(PS: 不需要是否同意的就不传这个字段就行)
     */
//    @NotNull
//    @Range(min = 0, max = 1, message = "是否同意取值范围： 0~1 ")
    private Integer agree;

    /**
     * 审核不通过原因，（审核不通过时必填）
     */
    private String reason;

}
