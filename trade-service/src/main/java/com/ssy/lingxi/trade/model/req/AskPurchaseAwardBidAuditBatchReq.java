package com.ssy.lingxi.trade.model.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 求购单-授标-审核
 *
 * <AUTHOR>
 */
@Data
public class AskPurchaseAwardBidAuditBatchReq {

    /**
     * 求购单id
     */
    @NotNull
    private List<Long> idList;

    /**
     * 是否同意： 0：不同意，1：同意(PS: 不需要是否同意的就不传这个字段就行)
     */
//    @NotNull
//    @Range(min = 0, max = 1, message = "是否同意取值范围： 0~1 ")
    private Integer agree;

    /**
     * 审核不通过原因，（审核不通过时必填）
     */
    private String reason;

}
