package com.ssy.lingxi.trade.api.model.req;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
* 待新增商品询价单添加
* <AUTHOR>
* @since 2020/8/10
*/
@Data
public class InquiryListAddReq {
    /**
    * 报价摘要
    */
    @NotEmpty(message = "请输入需求摘要")
    private String details;

    /**
     * 请输入交付日期
     */
    @NotNull(message = "请输入交付日期")
    private Long deliveryTime;

    /**
     * 交付地址详情
     */
    @NotEmpty(message = "请输入交付地址详情")
    private String fullAddress;

    /**
     * 交付地址详情Id
     */
    @NotNull(message = "请输入交付地址详情Id")
    private Long fullAddressId;

    /**
     * 报价截止时间
     */
    @NotNull(message = "请输入报价截止时间")
    private Long quotationAsTime;

    /**
     * 被询价会员
     */
    @NotEmpty(message = "被询价会员不能为空")
    private String memberName;

    /**
    * 被询价会员ID
    */
    @NotNull(message = "被询价会员ID不能为空")
    private Long memberId;

    /**
     *被询价会员角色ID
     */
    @NotNull(message = "被询价会员角色ID不能为空")
    private Long  memberRoleId;

    /**
     *被询价会员角色名字
     */
    @NotEmpty(message = "被询价会员角色名字不能为空")
    private String  memberRoleName;

    /**
     * 询价联系人id
     */
    private Long contactId;

    /**
     * 询价联系人姓名
     */
    private String contactName;

    /**
     * 询价联系人电话国标码
     */
    private String phoneCode;

    /**
     * 询价联系人电话
     */
    private String contactPhone;

    /**
     * 报价要求
     */
    private String offer;

    /**
     * 付款方式
     */
    private String paymentType;

    /**
     * 税费要求
     */
    private String taxes;

    /**
     * 物流要求
     */
    private String logistics;

    /**
     * 包装要求
     */
    private String packRequire;

    /**
     * 其他要求
     */
    private String otherRequire;

    /**
     * 附件链接集合
     */
    private List<EnclosureUrlsReq> enclosureUrls;

    /**
     * 商城id
     */
    private Long shopId;

    /**
     * 商品列表
     */
    @Valid
    @NotEmpty(message = "商品列表不能为空")
    private List<InquiryListProductReq> inquiryListProductRequests;

}
