package com.ssy.lingxi.trade.api.model.req;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 查询报价询价单
 *
 * <AUTHOR>
 * @since 2022/3/23
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AppletInquiryListReq extends PageDataReq {
    private static final long serialVersionUID = 1850258153062843990L;

    /**
     * 关键字：询价单摘要、询价会员
     */
    private String keyword;

    /**
     * 排序类型
     * 0-默认排序，按照单据时间倒序排序
     * 1-按照交付时间正序排序
     * 2-按照交付时间倒序排序
     * 3-按照报价截止时间正序排序
     * 4-按照报价截止时间倒序排序
     */
    private int sortType;


}
