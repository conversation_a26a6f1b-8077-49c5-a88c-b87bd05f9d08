package com.ssy.lingxi.trade.api.model.req;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
* 交易中心-确认询价报价-待提交审核报价单
* <AUTHOR>
* @since 2020/8/20
*/
@Data
public class NotarizeEnquiryQuotedPriceSubmitReq {

    /**
     *确认询价报价单Id
     */
    @NotNull(message = "确认询价报价单Id不能为空")
    private Long id;

    /**
     *确认询价报价单审核状态：1.通过 0.不通过
     */
    private Integer state;

    /**
     *确认询价报价单Id原因
     */
    private String auditOpinion;

}
