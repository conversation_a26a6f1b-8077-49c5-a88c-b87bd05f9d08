package com.ssy.lingxi.common.util;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/27 20:44
 **/
public class ListUtil {

    /**
     * 平均拆分list方法.
     *
     * @param source source
     * @param n 分成n份
     * @param <T> 泛型
     * @return List<List<T>>
     */
    public static <T> List<List<T>> averageAssign(List<T> source, int n) {
        List<List<T>> result = new ArrayList<List<T>>();
        int remaider = source.size() % n;
        int number = source.size() / n;
        int offset = 0;//偏移量
        for (int i = 0; i < n; i++) {
            List<T> value = null;
            if (remaider > 0) {
                value = source.subList(i * number + offset, (i + 1) * number + offset + 1);
                remaider--;
                offset++;
            } else {
                value = source.subList(i * number + offset, (i + 1) * number + offset);
            }
            result.add(value);
        }
        return result;
    }

}
