package com.ssy.lingxi.scheduler;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 任务调度服务启动类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/19
 */
@EnableAsync
@EnableScheduling
@EnableAspectJAutoProxy(exposeProxy = true)
@EnableFeignClients(basePackages = {"com.ssy.lingxi.**.api.feign"})
@ComponentScan(basePackages = {
        "com.ssy.lingxi.component",
        "com.ssy.lingxi.**.api.fallback",
        "com.ssy.lingxi." + ServiceModuleConstant.SCHEDULER + ".component",
        "com.ssy.lingxi." + ServiceModuleConstant.SCHEDULER + ".config",
        "com.ssy.lingxi." + ServiceModuleConstant.SCHEDULER + ".controller",
        "com.ssy.lingxi." + ServiceModuleConstant.SCHEDULER + ".entity",
        "com.ssy.lingxi." + ServiceModuleConstant.SCHEDULER + ".handler",
        "com.ssy.lingxi." + ServiceModuleConstant.SCHEDULER + ".repository",
        "com.ssy.lingxi." + ServiceModuleConstant.SCHEDULER + ".serviceImpl",
})
@EnableDiscoveryClient
@SpringBootApplication
public class SchedulerServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(SchedulerServiceApplication.class, args);
    }

}
