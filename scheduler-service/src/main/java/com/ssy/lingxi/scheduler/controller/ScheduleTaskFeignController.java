package com.ssy.lingxi.scheduler.controller;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.scheduler.api.feign.IScheduleTaskFeign;
import com.ssy.lingxi.scheduler.api.model.req.*;
import com.ssy.lingxi.scheduler.api.model.resp.ScheduleTaskDefinitionResp;
import com.ssy.lingxi.scheduler.service.IScheduleTaskService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 定时任务相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-12
 */
@RestController
public class ScheduleTaskFeignController implements IScheduleTaskFeign {

    @Resource
    private IScheduleTaskService scheduleTaskService;

    /**
     * 创建定时任务
     * @param taskVO 接口参数
     * @return 定时任务的Id
     */
    @Override
    public WrapperResp<Long> createScheduleTask(@RequestBody @Valid ScheduleTaskReq taskVO) {
        return WrapperUtil.success(scheduleTaskService.createScheduleTask(taskVO));
    }

    /**
     * 创建永久任务
     * @param taskVO 接口参数
     * @return 定时任务的Id
     */
    @Override
    public WrapperResp<Long> createPermanentTask(@RequestBody @Valid PermanentTaskReq taskVO) {
        return WrapperUtil.success(scheduleTaskService.createPermanentTask(taskVO));
    }

    /**
     * 查询定时任务信息列表
     * @param queryVO 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<ScheduleTaskDefinitionResp>> listScheduleTask(@RequestBody @Valid ScheduleTaskQueryReq queryVO) {
        return WrapperUtil.success(scheduleTaskService.listScheduleTask(queryVO));
    }

    /**
     * 修改任务的启动时间
     * @param updateVO 接口参数
     * @return 修改结果
     */
    @Override
    public WrapperResp<Void> updateScheduleTaskExecTime(@RequestBody @Valid ScheduleTaskUpdateExecTimeReq updateVO) {
        scheduleTaskService.updateScheduleTaskExecTime(updateVO);
        return WrapperUtil.success();
    }

    /**
     * 调用方执行完成定时任务后，通知定时任务模块删除任务
     * @param idVO 接口参数
     * @return 通知结果
     */
    @Override
    public WrapperResp<Void> deleteScheduleTask(@RequestBody @Valid ScheduleTaskIdReq idVO) {
        scheduleTaskService.deleteScheduleTask(idVO);
        return WrapperUtil.success();
    }
}
