package com.ssy.lingxi.scheduler.api.model.req;

import com.ssy.lingxi.component.base.annotation.scheduler.TimestampAnnotation;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * 修改任务启动时间接口参数
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-14
 */
public class ScheduleTaskUpdateExecTimeReq implements Serializable {
    private static final long serialVersionUID = -4975993025367994877L;

    /**
     * 定时任务Id
     */
    @NotNull(message = "定时任务Id要大于0")
    @Positive(message = "定时任务Id要大于0")
    private Long taskId;

    /**
     * 任务开始执行的时间，（Unix时间戳格式，精确到毫秒）
     */
    @TimestampAnnotation
    private Long execTime;

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Long getExecTime() {
        return execTime;
    }

    public void setExecTime(Long execTime) {
        this.execTime = execTime;
    }
}
