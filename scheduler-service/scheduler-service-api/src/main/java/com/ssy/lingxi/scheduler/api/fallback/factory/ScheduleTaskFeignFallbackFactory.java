package com.ssy.lingxi.scheduler.api.fallback.factory;

import com.ssy.lingxi.scheduler.api.fallback.ScheduleTaskFeignFallback;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 定时任务OpenFeign接口Fallback
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-13
 */
@Component
public class ScheduleTaskFeignFallbackFactory implements FallbackFactory<ScheduleTaskFeignFallback> {

    @Override
    public ScheduleTaskFeignFallback create(Throwable cause) {
        return new ScheduleTaskFeignFallback(cause);
    }

}
