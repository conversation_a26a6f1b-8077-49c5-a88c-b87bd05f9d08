package com.ssy.lingxi.contract.api.model.req;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 采购询价合同新增VO
 * <AUTHOR>
 * @since 2021/3/4
 * @version 2.0.0
 */
public class PurchaseInquiryAddReq implements Serializable {
    private static final long serialVersionUID = 8601697760212063918L;

    /**
     * 需求会员id
     */
    @NotNull(message = "需求会员id需大于0")
    @Positive(message = "需求会员id需大于0")
    private Long demandMemberId;

    /**
     * 需求会员角色id
     */
    @NotNull(message = "需求会员角色id需大于0")
    @Positive(message = "需求会员角色id需大于0")
    private Long demandRoleId;

    /**
     * 需求单id
     */
    @NotNull(message = "需求单id需大于0")
    @Positive(message = "需求单id需大于0")
    private Long demandId;

    /**
     * 需求单号
     */
    @NotBlank(message = "需求单号不能为空")
    private String demandNO;

    /**
     * 需求摘要
     */
    private String demandAbstract;

    /**
     * 需求发布时间
     */
    @NotNull(message = "需求发布时间不能为空")
    private Long demandPublishTime;

    /**
     * 授标会员id
     */
    @NotNull(message = "授标会员id需大于0")
    @Positive(message = "授标会员id需大于0")
    private Long awardMemberId;

    /**
     * 授标会员角色id
     */
    @NotNull(message = "授标会员id需大于0")
    @Positive(message = "授标会员角色id需大于0")
    private Long awardRoleId;

    /**
     * 授标会员名称
     */
    @NotNull(message = "授标会员名称不能为空")
    private String awardName;

    /**
     * 授标时间
     */
    @NotNull(message = "授标时间不能为空")
    private Long awardTime;

    /**
     * 授标金额
     */
    @NotNull(message = "授标金额需大于0")
    @Positive(message = "授标金额需大于0")
    private BigDecimal awardAmount;

    /**
     * 内部状态
     */
    @NotNull(message = "内部状态不能为空")
    private Integer innerStatus;

    /**
     * 外部状态
     */
    @NotNull(message = "外部状态不能为空")
    private Integer outerStatus;
    /**
     *采购类型:1.单词采购 2.协议采购
     */
    @NotNull(message = "采购类型不能为空")
    private Integer purchaseType;
    /**
     *轮次
     */
    private Integer turn;

    /**
     *报价单ID
     */
    private Long id;

    /**
     *报价单号
     */
    private String orderNo;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getTurn() {
        return turn;
    }

    public void setTurn(Integer turn) {
        this.turn = turn;
    }

    public Long getDemandMemberId() {
        return demandMemberId;
    }

    public void setDemandMemberId(Long demandMemberId) {
        this.demandMemberId = demandMemberId;
    }

    public Long getDemandRoleId() {
        return demandRoleId;
    }

    public void setDemandRoleId(Long demandRoleId) {
        this.demandRoleId = demandRoleId;
    }

    public Long getDemandId() {
        return demandId;
    }

    public void setDemandId(Long demandId) {
        this.demandId = demandId;
    }

    public String getDemandNO() {
        return demandNO;
    }

    public void setDemandNO(String demandNO) {
        this.demandNO = demandNO;
    }

    public String getDemandAbstract() {
        return demandAbstract;
    }

    public void setDemandAbstract(String demandAbstract) {
        this.demandAbstract = demandAbstract;
    }

    public Long getDemandPublishTime() {
        return demandPublishTime;
    }

    public void setDemandPublishTime(Long demandPublishTime) {
        this.demandPublishTime = demandPublishTime;
    }

    public Long getAwardMemberId() {
        return awardMemberId;
    }

    public void setAwardMemberId(Long awardMemberId) {
        this.awardMemberId = awardMemberId;
    }

    public Long getAwardRoleId() {
        return awardRoleId;
    }

    public void setAwardRoleId(Long awardRoleId) {
        this.awardRoleId = awardRoleId;
    }

    public String getAwardName() {
        return awardName;
    }

    public void setAwardName(String awardName) {
        this.awardName = awardName;
    }

    public Long getAwardTime() {
        return awardTime;
    }

    public void setAwardTime(Long awardTime) {
        this.awardTime = awardTime;
    }

    public BigDecimal getAwardAmount() {
        return awardAmount;
    }

    public void setAwardAmount(BigDecimal awardAmount) {
        this.awardAmount = awardAmount;
    }

    public Integer getInnerStatus() {
        return innerStatus;
    }

    public void setInnerStatus(Integer innerStatus) {
        this.innerStatus = innerStatus;
    }

    public Integer getOuterStatus() {
        return outerStatus;
    }

    public void setOuterStatus(Integer outerStatus) {
        this.outerStatus = outerStatus;
    }

    public Integer getPurchaseType() {
        return purchaseType;
    }

    public void setPurchaseType(Integer purchaseType) {
        this.purchaseType = purchaseType;
    }
}
