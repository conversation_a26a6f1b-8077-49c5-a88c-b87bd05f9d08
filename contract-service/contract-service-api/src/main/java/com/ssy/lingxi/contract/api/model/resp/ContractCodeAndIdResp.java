package com.ssy.lingxi.contract.api.model.resp;

import java.io.Serializable;

/**
 * 合同id和合同编码对象
 * <AUTHOR>
 * @email <EMAIL>
 * @since 2022/6/9 18:08
 */
public class ContractCodeAndIdResp implements Serializable {
    private static final long serialVersionUID = -5211996081180173206L;

    /**
     * 合同id
     */
    private Long contractId;

    /**
     * 合同编码
     */
    private String contractNo;

    public Long getContractId() {
        return contractId;
    }

    public void setContractId(Long contractId) {
        this.contractId = contractId;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }
}
