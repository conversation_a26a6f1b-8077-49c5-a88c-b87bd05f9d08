package com.ssy.lingxi.contract.api.model.resp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 合同日报表
 * <AUTHOR>
 * @since 2021/6/10
 * @version 2.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContractDateTimeResp implements Serializable {

    private static final long serialVersionUID = -8832712315607362341L;

    /**
     * 数量
     */
    private Long count;

    /**
     * 采购金额
     */
    private BigDecimal amount;

}
