package com.ssy.lingxi.contract.api.feign;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.CommonNoReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.contract.api.fallback.factory.ContractFeignFallbackFactory;
import com.ssy.lingxi.contract.api.model.req.*;
import com.ssy.lingxi.contract.api.model.resp.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * 合同内部接口定义
 * <AUTHOR>
 * @since 2021/3/19
 * @version 2.0.0
 */
@FeignClient(name = ServiceModuleConstant.CONTRACT_SERVICE, fallbackFactory = ContractFeignFallbackFactory.class)
public interface IContractFeign {

    String PATH_PREFIX = ServiceModuleConstant.CONTRACT_FEIGN_PATH_PREFIX + "/";
    
    /**
     * 查询采购物料单价
     *
     * <AUTHOR>
     * @since 2021/3/19
     **/
    @PostMapping(PATH_PREFIX + "getPurchaseMaterielPriceList")
    WrapperResp<List<PurchaseMaterielPriceResp>> getPurchaseMaterielPriceList(@RequestBody @Valid PurchaseMaterielPriceReq requestVO);

    /**
     * 支付请款
     *
     * <AUTHOR>
     * @since 2021/3/24
     **/
    @GetMapping(PATH_PREFIX + "payApplyAmount")
    WrapperResp<Void> payApplyAmount(@RequestBody @Valid PayApplyAmountReq payVO);

    /**
     * 查询合同-合同乙方(请款单申请)
     *
     * <AUTHOR>
     * @since 2021/3/19
     **/
    @PostMapping(PATH_PREFIX + "page/by/partb")
    WrapperResp<PageDataResp<ContractPageResp>> pageContractByPartB(@RequestBody @Valid ContractPageDataReq requestVO);

    /**
     * 根据物料信息查询合同(商品能力- 物料价格库)
     *
     * <AUTHOR>
     * @since 2022/3/25
     **/
    @PostMapping(PATH_PREFIX + "materielPrice/by/materiel")
    WrapperResp<ContractPriceQueryResp> materielPriceByMateriel(@RequestBody @Valid ContrPriceQueryReq request);

    /**
     * 根据合同id查询合同物料剩余数量
     *
     * @param request 查询参数
     * @return 查询结果
     */
    @PostMapping(PATH_PREFIX + "materiel/list")
    WrapperResp<List<ContractMaterielListResp>> getMaterielList(@RequestBody @Valid ContractMaterielListReq request);

    /**
     * 根据合同id查询合同物料剩余数量(变更合同则返回新合同数量)
     *
     * @param request 查询参数
     * @return 查询结果
     */
    @PostMapping(PATH_PREFIX + "materiel/new/list")
    WrapperResp<List<ContractMaterielListResp>> getNewMaterielList(@RequestBody @Valid ContractMaterielListReq request);

    /**
     * 根据合同id批量查询返回合同id和合同编码
     *
     * @return 根据合同id批量查询返回合同id和合同编码
     */
    @PostMapping(PATH_PREFIX + "list/contract/code/and/id")
    WrapperResp<List<ContractCodeAndIdResp>> listContractCodeAndIdVOS(@RequestBody @Valid ContractIdReq contractIdReq);

    /**
     * 根据合同ids批量查询合同物料剩余数量
     *
     * @param request 查询参数
     * @return 查询结果
     */
    @PostMapping(PATH_PREFIX + "materiel/list/batch")
    WrapperResp<List<ContractMaterielListResp>> getMaterielListByContractIds(@RequestBody @Valid ContractMaterielListBatchReq request);

    /**
     * 根据合同id查询订单是否已经停用
     * @param commonIdReq 合同id查询条件
     * @return 查询结果
     */
    @PostMapping(PATH_PREFIX + "check/contract/is/change")
    WrapperResp<Boolean> checkContractIsChange(@RequestBody @Valid CommonIdReq commonIdReq);

    /**
     * 根据合同id查询该合同的变更合同是否正在变更（是否走完流程）
     * @param commonIdReq 合同id查询条件
     * @return true 正在处于变更中 false 已走完变更流程/不存在他的变更合同
     */
    @PostMapping(PATH_PREFIX + "check/subContract/is/change")
    WrapperResp<Boolean> checkSubContractIsChange(@RequestBody @Valid CommonIdReq commonIdReq);

    /**
     * 根据订单id查询最新合同
     * @param commonIdReq 订单id查询条件
     */
    @PostMapping(PATH_PREFIX + "getTheLastContractByOrderId")
    WrapperResp<ContractResp> getTheLastContractByOrderId(@RequestBody @Valid CommonIdReq commonIdReq);

    /**
     * 根据合同编号查询最新合同
     * @param commonNoReq 合同编号
     */
    @PostMapping(PATH_PREFIX + "getTheLastContractByContractNo")
    WrapperResp<ContractResp> getTheLastContractByContractNo(@RequestBody @Valid CommonNoReq commonNoReq);

    /**
     * 根据时间段查询已签约的合同
     * @param contractDateTimeReq 查询条件
     */
    @GetMapping(PATH_PREFIX + "getCompleteContractByDateTime")
    WrapperResp<ContractDateTimeResp> getCompleteContractByDateTime(@Valid ContractDateTimeReq contractDateTimeReq);
}
