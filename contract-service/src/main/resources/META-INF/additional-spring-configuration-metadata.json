{"properties": [{"sourceType": "com.ssy.lingxi.component.esign.config.EsignConfig", "name": "esign.appId", "type": "java.lang.String", "description": "E签宝appId"}, {"sourceType": "com.ssy.lingxi.component.esign.config.EsignConfig", "name": "esign.appSecret", "type": "java.lang.String", "description": "E签宝appSecret"}, {"sourceType": "com.ssy.lingxi.component.esign.config.EsignConfig", "name": "esign.sandBox", "type": "java.lang.Bo<PERSON>an", "description": "沙箱模式"}, {"sourceType": "com.ssy.lingxi.component.esign.config.EsignConfig", "name": "esign.notifyUrl", "type": "java.lang.String", "description": "回调url"}, {"sourceType": "com.ssy.lingxi.component.esign.config.EsignConfig", "name": "esign.debug", "type": "java.lang.Bo<PERSON>an", "description": "调试模式(打印日志)"}]}