package com.ssy.lingxi.contract.handler.listener;

import cn.hutool.json.JSONUtil;
import com.rabbitmq.client.Channel;
import com.ssy.lingxi.common.constant.mq.ContractMqConstant;
import com.ssy.lingxi.product.api.feign.IMaterielPriceFeign;
import com.ssy.lingxi.product.api.model.req.price.ContractPriceReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * 物料信息管理监听类
 */
@Slf4j
@Component
public class MaterielPriceListener {

    @Resource
    IMaterielPriceFeign materielPriceFeign;

    /**
     * 合同订单变更、到期、停用、作废, 同步更新物料价格库
     */
    @RabbitListener(queues = ContractMqConstant.MATERIEL_PRICE_CHANGE_QUEUE, ackMode ="MANUAL")
    public void materielPriceChangeReceive(Message message, Channel channel) throws IOException {
        ContractPriceReq request = JSONUtil.toBean(new String(message.getBody()), ContractPriceReq.class);
        log.info("合同变动,更新物料价格信息- 开始处理:接受内容:{}", JSONUtil.toJsonStr(request));
        try {
            materielPriceFeign.changeContractMaterielPrice(request);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            log.info("合同变动,更新物料价格信息- 处理完成");
        } catch (Exception e) {
            log.error("============合同变动,更新物料价格信息 MQ消费失败,尝试消息补发再次消费!==============");
            log.error(e.getMessage());
            channel.basicRecover(false);
        }

    }


}