package com.ssy.lingxi.contract.serviceImpl;


import com.ssy.lingxi.contract.entity.do_.SignatureDO;
import com.ssy.lingxi.contract.entity.do_.SignatureOrganizationAuthFlowDO;
import com.ssy.lingxi.contract.entity.do_.SignaturePersonalAuthFlowDO;
import com.ssy.lingxi.contract.enums.sign.SignatureAuthStateEnum;
import com.ssy.lingxi.contract.model.dto.SignAuthDetailDTO;
import com.ssy.lingxi.contract.repository.SignatureRepository;
import com.ssy.lingxi.contract.service.ISignatureUserDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 签章认证用户信息服务实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/1/28
 */
@Slf4j
@Service
public class SignatureUserDetailServiceImpl implements ISignatureUserDetailService {

    @Resource
    private SignatureRepository signatureRepository;

    @Override
    public SignAuthDetailDTO getSignAuthDetail(Long memberId) {
        // 签章申请信息
        SignatureDO signatureDO = signatureRepository.findByMemberId(memberId);
        if (Objects.isNull(signatureDO)) {
            return null;
        }

        SignAuthDetailDTO signAuthDetailDTO = new SignAuthDetailDTO();
        signAuthDetailDTO.setMemberId(memberId);
        //个人认证
        SignaturePersonalAuthFlowDO signaturePersonalAuthFlowDO = signatureDO.getSignaturePersonalAuthFlow();
        if(Objects.nonNull(signaturePersonalAuthFlowDO) && SignatureAuthStateEnum.APPROVE.getCode().equals(signaturePersonalAuthFlowDO.getStatus())){
            signAuthDetailDTO.setSignaturePersonal(signaturePersonalAuthFlowDO.getSignaturePersonal());
        }
        //机构认证
        SignatureOrganizationAuthFlowDO signatureOrganizationAuthFlowDO = signatureDO.getSignatureOrganizationAuthFlow();
        if(Objects.nonNull(signatureOrganizationAuthFlowDO) && SignatureAuthStateEnum.APPROVE.getCode().equals(signatureOrganizationAuthFlowDO.getStatus())){
            signAuthDetailDTO.setSignatureOrganization(signatureOrganizationAuthFlowDO.getSignatureOrganization());
        }
        return signAuthDetailDTO;
    }

//    @Override
//    public SignatureAuthLog getSignatureAuthLog(Long memberId) {
//        return null;
//        // 签章申请信息
//        SignatureAuthDO signatureAuthDO = signatureAuthRepository.findByMemberId(memberId);
//        if (Objects.isNull(signatureAuthDO)) {
//            return null;
//        }
//
//        // 认证信息
//        SignatureAuthLog signatureAuthLog = signatureAuthLogRepository.findById(signatureAuthDO.getSignatureAuthLogId()).orElse(null);
//        if (Objects.isNull(signatureAuthLog)) {
//            return null;
//        }
//
//        return signatureAuthLog;
//    }

    @Override
    public boolean silentSignAuth(SignAuthDetailDTO signAuthDetail) {
//        if (Objects.nonNull(signAuthDetail.getSignatureOrganization())) {
//            // 企业签署静默授权
//            SignatureOrganizationDO organization = signAuthDetail.getSignatureOrganization();
//            if (!Objects.isNull(organization)) {
//                if (CommonBooleanEnum.NO.getCode().equals(organization.getSilent())) {
//                    SilentSignSetReq silentSignSetReq = SilentSignSetReq.builder().accountId(organization.getOrgId()).build();
//                    try {
//                        esignApiUtil.account().silentSignSet(silentSignSetReq);
//                    } catch (EsignException e) {
//                        log.error("e签宝企业账号设置默认签署: {}", e.getError());
//                        return false;
//                    }
//
//                    // 设置成功,更新信息
//                    SignatureOrganizationDO saveOrg = signatureOrganizationRepository.findByOrgId(organization.getOrgId());
//                    if (Objects.nonNull(saveOrg)) {
//                        saveOrg.setSilent(CommonBooleanEnum.YES.getCode());
//                        signatureOrganizationRepository.saveAndFlush(saveOrg);
//                    }
//                }
//            }
//
//            // 关联人静默授权
//            SignaturePersonalDO account = signAuthDetail.getAccount();
//            if (!Objects.isNull(account)) {
//                if (CommonBooleanEnum.NO.getCode().equals(account.getSilent())) {
//                    SilentSignSetReq silentSignSetReq = SilentSignSetReq.builder().accountId(account.getPsnId()).build();
//                    try {
//                        esignApiUtil.account().silentSignSet(silentSignSetReq);
//                    } catch (EsignException e) {
//                        log.error("e签宝企业账号关联人设置默认签署: {}", e.getError());
//                        return false;
//                    }
//
//                    // 设置成功,更新信息
//                    SignaturePersonalDO saveAccount = signaturePersonalRepository.findByAccountId(account.getPsnId());
//                    if (Objects.nonNull(saveAccount)) {
//                        saveAccount.setSilent(CommonBooleanEnum.YES.getCode());
//                        signaturePersonalRepository.saveAndFlush(saveAccount);
//                    }
//                }
//            }
//
//            return true;
//        }
//
//        if (AuthTypeEnum.PERSONAL.getCode().equals(signAuthDetail.getAuthType())) {
//            // 个人签署静默授权
//            SignaturePersonalDO account = signAuthDetail.getAccount();
//            if (!Objects.isNull(account)) {
//                if (CommonBooleanEnum.NO.getCode().equals(account.getSilent())) {
//                    SilentSignSetReq silentSignSetReq = SilentSignSetReq.builder().accountId(account.getPsnId()).build();
//                    try {
//                        esignApiUtil.account().silentSignSet(silentSignSetReq);
//                    } catch (EsignException e) {
//                        log.error("e签宝个人账号设置默认签署: {}", e.getError());
//                        return false;
//                    }
//
//                    // 设置成功,更新信息
//                    SignaturePersonalDO saveAccount = signaturePersonalRepository.findByAccountId(account.getPsnId());
//                    if (Objects.nonNull(saveAccount)) {
//                        saveAccount.setSilent(CommonBooleanEnum.YES.getCode());
//                        signaturePersonalRepository.saveAndFlush(saveAccount);
//                    }
//                }
//                return true;
//            }
//        }

        return false;
    }
}