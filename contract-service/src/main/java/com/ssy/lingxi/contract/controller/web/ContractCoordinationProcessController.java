package com.ssy.lingxi.contract.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.contract.model.req.*;
import com.ssy.lingxi.contract.model.resp.ContractBaseProcessWebResp;
import com.ssy.lingxi.contract.model.resp.ContractCoordinationProcessDetailResp;
import com.ssy.lingxi.contract.model.resp.ContractCoordinationProcessPageQueryWebResp;
import com.ssy.lingxi.contract.model.resp.ContractProcessResp;
import com.ssy.lingxi.contract.service.web.IContractCoordinationProcessWebService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * 能力中心 - 合同协同流程配置相关接口
 * <AUTHOR>
 * @since 2022/4/25
 **/
@RestController
@RequestMapping(ServiceModuleConstant.CONTRACT_PATH_PREFIX + "/coordination/process")
public class ContractCoordinationProcessController extends BaseController {

    @Resource
    private IContractCoordinationProcessWebService service;

    /**
     * 分页查询协同流程规则
     * @param request
     * @param pageVO
     * @return
     */
    @GetMapping("/page")
    public WrapperResp<PageDataResp<ContractCoordinationProcessPageQueryWebResp>> pageContractCoordinationProcess(HttpServletRequest request, ContractProcessPageDataReq pageVO){
        return WrapperUtil.success(service.pageContractCoordinationProcess(getSysUser(request),pageVO));
    }

    /**
     * 新增合同协同规则页面 - 查询基础合同协同流程列表
     * @param request
     * @return
     */
    @GetMapping("/base/list")
    public WrapperResp<List<ContractBaseProcessWebResp>> listBaseContractProcess(HttpServletRequest request) {
        return WrapperUtil.success(service.listBaseContractProcess(getSysUser(request)));
    }

    /**
     * 新增合同协同流程
     * @param request
     * @param saveVO
     * @return
     */
    @PostMapping("/save")
    public WrapperResp<Void> save(HttpServletRequest request, @RequestBody @Valid ContractCoordinationProcessSaveWebReq saveVO){
        service.save(getSysUser(request),saveVO);
        return WrapperUtil.success();
    }

    /**
     * 查询合同协同流程规则详细
     * @param request
     * @param processId
     * @return
     */
    @GetMapping("/get")
    public WrapperResp<ContractCoordinationProcessDetailResp> getInfo(HttpServletRequest request, @RequestParam("processId") Long processId){
        return WrapperUtil.success(service.getInfo(processId));
    }


    /**
     * 修改合同协同流程规则
     * @param request
     * @param updateVO
     * @return
     */
    @PostMapping("/update")
    public WrapperResp<Void> update(HttpServletRequest request, @RequestBody @Valid ContractCoordinationProcessUpdateReq updateVO){
        service.update(getSysUser(request),updateVO);
        return WrapperUtil.success();
    }

    /**
     * 修改合同协同流程规则状态
     * @param request
     * @param updateStatusVO
     * @return
     */
    @PostMapping("/status/update")
    public WrapperResp<Void> updateStatus(HttpServletRequest request, @RequestBody @Valid ContractProcessUpdateStatusReq updateStatusVO){
        service.updateStatus(getSysUser(request),updateStatusVO);
        return WrapperUtil.success();
    }



    /**
     * 删除合同协同流程
     * @param request
     * @param processId
     * @return
     */
    @GetMapping("/delete")
    public WrapperResp<Void> delete(HttpServletRequest request, @RequestParam("processId")  Long processId){
        service.delete(getSysUser(request),processId);
        return WrapperUtil.success();
    }


    /**
     * 获取合同管理可用流程
     * @param request
     * @param workflowVO
     * @return
     */
    @PostMapping("/getContractProcessWorkflow")
    public WrapperResp<ContractProcessResp> getContractProcessWorkflow(HttpServletRequest request, @RequestBody ContractCoordinationWorkflowReq workflowVO){
        return WrapperUtil.success(service.getContractProcessDTO(getSysUser(request),workflowVO));
    }
}
