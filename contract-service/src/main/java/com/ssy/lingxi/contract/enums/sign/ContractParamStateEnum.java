package com.ssy.lingxi.contract.enums.sign;

/**
 * 合同参数停用/启用状态枚举
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/10
 */
public enum ContractParamStateEnum {
    /**
     * 停用 - 0
     */
    DISABLE(0, "停用"),

    /**
     * 启用 - 1
     */
    ENABLE(1, "启用");

    private final Integer code;
    private final String message;


    ContractParamStateEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

}
