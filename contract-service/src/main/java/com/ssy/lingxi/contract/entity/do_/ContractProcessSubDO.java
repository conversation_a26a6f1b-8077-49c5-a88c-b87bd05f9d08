package com.ssy.lingxi.contract.entity.do_;

import com.ssy.lingxi.common.constant.TableNameConstant;
import com.ssy.lingxi.contract.enums.process.ContractProcessStatusEnum;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 能力中心 - 合同审核流程规则配置
 * <AUTHOR>
 * @since 2022/4/26
 **/
@Entity
@Table(name = TableNameConstant.TABLE_PRE_CONTRACT_SERVICE + "contract_process_sub",schema = TableNameConstant.TABLE_SCHEMA)
public class ContractProcessSubDO implements Serializable {

    private static final long serialVersionUID = 8946339796714292724L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 流程名称
     */
    @Column(columnDefinition = "varchar(200)")
    private String name;

    /**
     * 状态
     * 定义枚举 ContractProcessStatusEnum
     * @see ContractProcessStatusEnum
     */
    @Column
    private Integer status;

    /**
     * 创建时间
     */
    @Column(columnDefinition = "timestamp")
    private LocalDateTime createTime;

    /**
     * 创建人id
     */
    @Column
    private Long createMemberId;

    /**
     * 创建人role
     */
    @Column
    private Long createRoleId;

//    /**
//     * 是否是全部会员
//     */
//    @Column
//    private Boolean allMembers;

//    /**
//     * 一对一 对应平台给会员配置的合同审核流程
//     */
//    @OneToOne(cascade = CascadeType.DETACH)
//    @JoinColumn(name = "contract_process_id", referencedColumnName = "id")
//    private ContractProcessDO contractProcessDO;


    /**
     * 一对一 对应基础合同审核流程
     */
    @OneToOne(cascade = CascadeType.DETACH)
    @JoinColumn(name = "base_contract_process_id", referencedColumnName = "id")
    private BaseContractProcessDO baseContractProcessDO;

    /**
     * 流程对应的工作流key
     */
    @Column(columnDefinition = "varchar(200)")
    private String processKey;

    /**
     * 流程类型
     * 定义在 ContractProcessTypeEnum
     */
    @Column
    private Integer processType;


    /**
     * 流程图片url
     */
    @Column
    private String processImgUrl;

    /**
     * 引擎ID
     */
    @Column(columnDefinition = "int8")
    private Long engineId;

    /**
     * 来源 0.系统 1.Paas
     */
    @Column(columnDefinition = "int")
    private Integer source;

    /**
     * 是否默认流程 0.非默认 1.默认
     */
    @Column(columnDefinition = "int")
    private Integer isDefault;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getCreateMemberId() {
        return createMemberId;
    }

    public void setCreateMemberId(Long createMemberId) {
        this.createMemberId = createMemberId;
    }

    public Long getCreateRoleId() {
        return createRoleId;
    }

    public void setCreateRoleId(Long createRoleId) {
        this.createRoleId = createRoleId;
    }

    public BaseContractProcessDO getBaseContractProcessDO() {
        return baseContractProcessDO;
    }

    public void setBaseContractProcessDO(BaseContractProcessDO baseContractProcessDO) {
        this.baseContractProcessDO = baseContractProcessDO;
    }

    public String getProcessImgUrl() {
        return processImgUrl;
    }

    public void setProcessImgUrl(String processImgUrl) {
        this.processImgUrl = processImgUrl;
    }

    public Long getEngineId() {
        return engineId;
    }

    public void setEngineId(Long engineId) {
        this.engineId = engineId;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Integer getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }

    public String getProcessKey() {
        return processKey;
    }

    public void setProcessKey(String processKey) {
        this.processKey = processKey;
    }

    public Integer getProcessType() {
        return processType;
    }

    public void setProcessType(Integer processType) {
        this.processType = processType;
    }
}
