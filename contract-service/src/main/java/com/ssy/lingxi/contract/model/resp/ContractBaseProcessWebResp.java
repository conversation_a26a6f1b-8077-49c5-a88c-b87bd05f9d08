package com.ssy.lingxi.contract.model.resp;

/**
 * 能力中心 - 分页查询合同协同流程配置
 * <AUTHOR>
 * @since 2022/4/26
 **/
public class ContractBaseProcessWebResp extends ContractProcessPageQueryResp {

    private static final long serialVersionUID = -5682992343554779999L;

    /**
     * 流程名称
     */
    private String processName;

    /**
     * 流程说明
     */
    private String description;

    /**
     * 流程类型
     */
    private Integer processType;

    /**
     * 流程规则类型：
     * ContractProcessTypeEnum
     */
    private String processTypeName;

    public String getProcessName() {
        return processName;
    }

    public void setProcessName(String processName) {
        this.processName = processName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public Integer getProcessType() {
        return processType;
    }

    @Override
    public void setProcessType(Integer processType) {
        this.processType = processType;
    }

    public String getProcessTypeName() {
        return processTypeName;
    }

    public void setProcessTypeName(String processTypeName) {
        this.processTypeName = processTypeName;
    }
}

