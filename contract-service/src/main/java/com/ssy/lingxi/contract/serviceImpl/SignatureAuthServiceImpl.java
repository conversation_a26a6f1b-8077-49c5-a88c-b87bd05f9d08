package com.ssy.lingxi.contract.serviceImpl;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.esign.common.constant.ApiNotifyUrlV3Constant;
import com.ssy.lingxi.component.esign.common.enums.ClientTypeEnum;
import com.ssy.lingxi.component.esign.common.enums.OrganizationAuthorizedScopeEnum;
import com.ssy.lingxi.component.esign.common.enums.PersonalAuthorizedScopeEnum;
import com.ssy.lingxi.component.esign.exception.EsignException;
import com.ssy.lingxi.component.esign.model.req.v3.OrganizationAuthV3Req;
import com.ssy.lingxi.component.esign.model.req.v3.PersonalAuthV3Req;
import com.ssy.lingxi.component.esign.model.resp.v3.AuthV3Resp;
import com.ssy.lingxi.component.esign.model.resp.v3.notify.NotifyAuthV3Resp;
import com.ssy.lingxi.component.esign.model.resp.v3.notify.NotifyRealNameV3Resp;
import com.ssy.lingxi.component.esign.util.EsignApiV3Util;
import com.ssy.lingxi.contract.entity.do_.*;
import com.ssy.lingxi.contract.enums.sign.SignatureAuthStateEnum;
import com.ssy.lingxi.contract.model.req.sign.OrgAuthReq;
import com.ssy.lingxi.contract.model.req.sign.PersonalAuthReq;
import com.ssy.lingxi.contract.model.resp.sign.SignatureAuthResp;
import com.ssy.lingxi.contract.repository.*;
import com.ssy.lingxi.contract.service.ISignatureAuthService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 电子签章服认证务实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/9/1
 */
@Slf4j
@Service
public class SignatureAuthServiceImpl implements ISignatureAuthService {

    @Resource
    private SignatureRepository signatureRepository;
    @Resource
    private SignaturePersonalRepository signaturePersonalRepository;
    @Resource
    private SignatureOrganizationRepository signatureOrganizationRepository;
    @Resource
    private SignaturePersonalAuthFlowRepository signaturePersonalAuthFlowRepository;
    @Resource
    private SignatureOrganizationAuthFlowRepository signatureOrganizationAuthFlowRepository;
    @Resource
    private EsignApiV3Util esignApiV3Util;

    /**
     * 个人认证
     * @param personalAuthReq   参数
     * @param sysUser           登录用户
     */
    @Override
    @Transactional
    public String personalAuth(PersonalAuthReq personalAuthReq, UserLoginCacheDTO sysUser) {
        //检查数据库中是否存在认证信息
        SignatureDO signatureDO = signatureRepository.findByMemberId(sysUser.getMemberId());
        if(Objects.isNull(signatureDO)){
            signatureDO = new SignatureDO();
        }
        SignaturePersonalAuthFlowDO signaturePersonalAuthFlowDO = signatureDO.getSignaturePersonalAuthFlow();
        if(Objects.isNull(signaturePersonalAuthFlowDO)){
            signaturePersonalAuthFlowDO = new SignaturePersonalAuthFlowDO();
        }
        SignaturePersonalDO signaturePersonalDO = signaturePersonalAuthFlowDO.getSignaturePersonal();
        if(Objects.isNull(signaturePersonalDO)){
            signaturePersonalDO = new SignaturePersonalDO();
        }

        AuthV3Resp authV3Resp;
        try {
            //个人手机号
            personalAuthReq.setPsnMobile(personalAuthReq.getPsnMobile() == null ? sysUser.getUserPhone() : personalAuthReq.getPsnMobile());

            //请求e签宝获取认证链接
            authV3Resp = getPersonalAuthUrl(personalAuthReq);

            //电子签章 → 个人认证授权表 → 个人认证信息
            signaturePersonalDO.setPsnAccount(personalAuthReq.getPsnMobile());
            signaturePersonalDO.setPsnName(personalAuthReq.getPsnName());
            signaturePersonalDO.setPsnMobile(personalAuthReq.getPsnMobile());
            signaturePersonalDO.setPsnIDCardType(personalAuthReq.getPsnIDCardType());
            signaturePersonalDO.setPsnIDCardNum(personalAuthReq.getPsnIDCardNum());
            signaturePersonalDO.setCreateTime(System.currentTimeMillis());

            //电子签章 → 个人认证授权表
            signaturePersonalAuthFlowDO.setAuthUrl(authV3Resp.getAuthUrl());
            signaturePersonalAuthFlowDO.setAuthFlowId(authV3Resp.getAuthFlowId());
            signaturePersonalAuthFlowDO.setSignaturePersonal(signaturePersonalDO);
            signaturePersonalAuthFlowDO.setStatus(SignatureAuthStateEnum.APPLYING.getCode());
            signaturePersonalAuthFlowDO.setCreateTime(System.currentTimeMillis());

            //电子签章主表
            signatureDO.setMemberId(sysUser.getMemberId());
            signatureDO.setSignaturePersonalAuthFlow(signaturePersonalAuthFlowDO);
            signatureDO.setCreateTime(System.currentTimeMillis());
            signatureRepository.saveAndFlush(signatureDO);
        } catch (EsignException e) {
            log.error("e签宝->获取认证链接异常：", e);
            throw new BusinessException(e.getMessage());
        }
        return authV3Resp.getAuthUrl();
    }

    /**
     * 获取个人认证&授权url
     */
    private AuthV3Resp getPersonalAuthUrl(PersonalAuthReq personalAuthReq) throws EsignException {
        //个人身份附加信息
        PersonalAuthV3Req.PsnInfo psnInfo = new PersonalAuthV3Req.PsnInfo();
        psnInfo.setPsnName(personalAuthReq.getPsnName());
        psnInfo.setPsnMobile(personalAuthReq.getPsnMobile());
        psnInfo.setPsnIDCardNum(personalAuthReq.getPsnIDCardNum());
        psnInfo.setPsnIDCardType(personalAuthReq.getPsnIDCardType());
        psnInfo.setPsnIdentityVerify(Boolean.TRUE);

        //个人实名认证配置项
        PersonalAuthV3Req.PsnAuthConfig psnAuthConfig = new PersonalAuthV3Req.PsnAuthConfig();
        psnAuthConfig.setPsnAccount(personalAuthReq.getPsnMobile());
        psnAuthConfig.setPsnInfo(psnInfo);

        //个人授权配置项
        List<String> authorizedScopes = new ArrayList<>();
        authorizedScopes.add(PersonalAuthorizedScopeEnum.GET_PSN_IDENTITY_INFO.getType());
        authorizedScopes.add(PersonalAuthorizedScopeEnum.PSN_INITIATE_SIGN.getType());
        authorizedScopes.add(PersonalAuthorizedScopeEnum.MANAGE_PSN_RESOURCE.getType());
        PersonalAuthV3Req.AuthorizeConfig authorizeConfig = new PersonalAuthV3Req.AuthorizeConfig();
        authorizeConfig.setAuthorizedScopes(authorizedScopes);

        //认证完成重定向配置项
        PersonalAuthV3Req.RedirectConfig redirectConfig = new PersonalAuthV3Req.RedirectConfig();
        redirectConfig.setRedirectUrl(checkRedirectUrl(personalAuthReq.getRedirectUrl()));

        //请求e签宝获取个人认证&授权链接
        PersonalAuthV3Req personalAuthV3Req = new PersonalAuthV3Req();
        personalAuthV3Req.setPsnAuthConfig(psnAuthConfig);
        personalAuthV3Req.setAuthorizeConfig(authorizeConfig);
        personalAuthV3Req.setRedirectConfig(redirectConfig);
        personalAuthV3Req.setNotifyUrl(esignApiV3Util.getProperties().getNotifyUrl() + ServiceModuleConstant.CONTRACT_PATH_PREFIX + ApiNotifyUrlV3Constant.AUTH_INDEX);
        personalAuthV3Req.setClientType(ClientTypeEnum.ALL.getType());
        return esignApiV3Util.identityAuthV3().getPersonalAuthUrl(personalAuthV3Req);
    }

    /**
     * 企业认证
     * @param orgAuthReq   参数
     * @param sysUser   登录用户
     */
    @Override
    public String orgAuth(OrgAuthReq orgAuthReq, UserLoginCacheDTO sysUser) {
        //检查数据库中是否存在认证信息
        SignatureDO signatureDO = signatureRepository.findByMemberId(sysUser.getMemberId());
        if(Objects.isNull(signatureDO)){
            signatureDO = new SignatureDO();
        }
        SignatureOrganizationAuthFlowDO signatureOrganizationAuthFlowDO = signatureDO.getSignatureOrganizationAuthFlow();
        if(Objects.isNull(signatureOrganizationAuthFlowDO)){
            signatureOrganizationAuthFlowDO = new SignatureOrganizationAuthFlowDO();
        }
        SignatureOrganizationDO signatureOrganization = signatureOrganizationAuthFlowDO.getSignatureOrganization();
        if(Objects.isNull(signatureOrganization)){
            signatureOrganization = new SignatureOrganizationDO();
        }

        AuthV3Resp authV3Resp;

        try {
            //请求e签宝获取认证链接
            authV3Resp = getOrgAuthUrl(orgAuthReq);

            //电子签章 → 机构认证授权表 → 组织认证信息
            signatureOrganization.setOrgName(orgAuthReq.getOrgName());
            signatureOrganization.setOrgIDCardType(orgAuthReq.getOrgIDCardType());
            signatureOrganization.setOrgIDCardNum(orgAuthReq.getOrgIDCardNum());
            signatureOrganization.setLegalRepName(orgAuthReq.getLegalRepName());
            signatureOrganization.setLegalRepIDCardType(orgAuthReq.getLegalRepIDCardType());
            signatureOrganization.setLegalRepIDCardNum(orgAuthReq.getLegalRepIdCardNum());
            signatureOrganization.setLegalRepMobile(orgAuthReq.getLegalRepMobile());
            signatureOrganization.setCreateTime(System.currentTimeMillis());

            //电子签章 → 机构认证授权表 → 经办人信息 → 个人认证信息
            SignaturePersonalDO signaturePersonal = new SignaturePersonalDO();
            signaturePersonal.setPsnAccount(orgAuthReq.getTransactorMobile());
            signaturePersonal.setPsnName(orgAuthReq.getTransactorName());
            signaturePersonal.setPsnIDCardType(orgAuthReq.getTransactorIDCardType());
            signaturePersonal.setPsnIDCardNum(orgAuthReq.getTransactorIdCardNum());
            signaturePersonal.setPsnMobile(orgAuthReq.getTransactorMobile());
            signaturePersonal.setPsnBankCardNum(orgAuthReq.getTransactorBankCardNum());
            signaturePersonal.setCreateTime(System.currentTimeMillis());

            //电子签章 → 机构认证授权表 → 经办人信息
            List<SignatureTransactorDO> signatureTransactorList = new ArrayList<>();
            SignatureTransactorDO signatureTransactorDO = new SignatureTransactorDO();
            signatureTransactorDO.setSignaturePersonal(signaturePersonal);
            signatureTransactorDO.setSignatureOrganizationAuthFlow(signatureOrganizationAuthFlowDO);
            signatureTransactorDO.setCreateTime(System.currentTimeMillis());
            signatureTransactorList.add(signatureTransactorDO);

            //电子签章 → 机构认证授权表 → 经办人信息(如果存在旧数据，进行清除)
            List<SignatureTransactorDO> signatureTransactorDOList = signatureOrganizationAuthFlowDO.getSignatureTransactorList();
            if(!CollectionUtils.isEmpty(signatureTransactorDOList)){
                //调用了clear()方法，不能用set，换成addAll()
                signatureTransactorDOList.clear();
                signatureTransactorDOList.addAll(signatureTransactorList);
            }else{
                signatureOrganizationAuthFlowDO.setSignatureTransactorList(signatureTransactorList);
            }

            //电子签章 → 机构认证授权表
            signatureOrganizationAuthFlowDO.setAuthUrl(authV3Resp.getAuthUrl());
            signatureOrganizationAuthFlowDO.setAuthFlowId(authV3Resp.getAuthFlowId());
            signatureOrganizationAuthFlowDO.setSignatureOrganization(signatureOrganization);
            signatureOrganizationAuthFlowDO.setStatus(SignatureAuthStateEnum.APPLYING.getCode());
            signatureOrganizationAuthFlowDO.setCreateTime(System.currentTimeMillis());

            //电子签章主表
            signatureDO.setMemberId(sysUser.getMemberId());
            signatureDO.setSignatureOrganizationAuthFlow(signatureOrganizationAuthFlowDO);
            signatureDO.setCreateTime(System.currentTimeMillis());

            signatureRepository.saveAndFlush(signatureDO);
        } catch (EsignException e) {
            log.error("e签宝->企业获取认证链接异常：",e);
            throw new BusinessException(e.getMessage());
        }
        return authV3Resp.getAuthUrl();
    }

    /**
     * 获取企业认证url
     */
    private AuthV3Resp getOrgAuthUrl(OrgAuthReq orgAuthReq) throws EsignException {
        //组织机构身份附加信息
        OrganizationAuthV3Req.OrgInfo orgInfo = new OrganizationAuthV3Req.OrgInfo();
        orgInfo.setOrgIDCardType(orgAuthReq.getOrgIDCardType());
        orgInfo.setOrgIDCardNum(orgAuthReq.getOrgIDCardNum());
        orgInfo.setLegalRepName(orgAuthReq.getLegalRepName());
        orgInfo.setLegalRepIDCardNum(orgAuthReq.getLegalRepIdCardNum());
        orgInfo.setLegalRepIDCardType(orgAuthReq.getLegalRepIDCardType());

        //组织机构认证配置项
        OrganizationAuthV3Req.OrgAuthConfig orgAuthConfig = new OrganizationAuthV3Req.OrgAuthConfig();
        orgAuthConfig.setOrgName(orgAuthReq.getOrgName());
        orgAuthConfig.setOrgInfo(orgInfo);

        //经办人身份信息
        OrganizationAuthV3Req.PsnInfo psnInfo = new OrganizationAuthV3Req.PsnInfo();
        psnInfo.setPsnName(orgAuthReq.getTransactorName());
        psnInfo.setPsnMobile(orgAuthReq.getTransactorMobile());
        psnInfo.setPsnIDCardNum(orgAuthReq.getTransactorIdCardNum());
        psnInfo.setPsnIDCardType(orgAuthReq.getTransactorIDCardType());
        psnInfo.setPsnIdentityVerify(Boolean.TRUE);
        OrganizationAuthV3Req.TransactorInfo transactorInfo = new OrganizationAuthV3Req.TransactorInfo();
        transactorInfo.setPsnInfo(psnInfo);
        orgAuthConfig.setTransactorInfo(transactorInfo);

        //机构授权配置项
        List<String> authorizedScopes = new ArrayList<>();
        authorizedScopes.add(OrganizationAuthorizedScopeEnum.GET_ORG_IDENTITY_INFO.getType());
        authorizedScopes.add(OrganizationAuthorizedScopeEnum.GET_PSN_IDENTITY_INFO.getType());
        authorizedScopes.add(OrganizationAuthorizedScopeEnum.ORG_INITIATE_SIGN.getType());
        authorizedScopes.add(OrganizationAuthorizedScopeEnum.PSN_INITIATE_SIGN.getType());
        OrganizationAuthV3Req.AuthorizeConfig authorizeConfig = new OrganizationAuthV3Req.AuthorizeConfig();
        authorizeConfig.setAuthorizedScopes(authorizedScopes);

        //认证完成重定向配置项
        OrganizationAuthV3Req.RedirectConfig redirectConfig = new OrganizationAuthV3Req.RedirectConfig();
        redirectConfig.setRedirectUrl(checkRedirectUrl(orgAuthReq.getRedirectUrl()));

        //请求e签宝获取机构认证链接
        OrganizationAuthV3Req organizationAuthV3Req = new OrganizationAuthV3Req();
        organizationAuthV3Req.setOrgAuthConfig(orgAuthConfig);
        organizationAuthV3Req.setAuthorizeConfig(authorizeConfig);
        organizationAuthV3Req.setRedirectConfig(redirectConfig);
        organizationAuthV3Req.setClientType(ClientTypeEnum.ALL.getType());
        organizationAuthV3Req.setNotifyUrl(esignApiV3Util.getProperties().getNotifyUrl() + ServiceModuleConstant.CONTRACT_PATH_PREFIX + ApiNotifyUrlV3Constant.AUTH_INDEX);

        return esignApiV3Util.identityAuthV3().getOrganizationAuthUrl(organizationAuthV3Req);
    }

    /**
     * 校验重定向地址
     * @param redirectUrl   前端路径
     */
    private String checkRedirectUrl(String redirectUrl){
        if(StringUtils.isEmpty(redirectUrl)){
            return null;
        }

        //校验是否http或者https开头
        if(redirectUrl.startsWith("http") || redirectUrl.startsWith("https")){
            return redirectUrl;
        }

        throw new BusinessException(ResponseCodeEnum.REQUEST_PARAM_CHECK_FAILED);
    }

    /**
     * 实名认证通过回调
     * @param notifyRealNameV3Req  通知内容
     */
    @Override
    @Transactional
    public boolean realNameNotify(NotifyRealNameV3Resp notifyRealNameV3Req) {
        String authFlowId = notifyRealNameV3Req.getAuthFlowId();
        NotifyRealNameV3Resp.PsnInfo psnInfo = notifyRealNameV3Req.getPsnInfo();
        NotifyRealNameV3Resp.Organization organization = notifyRealNameV3Req.getOrganization();

        //机构认证
        SignatureOrganizationAuthFlowDO signatureOrganizationAuthFlowDO = signatureOrganizationAuthFlowRepository.findByAuthFlowIdAndStatus(authFlowId, SignatureAuthStateEnum.APPLYING.getCode());
        if(Objects.nonNull(signatureOrganizationAuthFlowDO) && Objects.nonNull(organization)){
            //机构认证信息
            SignatureOrganizationDO signatureOrganization = signatureOrganizationAuthFlowDO.getSignatureOrganization();
            if(Objects.nonNull(signatureOrganization)){
                signatureOrganization.setOrgId(organization.getOrgId());
                //经办人信息
                NotifyRealNameV3Resp.Transactor transactor = organization.getTransactor();
                if(Objects.nonNull(transactor) && Objects.nonNull(transactor.getPsnAccount())){
                    List<SignatureTransactorDO> existSignatureTransactorList = signatureOrganizationAuthFlowDO.getSignatureTransactorList();
                    if(!CollectionUtils.isEmpty(existSignatureTransactorList)){
                        existSignatureTransactorList.forEach(signatureTransactorDO -> {
                            SignaturePersonalDO signaturePersonal = signatureTransactorDO.getSignaturePersonal();
                            if(StringUtils.isEmpty(signaturePersonal.getPsnId()) && signaturePersonal.getPsnAccount().equals(transactor.getPsnAccount().getAccountMobile())){
                                signaturePersonal.setPsnId(transactor.getPsnId());
                            }
                        });
                    }
                }
            }

            signatureOrganizationAuthFlowRepository.saveAndFlush(signatureOrganizationAuthFlowDO);

            return Boolean.TRUE;
        }

        //个人认证
        SignaturePersonalAuthFlowDO signaturePersonalAuthFlowDO = signaturePersonalAuthFlowRepository.findByAuthFlowIdAndStatus(authFlowId, SignatureAuthStateEnum.APPLYING.getCode());
        if(Objects.nonNull(signaturePersonalAuthFlowDO) && Objects.nonNull(psnInfo)){
            //个人认证信息
            SignaturePersonalDO signaturePersonalDO = signaturePersonalRepository.findByPsnId(psnInfo.getPsnId());
            if(Objects.nonNull(signaturePersonalDO)){
                //如果存在个人认证信息，进行换绑，删掉临时数据
                signaturePersonalAuthFlowDO.setSignaturePersonal(signaturePersonalDO);
                signaturePersonalRepository.delete(signaturePersonalAuthFlowDO.getSignaturePersonal());
            }else{
                SignaturePersonalDO newSignaturePersonalDO = signaturePersonalAuthFlowDO.getSignaturePersonal();
                if(Objects.nonNull(newSignaturePersonalDO)){
                    newSignaturePersonalDO.setPsnId(psnInfo.getPsnId());
                    signaturePersonalAuthFlowDO.setSignaturePersonal(newSignaturePersonalDO);
                }
            }

            signaturePersonalAuthFlowRepository.saveAndFlush(signaturePersonalAuthFlowDO);

            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    /**
     * 授权完成回调
     * @param notifyAuthV3Req  通知内容
     */
    @Override
    public boolean authNotify(NotifyAuthV3Resp notifyAuthV3Req) {
        String psnId = notifyAuthV3Req.getPsnId();
        String authFlowId = notifyAuthV3Req.getAuthFlowId();
        List<NotifyAuthV3Resp.AuthorizedInfo> authorizedInfoList = notifyAuthV3Req.getAuthorizedInfo();

        //企业认证
        SignatureOrganizationAuthFlowDO signatureOrganizationAuthFlowDO = signatureOrganizationAuthFlowRepository.findByAuthFlowIdAndStatus(authFlowId, SignatureAuthStateEnum.APPLYING.getCode());
        if(Objects.nonNull(signatureOrganizationAuthFlowDO)){
            signatureOrganizationAuthFlowDO.setStatus(SignatureAuthStateEnum.APPROVE.getCode());
            //企业认证 → 企业认证信息
            SignatureOrganizationDO signatureOrganization = signatureOrganizationAuthFlowDO.getSignatureOrganization();
            List<SignatureOrganizationAuthDO> signatureOrganizationAuthDOList = authorizedInfoList.stream().map(authorizedInfo -> {
                SignatureOrganizationAuthDO signatureOrganizationAuthDO = new SignatureOrganizationAuthDO();
                signatureOrganizationAuthDO.setAuthorizedScope(authorizedInfo.getAuthorizedScope());
                signatureOrganizationAuthDO.setEffectiveTime(authorizedInfo.getEffectiveTime());
                signatureOrganizationAuthDO.setExpireTime(authorizedInfo.getExpireTime());
                signatureOrganizationAuthDO.setCreateTime(System.currentTimeMillis());
                signatureOrganizationAuthDO.setSignatureOrganization(signatureOrganization);
                return signatureOrganizationAuthDO;
            }).collect(Collectors.toList());
            //企业认证 → 企业授权信息(如果存在旧数据，进行清除)
            List<SignatureOrganizationAuthDO> signatureOrganizationAuthList = signatureOrganization.getSignatureOrganizationAuthList();
            if(!CollectionUtils.isEmpty(signatureOrganizationAuthList)){
                signatureOrganizationAuthList.clear();
            }else{
                signatureOrganizationAuthList = new ArrayList<>();
            }
            //调用了clear()方法，不能用set，换成addAll()
            signatureOrganizationAuthList.addAll(signatureOrganizationAuthDOList);
            signatureOrganizationAuthFlowRepository.saveAndFlush(signatureOrganizationAuthFlowDO);
        }

        //个人认证
        SignaturePersonalAuthFlowDO signaturePersonalAuthFlowDO = signaturePersonalAuthFlowRepository.findByAuthFlowIdAndStatus(authFlowId, SignatureAuthStateEnum.APPLYING.getCode());
        if(Objects.nonNull(signaturePersonalAuthFlowDO)){
            signaturePersonalAuthFlowDO.setStatus(SignatureAuthStateEnum.APPROVE.getCode());

            //个人认证信息
            SignaturePersonalDO signaturePersonal = signaturePersonalAuthFlowDO.getSignaturePersonal();
            signaturePersonal.setPsnId(psnId);

            //个人认证信息 → 个人授权信息
            List<SignaturePersonalAuthDO> signaturePersonalAuthDOList = authorizedInfoList.stream().map(authorizedInfo -> {
                SignaturePersonalAuthDO signaturePersonalAuthDO = new SignaturePersonalAuthDO();
                signaturePersonalAuthDO.setAuthorizedScope(authorizedInfo.getAuthorizedScope());
                signaturePersonalAuthDO.setEffectiveTime(authorizedInfo.getEffectiveTime());
                signaturePersonalAuthDO.setExpireTime(authorizedInfo.getExpireTime());
                signaturePersonalAuthDO.setCreateTime(System.currentTimeMillis());
                signaturePersonalAuthDO.setSignaturePersonal(signaturePersonal);
                return signaturePersonalAuthDO;
            }).collect(Collectors.toList());
            //个人认证信息 → 个人授权信息(如果存在旧数据，进行清除)
            List<SignaturePersonalAuthDO> signaturePersonalAuthList = signaturePersonal.getSignaturePersonalAuthList();
            if(!CollectionUtils.isEmpty(signaturePersonalAuthList)){
                //调用了clear()方法，不能用set，换成addAll()
                signaturePersonalAuthList.clear();
                signaturePersonalAuthList.addAll(signaturePersonalAuthDOList);
            }else {
                signaturePersonal.setSignaturePersonalAuthList(signaturePersonalAuthDOList);
            }

            signaturePersonalAuthFlowRepository.saveAndFlush(signaturePersonalAuthFlowDO);
        }

        return Boolean.TRUE;
    }

    /**
     * 电子签章认证详情
     * @param sysUser 登录用户信息
     * @return 返回结果
     */
    @Override
    public SignatureAuthResp getSignatureDetail(UserLoginCacheDTO sysUser) {
        SignatureDO signatureDO = signatureRepository.findByMemberId(sysUser.getMemberId());

        //判断是否认证过
        if (Objects.isNull(signatureDO)) {
            throw new BusinessException(ResponseCodeEnum.CONTRACT_SIGNATURE_NOT_FOUND);
        }

        SignatureAuthResp signatureAuthResp = new SignatureAuthResp();

        if (MemberTypeEnum.MERCHANT.getCode().equals(sysUser.getMemberType())) {
            // 企业认证
            SignatureOrganizationAuthFlowDO signatureOrganizationAuthFlowDO = signatureDO.getSignatureOrganizationAuthFlow();
            if (!Objects.isNull(signatureOrganizationAuthFlowDO)) {
                //机构信息
                SignatureOrganizationDO signatureOrganizationDO = signatureOrganizationAuthFlowDO.getSignatureOrganization();
                SignatureAuthResp.OrganizationRep organizationRep = new SignatureAuthResp.OrganizationRep();
                organizationRep.setOrgName(signatureOrganizationDO.getOrgName());
                organizationRep.setOrgIDCardType(signatureOrganizationDO.getOrgIDCardType());
                organizationRep.setOrgIDCardNum(signatureOrganizationDO.getOrgIDCardNum());
                organizationRep.setLegalRepName(signatureOrganizationDO.getLegalRepName());
                organizationRep.setLegalRepIDCardType(signatureOrganizationDO.getLegalRepIDCardType());
                organizationRep.setLegalRepIdCardNum(signatureOrganizationDO.getLegalRepIDCardNum());
                organizationRep.setLegalRepMobile(signatureOrganizationDO.getLegalRepMobile());
                organizationRep.setAuthUrl(signatureOrganizationAuthFlowDO.getAuthUrl());
                organizationRep.setStatus(signatureOrganizationAuthFlowDO.getStatus());
                //机构信息 → 授权信息
                List<SignatureOrganizationAuthDO> signatureOrganizationAuthList = signatureOrganizationDO.getSignatureOrganizationAuthList();
                List<SignatureAuthResp.OrganizationAuthRep> organizationAuthRepList = signatureOrganizationAuthList.stream().map(signaturePersonalAuthDO -> {
                    SignatureAuthResp.OrganizationAuthRep signatureAuthRep = new SignatureAuthResp.OrganizationAuthRep();
                    signatureAuthRep.setEffectiveTime(signaturePersonalAuthDO.getEffectiveTime());
                    signatureAuthRep.setExpireTime(signaturePersonalAuthDO.getExpireTime());
                    signatureAuthRep.setAuthorizedScope(OrganizationAuthorizedScopeEnum.getDesc(signaturePersonalAuthDO.getAuthorizedScope()));
                    return signatureAuthRep;
                }).collect(Collectors.toList());
                organizationRep.setOrganizationAuthList(organizationAuthRepList);

                //机构信息 → 经办人
                List<SignatureTransactorDO> signatureTransactorList = signatureOrganizationAuthFlowDO.getSignatureTransactorList();
                if(!CollectionUtils.isEmpty(signatureTransactorList)){
                    List<SignatureAuthResp.TransactorResp> transactorRespList = signatureTransactorList.stream().map(signatureTransactorDO -> {
                        SignatureAuthResp.TransactorResp transactorResp = new SignatureAuthResp.TransactorResp();
                        transactorResp.setTransactorName(signatureTransactorDO.getSignaturePersonal().getPsnName());
                        transactorResp.setTransactorIDCardType(signatureTransactorDO.getSignaturePersonal().getPsnIDCardType());
                        transactorResp.setTransactorIdCardNum(signatureTransactorDO.getSignaturePersonal().getPsnIDCardNum());
                        transactorResp.setTransactorMobile(signatureTransactorDO.getSignaturePersonal().getPsnMobile());
                        transactorResp.setTransactorBankCardNum(signatureTransactorDO.getSignaturePersonal().getPsnBankCardNum());
                        return transactorResp;
                    }).collect(Collectors.toList());
                    organizationRep.setTransactorList(transactorRespList);
                }

                signatureAuthResp.setIsPersonal(Boolean.FALSE);
                signatureAuthResp.setOrganization(organizationRep);
            }
        } else if (MemberTypeEnum.MERCHANT_PERSONAL.getCode().equals(sysUser.getMemberType())){
            SignaturePersonalAuthFlowDO signaturePersonalAuthFlowDO = signatureDO.getSignaturePersonalAuthFlow();
            if(Objects.nonNull(signaturePersonalAuthFlowDO)){
                // 个人认证
                SignaturePersonalDO signaturePersonalDO = signaturePersonalAuthFlowDO.getSignaturePersonal();
                if (!Objects.isNull(signaturePersonalDO)) {
                    //个人信息
                    SignatureAuthResp.PersonalRep personalRep = new SignatureAuthResp.PersonalRep();
                    personalRep.setStatus(signaturePersonalAuthFlowDO.getStatus());
                    personalRep.setPsnId(signaturePersonalDO.getPsnId());
                    personalRep.setPsnAccount(signaturePersonalDO.getPsnAccount());
                    personalRep.setPsnName(signaturePersonalDO.getPsnName());
                    personalRep.setPsnMobile(signaturePersonalDO.getPsnMobile());
                    personalRep.setPsnIDCardType(signaturePersonalDO.getPsnIDCardType());
                    personalRep.setPsnIDCardNum(signaturePersonalDO.getPsnIDCardNum());
                    personalRep.setAuthUrl(signaturePersonalAuthFlowDO.getAuthUrl());
                    //授权信息
                    List<SignaturePersonalAuthDO> signaturePersonalAuthDOList = signaturePersonalDO.getSignaturePersonalAuthList();
                    List<SignatureAuthResp.PersonalAuthRep> personalAuthRepList = signaturePersonalAuthDOList.stream().map(signaturePersonalAuthDO -> {
                        SignatureAuthResp.PersonalAuthRep personalAuthRep = new SignatureAuthResp.PersonalAuthRep();
                        personalAuthRep.setEffectiveTime(signaturePersonalAuthDO.getEffectiveTime());
                        personalAuthRep.setExpireTime(signaturePersonalAuthDO.getExpireTime());
                        personalAuthRep.setAuthorizedScope(PersonalAuthorizedScopeEnum.getDesc(signaturePersonalAuthDO.getAuthorizedScope()));
                        return personalAuthRep;
                    }).collect(Collectors.toList());
                    personalRep.setPersonalAuthList(personalAuthRepList);

                    signatureAuthResp.setIsPersonal(Boolean.TRUE);
                    signatureAuthResp.setPersonal(personalRep);
                }
            }
        }

        return signatureAuthResp;
    }

    /**
     * 电子签章查询认证状态
     * @param sysUser 登录用户信息
     * @return 返回结果
     */
    @Override
    public Boolean getAuthStatus(UserLoginCacheDTO sysUser) {
        boolean status = Boolean.FALSE;

        //根据当前登录账号所属会员查询
        SignatureDO signatureDO = signatureRepository.findByMemberId(sysUser.getMemberId());
        if(Objects.isNull(signatureDO)){
            return false;
        }

        //企业认证
        SignatureOrganizationAuthFlowDO signatureOrganizationAuthFlow = signatureDO.getSignatureOrganizationAuthFlow();
        if(Objects.nonNull(signatureOrganizationAuthFlow) && SignatureAuthStateEnum.APPROVE.getCode().equals(signatureOrganizationAuthFlow.getStatus())){
            status = Boolean.TRUE;
        }

        //个人认证
        SignaturePersonalAuthFlowDO signaturePersonalAuthFlow = signatureDO.getSignaturePersonalAuthFlow();
        if(Objects.nonNull(signaturePersonalAuthFlow) && SignatureAuthStateEnum.APPROVE.getCode().equals(signaturePersonalAuthFlow.getStatus())){
            status = Boolean.TRUE;
        }

        return status;
    }
}