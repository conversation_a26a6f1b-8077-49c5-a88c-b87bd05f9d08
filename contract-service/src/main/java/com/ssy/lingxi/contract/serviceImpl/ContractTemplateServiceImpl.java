package com.ssy.lingxi.contract.serviceImpl;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.FileUtil;
import com.ssy.lingxi.component.base.util.HttpUtil;
import com.ssy.lingxi.contract.api.model.resp.ContractTemplateResp;
import com.ssy.lingxi.contract.constant.ContractConstant;
import com.ssy.lingxi.contract.entity.do_.ContractTemplateDO;
import com.ssy.lingxi.contract.enums.ContractFileSuffixEnum;
import com.ssy.lingxi.contract.enums.sign.ContractTemplateStateEnum;
import com.ssy.lingxi.contract.model.dto.template.ContractParamFillDTO;
import com.ssy.lingxi.contract.model.req.sign.ContractTemplateListDataReq;
import com.ssy.lingxi.contract.model.req.sign.ContractTemplateReq;
import com.ssy.lingxi.contract.model.req.sign.ContractTemplateStateReq;
import com.ssy.lingxi.contract.model.resp.sign.ContractTemplateExampleResp;
import com.ssy.lingxi.contract.model.resp.sign.ContractTemplateListResp;
import com.ssy.lingxi.contract.model.resp.sign.ContractTemplateSelectResp;
import com.ssy.lingxi.contract.model.resp.sign.PreviewContractTemplateResp;
import com.ssy.lingxi.contract.repository.ContractTemplateRepository;
import com.ssy.lingxi.contract.service.IContractTemplateService;
import com.ssy.lingxi.contract.service.IFeignService;
import com.ssy.lingxi.contract.util.TemplateFillUtil;
import com.ssy.lingxi.support.api.model.dto.FillDataDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 合同模板服务实现类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/10
 */
@Slf4j
@Service
public class ContractTemplateServiceImpl implements IContractTemplateService {

    @Resource
    private ContractTemplateRepository contractTemplateRepository;
    @Resource
    private IFeignService feignService;

    @Override
    public PageDataResp<ContractTemplateListResp> pageContractTemplate(ContractTemplateListDataReq request, UserLoginCacheDTO sysUser) {
        Pageable page = PageRequest.of(request.getCurrent() - 1, request.getPageSize(), Sort.by("id").ascending());

        Page<ContractTemplateDO> pageList = contractTemplateRepository.findAll((Specification<ContractTemplateDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            predicateList.add(cb.equal(root.get("memberId"), sysUser.getMemberId()));
            if (Objects.nonNull(request.getName())) {
                predicateList.add(cb.like(root.get("name"), "%" + request.getName() + "%"));
            }
            return query.where(predicateList.toArray(new Predicate[0])).getRestriction();
        }, page);

        List<ContractTemplateListResp> resultList = pageList.stream().map(e -> {
            ContractTemplateListResp contractTemplateListResp = new ContractTemplateListResp();
            contractTemplateListResp.setId(e.getId());
            contractTemplateListResp.setName(e.getName());
            contractTemplateListResp.setVersion(e.getVersion());
            contractTemplateListResp.setDescription(e.getDescription());
            contractTemplateListResp.setState(e.getState());
            return contractTemplateListResp;
        }).collect(Collectors.toList());

        return new PageDataResp<>(pageList.getTotalElements(), resultList);
    }

    @Override
    public ContractTemplateResp getContractTemplate(CommonIdReq request) {
        ContractTemplateDO contractTemplateDO = contractTemplateRepository.findById(request.getId()).orElse(null);
        if (Objects.isNull(contractTemplateDO)) {
            throw new BusinessException(ResponseCodeEnum.CONTRACT_DATA_RECORDS_DOES_NOT_EXIST);
        }

        ContractTemplateResp contractTemplateResp = new ContractTemplateResp();
        contractTemplateResp.setId(contractTemplateDO.getId());
        contractTemplateResp.setName(contractTemplateDO.getName());
        contractTemplateResp.setVersion(contractTemplateDO.getVersion());
        contractTemplateResp.setDescription(contractTemplateDO.getDescription());
        contractTemplateResp.setState(contractTemplateDO.getState());
        contractTemplateResp.setFileUrl(contractTemplateDO.getFileUrl());
        contractTemplateResp.setFileName(contractTemplateDO.getFileName());
        return contractTemplateResp;
    }

    @Override
    public ContractTemplateResp getContractTemplate(CommonIdReq request, UserLoginCacheDTO sysUser) {
        ContractTemplateDO contractTemplateDO = contractTemplateRepository.findByIdAndMemberId(request.getId(), sysUser.getMemberId()).orElse(null);
        if (Objects.isNull(contractTemplateDO)) {
            throw new BusinessException(ResponseCodeEnum.CONTRACT_DATA_RECORDS_DOES_NOT_EXIST);
        }

        ContractTemplateResp contractTemplateResp = new ContractTemplateResp();
        contractTemplateResp.setId(contractTemplateDO.getId());
        contractTemplateResp.setName(contractTemplateDO.getName());
        contractTemplateResp.setVersion(contractTemplateDO.getVersion());
        contractTemplateResp.setDescription(contractTemplateDO.getDescription());
        contractTemplateResp.setState(contractTemplateDO.getState());
        contractTemplateResp.setFileUrl(contractTemplateDO.getFileUrl());
        contractTemplateResp.setFileName(contractTemplateDO.getFileName());

        return contractTemplateResp;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addContractTemplate(ContractTemplateReq request, UserLoginCacheDTO sysUser) {
        // 保存合同模板信息
        ContractTemplateDO contractTemplateDO = new ContractTemplateDO();
        contractTemplateDO.setName(request.getName());
        contractTemplateDO.setVersion(request.getVersion());
        contractTemplateDO.setDescription(request.getDescription());
        contractTemplateDO.setState(ContractTemplateStateEnum.ENABLE.getCode());
        contractTemplateDO.setFileName(request.getFileName());
        contractTemplateDO.setFileUrl(request.getFileUrl());
        contractTemplateDO.setMemberId(sysUser.getMemberId());
        contractTemplateDO.setRoleId(sysUser.getMemberRoleId());
        contractTemplateDO.setCreateTime(System.currentTimeMillis());

        contractTemplateRepository.saveAndFlush(contractTemplateDO);

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateContractTemplate(ContractTemplateReq request, UserLoginCacheDTO sysUser) {
        ContractTemplateDO contractTemplateDO = contractTemplateRepository.findByIdAndMemberId(request.getId(), sysUser.getMemberId()).orElse(null);
        if (Objects.isNull(contractTemplateDO)) {
            throw new BusinessException(ResponseCodeEnum.CONTRACT_DATA_RECORDS_DOES_NOT_EXIST);
        }

        contractTemplateDO.setName(request.getName());
        contractTemplateDO.setVersion(request.getVersion());
        contractTemplateDO.setDescription(request.getDescription());
        contractTemplateDO.setFileUrl(request.getFileUrl());
        contractTemplateDO.setFileName(request.getFileName());

        contractTemplateRepository.saveAndFlush(contractTemplateDO);

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateStatusContractTemplate(ContractTemplateStateReq request, UserLoginCacheDTO sysUser) {
        ContractTemplateDO contractTemplateDO = contractTemplateRepository.findByIdAndMemberId(request.getId(), sysUser.getMemberId()).orElse(null);
        if (Objects.isNull(contractTemplateDO)) {
            throw new BusinessException(ResponseCodeEnum.CONTRACT_DATA_RECORDS_DOES_NOT_EXIST);
        }

        if (StringUtils.isBlank(contractTemplateDO.getFileUrl())) {
            throw new BusinessException(ResponseCodeEnum.CONTRACT_READ_CONTRACT_FAIL);
        }

        contractTemplateDO.setState(request.getState());
        contractTemplateRepository.saveAndFlush(contractTemplateDO);

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteContractTemplate(CommonIdReq request, UserLoginCacheDTO sysUser) {
        ContractTemplateDO contractTemplateDO = contractTemplateRepository.findByIdAndMemberId(request.getId(), sysUser.getMemberId()).orElse(null);
        if (Objects.isNull(contractTemplateDO)) {
            throw new BusinessException(ResponseCodeEnum.CONTRACT_DATA_RECORDS_DOES_NOT_EXIST);
        }

        if (ContractTemplateStateEnum.ENABLE.getCode().equals(contractTemplateDO.getState())) {
            throw new BusinessException(ResponseCodeEnum.CONTRACT_STATUS_MISMATCH);
        }

        contractTemplateRepository.deleteById(contractTemplateDO.getId());

    }

    @Override
    public List<ContractTemplateSelectResp> listContractTemplate(UserLoginCacheDTO sysUser) {
        List<ContractTemplateDO> all = contractTemplateRepository.findAllByMemberIdAndState(sysUser.getMemberId(), ContractTemplateStateEnum.ENABLE.getCode(), Sort.by("id").descending());
        return all.stream().map(e -> {
            ContractTemplateSelectResp contractTemplateSelectResp = new ContractTemplateSelectResp();
            contractTemplateSelectResp.setId(e.getId());
            contractTemplateSelectResp.setName(e.getName());
            contractTemplateSelectResp.setVersion(e.getVersion());
            return contractTemplateSelectResp;
        }).collect(Collectors.toList());

    }

    /**
     * 合同模板文件预览
     * @param request 请求参数
     * @return 返回结果
     */
    @Override
    public PreviewContractTemplateResp previewContractTemplate(CommonIdReq request, UserLoginCacheDTO sysUser) {
        ContractTemplateDO contractTemplateDO = contractTemplateRepository.findByIdAndMemberId(request.getId(), sysUser.getMemberId()).orElse(null);
        if (Objects.isNull(contractTemplateDO)) {
            throw new BusinessException(ResponseCodeEnum.CONTRACT_DATA_RECORDS_DOES_NOT_EXIST);
        }

        // 文档填充并转换
        ContractParamFillDTO paramFillBO = ContractParamFillDTO.build();
        FillDataDTO fillDataDTO = TemplateFillUtil.fillContractTemplate(contractTemplateDO.getFileUrl(), paramFillBO);
        byte[] convertDocBytes = feignService.fillAndConvert(fillDataDTO, ContractFileSuffixEnum.DOC.getCode(), ContractFileSuffixEnum.PDF.getCode());

        PreviewContractTemplateResp response = new PreviewContractTemplateResp();
        response.setContractName(contractTemplateDO.getFileName());
        response.setContractBase64(ContractFileSuffixEnum.PDF.getBase64Prefix() + FileUtil.toBase64(convertDocBytes));

        return response;
    }

    /**
     * 合同模板文件下载
     * @param request 请求参数
     */
    @Override
    public void downloadContractTemplate(HttpServletResponse servletResponse, CommonIdReq request, UserLoginCacheDTO sysUser) {
        ContractTemplateDO contractTemplateDO = contractTemplateRepository.findByIdAndMemberId(request.getId(), sysUser.getMemberId()).orElse(null);
        if (Objects.isNull(contractTemplateDO)) {
            throw new BusinessException(ResponseCodeEnum.CONTRACT_DATA_RECORDS_DOES_NOT_EXIST);
        }

        //根据url下载文件
        byte[] bytes = FileUtil.fileDownloadByDecodeUrl(contractTemplateDO.getFileUrl());
        //将文件写到浏览器
        HttpUtil.write(servletResponse, bytes, contractTemplateDO.getFileName());
    }

    /**
     * 合同模板样例预览
     */
    @Override
    public ContractTemplateExampleResp examplePreview() {
        //读取resources下的文件
        byte[] fileBytes = FileUtil.readResourcesFileBytes(ContractConstant.FILE_PATH + File.separator + ContractConstant.CONTRACT_TEMPLATE_EXAMPLE_FILE);
        // word转pdf
        byte[] convertFileBytes = feignService.convert(fileBytes, ContractFileSuffixEnum.DOC.getCode(), ContractFileSuffixEnum.PDF.getCode());
        //pdf转Base64
        ContractTemplateExampleResp response = new ContractTemplateExampleResp();
        response.setContractName(ContractConstant.CONTRACT_TEMPLATE_EXAMPLE_FILE_NAME);
        response.setContractBase64(ContractFileSuffixEnum.PDF.getBase64Prefix() + FileUtil.toBase64(convertFileBytes));
        return response;
    }

    /**
     * 合同模板样例下载
     */
    @Override
    public void exampleDownload(HttpServletResponse servletResponse) {
        //读取resources下的文件
        InputStream inputStream = FileUtil.readResourcesFile(ContractConstant.FILE_PATH + File.separator + ContractConstant.CONTRACT_TEMPLATE_EXAMPLE_FILE);
        //文件流转成字节码
        byte[] bytes = FileUtil.inputStreamToBytes(inputStream);
        //将文件写到浏览器
        HttpUtil.write(servletResponse, bytes, ContractConstant.CONTRACT_TEMPLATE_EXAMPLE_FILE_NAME);
    }
}