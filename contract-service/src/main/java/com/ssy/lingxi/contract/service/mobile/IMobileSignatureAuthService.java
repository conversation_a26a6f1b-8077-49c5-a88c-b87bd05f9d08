package com.ssy.lingxi.contract.service.mobile;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.contract.model.resp.sign.MobileSignatureAuthStateResp;

/**
 * 电子签章认证服务类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/9/1
 */
public interface IMobileSignatureAuthService {

    /**
     * 电子签章查询认证状态
     * @param sysUser 登录用户信息
     * @return 返回结果
     */
    MobileSignatureAuthStateResp getState(UserLoginCacheDTO sysUser);

}
