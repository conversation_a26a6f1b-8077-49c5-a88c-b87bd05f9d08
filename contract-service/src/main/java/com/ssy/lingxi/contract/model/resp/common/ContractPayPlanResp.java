package com.ssy.lingxi.contract.model.resp.common;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 合同付款计划VO
 * <AUTHOR>
 * @since 2021/2/7
 * @version 2.0.0
 */
public class ContractPayPlanResp implements Serializable {
    private static final long serialVersionUID = 2576520129715684526L;

    /**
     * 计划id
     */
    private Long id;

    /**
     * 付款次数
     */
    private Integer payNum;

    /**
     * 付款阶段
     */
    private String payStage;

    /**
     * 预计付款时间(yyyy-MM-dd)
     */
    private String expectPayTime;

    /**
     * 付款比率
     */
    private BigDecimal payRatio;

    /**
     * 付款金额
     */
    private BigDecimal payAmount;

    /**
     * 付款方式
     */
    private Integer payWay;

    /**
     * 付款方式名称
     */
    private String payWayName;

    /**
     * 付款参数（账期天数/请款日）
     */
    private Integer payParam;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getPayNum() {
        return payNum;
    }

    public void setPayNum(Integer payNum) {
        this.payNum = payNum;
    }

    public String getPayStage() {
        return payStage;
    }

    public void setPayStage(String payStage) {
        this.payStage = payStage;
    }

    public String getExpectPayTime() {
        return expectPayTime;
    }

    public void setExpectPayTime(String expectPayTime) {
        this.expectPayTime = expectPayTime;
    }

    public BigDecimal getPayRatio() {
        return payRatio;
    }

    public void setPayRatio(BigDecimal payRatio) {
        this.payRatio = payRatio;
    }

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    public Integer getPayWay() {
        return payWay;
    }

    public void setPayWay(Integer payWay) {
        this.payWay = payWay;
    }

    public String getPayWayName() {
        return payWayName;
    }

    public void setPayWayName(String payWayName) {
        this.payWayName = payWayName;
    }

    public Integer getPayParam() {
        return payParam;
    }

    public void setPayParam(Integer payParam) {
        this.payParam = payParam;
    }
}
