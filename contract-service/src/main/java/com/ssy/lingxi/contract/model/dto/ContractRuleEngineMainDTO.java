package com.ssy.lingxi.contract.model.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 合同配置规则对象
 * <AUTHOR>
 * @email <EMAIL>
 * @since 2022/6/10 15:49
 */
@Getter
@Setter
public class ContractRuleEngineMainDTO implements Serializable {
    private static final long serialVersionUID = -95270969292487924L;

    /**
     * 供应商
     */
    private ContractRuleEngineVendorDTO vendor;

    /**
     * 物料
     */
    private ContractRuleEngineMaterielDTO materiel;

    /**
     * 品类名称
     */
    private String category;

    /**
     * 合同总金额
     */
    private BigDecimal amount;

    /**
     * 合同到期日期 yyyy-MM-dd
     */
    private Long endTime;
}
