package com.ssy.lingxi.contract.serviceImpl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.contract.ContractSourceTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.contract.api.enums.CurrencyTypeEnum;
import com.ssy.lingxi.contract.entity.do_.ContractDO;
import com.ssy.lingxi.contract.enums.ContractOuterStatusEnum;
import com.ssy.lingxi.contract.model.req.execute.ContractExecutePartyAPageDataReq;
import com.ssy.lingxi.contract.model.req.execute.ContractExecutePartyBPageDataReq;
import com.ssy.lingxi.contract.model.resp.common.ContractPayPlanResp;
import com.ssy.lingxi.contract.model.resp.common.PageItemResp;
import com.ssy.lingxi.contract.model.resp.execute.ContractExecuteBasicsResp;
import com.ssy.lingxi.contract.model.resp.execute.ContractExecuteDetailResp;
import com.ssy.lingxi.contract.model.resp.execute.ContractExecuteQueryResp;
import com.ssy.lingxi.contract.model.resp.manage.ContractVersionResp;
import com.ssy.lingxi.contract.repository.ContractRepository;
import com.ssy.lingxi.contract.service.IContractExecuteService;
import com.ssy.lingxi.contract.service.IContractPayPlanService;
import com.ssy.lingxi.contract.service.IFeignService;
import com.ssy.lingxi.settlement.api.model.req.ApplyAmountDetailTotalSunQueryReq;
import com.ssy.lingxi.settlement.api.model.resp.ApplyAmountDetailTotalSunResp;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 合同执行接口实现
 * <AUTHOR>
 * @since 2021/2/2
 * @version 2.0.0
 */
@Service
public class ContractExecuteServiceImpl implements IContractExecuteService {

    @Resource
    private ContractRepository repository;

    @Resource
    private IContractPayPlanService payPlanService;

    @Resource
    private IFeignService feignService;

    /**
     * 甲方分页查询合同列表
     * <AUTHOR>
     * @since 2021/2/2
     * @param user: 当前登录用户
     * @param pageVO: 分页请求参数
     * @return 操作结果
     **/
    @Override
    public PageDataResp<ContractExecuteQueryResp> pageListByPartyA(UserLoginCacheDTO user, ContractExecutePartyAPageDataReq pageVO) {

        List<Integer> outerStatusList = new ArrayList<>();
        outerStatusList.add(ContractOuterStatusEnum.COMPLETE_SIGN_CONTRACT.getCode());
        outerStatusList.add(ContractOuterStatusEnum.INVALID.getCode());
        outerStatusList.add(ContractOuterStatusEnum.STOP_USING.getCode());
        outerStatusList.add(ContractOuterStatusEnum.EXPIRE.getCode());

        // 组装查询条件
        Specification<ContractDO> spec =  (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("partyAMemberId").as(Long.class), user.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("partyARoleId").as(Long.class), user.getMemberRoleId()));
            if (!ContractOuterStatusEnum.ALL.getCode().equals(pageVO.getOuterStatus())) {
                list.add(criteriaBuilder.equal(root.get("outerStatus").as(Integer.class), pageVO.getOuterStatus()));
            } else {
                list.add(criteriaBuilder.in(root.get("outerStatus")).value(outerStatusList));
            }
            if (StringUtils.hasLength(pageVO.getStartTime())) {
                list.add(criteriaBuilder.greaterThan(root.get("startTime").as(Long.class), DateUtil.parse(pageVO.getStartTime()).getTime()));
            }
            if (StringUtils.hasLength(pageVO.getEndTime())) {
                list.add(criteriaBuilder.lessThan(root.get("endTime").as(Long.class), DateUtil.parse(pageVO.getEndTime()).getTime()));
            }
            if (StringUtils.hasLength(pageVO.getContractNo())) {
                list.add(criteriaBuilder.like(root.get("contractNo").as(String.class), "%" + pageVO.getContractNo().trim() + "%"));
            }
            if (StringUtils.hasLength(pageVO.getContractAbstract())) {
                list.add(criteriaBuilder.like(root.get("contractAbstract").as(String.class), "%" + pageVO.getContractAbstract().trim() + "%"));
            }
            if (StringUtils.hasLength(pageVO.getPartyBName())) {
                list.add(criteriaBuilder.like(root.get("partyBName").as(String.class), "%" + pageVO.getPartyBName().trim() + "%"));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        // 组装分页参数
        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("startTime").descending());

        Page<ContractDO> result = repository.findAll(spec, page);
        return new PageDataResp<>(result.getTotalElements(), convertContractExecuteQueryVO(result.getContent()));
    }

    /**
     * 乙方分页查询合同列表
     * <AUTHOR>
     * @since 2021/2/2
     * @param user: 当前登录用户
     * @param pageVO: 分页请求参数
     * @return 操作结果
     **/
    @Override
    public PageDataResp<ContractExecuteQueryResp> pageListByPartyB(UserLoginCacheDTO user, ContractExecutePartyBPageDataReq pageVO) {
        List<Integer> outerStatusList = new ArrayList<>();
        outerStatusList.add(ContractOuterStatusEnum.COMPLETE_SIGN_CONTRACT.getCode());
        outerStatusList.add(ContractOuterStatusEnum.STOP_USING.getCode());
        outerStatusList.add(ContractOuterStatusEnum.EXPIRE.getCode());

        // 组装查询条件
        Specification<ContractDO> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("partyBMemberId").as(Long.class), user.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("partyBRoleId").as(Long.class), user.getMemberRoleId()));
            if (!ContractOuterStatusEnum.ALL.getCode().equals(pageVO.getOuterStatus())) {
                list.add(criteriaBuilder.equal(root.get("outerStatus").as(Integer.class), pageVO.getOuterStatus()));
            } else {
                list.add(criteriaBuilder.in(root.get("outerStatus")).value(outerStatusList));
            }
            if (StringUtils.hasLength(pageVO.getStartTime())) {
                list.add(criteriaBuilder.greaterThan(root.get("startTime").as(Long.class), DateUtil.parse(pageVO.getStartTime()).getTime()));
            }
            if (StringUtils.hasLength(pageVO.getEndTime())) {
                list.add(criteriaBuilder.lessThan(root.get("endTime").as(Long.class), DateUtil.parse(pageVO.getEndTime()).getTime()));
            }
            if (StringUtils.hasLength(pageVO.getContractNo())) {
                list.add(criteriaBuilder.like(root.get("contractNo").as(String.class), "%" + pageVO.getContractNo().trim() + "%"));
            }
            if (StringUtils.hasLength(pageVO.getContractAbstract())) {
                list.add(criteriaBuilder.like(root.get("contractAbstract").as(String.class), "%" + pageVO.getContractAbstract().trim() + "%"));
            }
            if (StringUtils.hasLength(pageVO.getPartyAName())) {
                list.add(criteriaBuilder.like(root.get("partyAName").as(String.class), "%" + pageVO.getPartyAName().trim() + "%"));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        // 组装分页参数
        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("startTime").descending());

        Page<ContractDO> result = repository.findAll(spec, page);
        return new PageDataResp<>(result.getTotalElements(), convertContractExecuteQueryVO(result.getContent()));
    }

    /**
     * 转换执行合同返回VO
     * <AUTHOR>
     * @since 2021/2/2
     * @param contractList: 合同列表
     * @return 操作结果
     **/
    private List<ContractExecuteQueryResp> convertContractExecuteQueryVO(List<ContractDO> contractList) {
        // step1: 查询结算服务请款使用请款
        List<Long> contractIds = contractList.stream().map(ContractDO::getId).collect(Collectors.toList());
        List<ApplyAmountDetailTotalSunResp> helpList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(contractIds)) {
            ApplyAmountDetailTotalSunQueryReq queryVO = new ApplyAmountDetailTotalSunQueryReq();
            queryVO.setContractIds(contractIds);
            List<ApplyAmountDetailTotalSunResp> helpWrapperResp =  feignService.pageContractExecuteDetailSum(queryVO);
            if (CollUtil.isEmpty(helpWrapperResp)) {
                helpList.addAll(helpWrapperResp);
            }
        }
        return contractList.stream().map(o -> {
            ContractExecuteQueryResp entity = new ContractExecuteQueryResp();
            entity.setId(o.getId());
            entity.setContractNo(o.getContractNo());
            entity.setContractAbstract(o.getContractAbstract());
            entity.setStartTime(DateUtil.format(DateUtil.date(o.getStartTime()), "yyyy-MM-dd"));
            entity.setEndTime(DateUtil.format(DateUtil.date(o.getEndTime()), "yyyy-MM-dd"));
            entity.setPartyAName(o.getPartyAName());
            entity.setPartyBName(o.getPartyBName());
            entity.setTotalAmount(o.getTotalAmount());
            entity.setExecuteAmount(o.getExecuteAmount());
            Optional<ApplyAmountDetailTotalSunResp> help = helpList.stream().filter(f -> Objects.equals(f.getContractId(), o.getId())).findFirst();
            BigDecimal payAmount = help.map(ApplyAmountDetailTotalSunResp::getPayAmount).orElse(BigDecimal.ZERO);
            entity.setPayAmount(payAmount);
            BigDecimal unPayApplyAmount=help.map(ApplyAmountDetailTotalSunResp::getUnPayApplyAmount).orElse(BigDecimal.ZERO);
            entity.setUnPayApplyAmount(unPayApplyAmount);
            BigDecimal unApplyAmount = o.getExecuteAmount().subtract(payAmount).subtract(unPayApplyAmount).setScale(2, RoundingMode.HALF_UP);
            entity.setUnApplyAmount(unApplyAmount);
            entity.setOuterStatus(o.getOuterStatus());
            entity.setOuterStatusName(ContractOuterStatusEnum.getMessage(o.getOuterStatus()));
            return entity;
        }).collect(Collectors.toList());
    }

    /**
     * 获取外部状态列表
     * <AUTHOR>
     * @since 2021/2/2
     * @return 操作结果
     **/
    @Override
    public List<PageItemResp> getOuterStatusList() {

        List<PageItemResp> resultList = new ArrayList<>();
        PageItemResp item = new PageItemResp();
        item.setStatus(ContractOuterStatusEnum.ALL.getCode());
        item.setName(ContractOuterStatusEnum.ALL.getMessage());
        resultList.add(item);
        item = new PageItemResp();
        item.setStatus(ContractOuterStatusEnum.COMPLETE_SIGN_CONTRACT.getCode());
        item.setName(ContractOuterStatusEnum.COMPLETE_SIGN_CONTRACT.getMessage());
        resultList.add(item);
        item = new PageItemResp();
        item.setStatus(ContractOuterStatusEnum.STOP_USING.getCode());
        item.setName(ContractOuterStatusEnum.STOP_USING.getMessage());
        resultList.add(item);
        item = new PageItemResp();
        item.setStatus(ContractOuterStatusEnum.EXPIRE.getCode());
        item.setName(ContractOuterStatusEnum.EXPIRE.getMessage());
        resultList.add(item);

        return resultList;
    }

    /**
     * 查询合同详细
     * @param user 当前登录用户
     * @param contractId 当前合同id
     * @return 合同执行 - 查询合同执行详情时返回的VO
     */
    @Override
    public ContractExecuteDetailResp getDetail(UserLoginCacheDTO user, Long contractId) {

        ContractExecuteDetailResp result = new ContractExecuteDetailResp();

        ContractDO contract = repository.findById(contractId).orElse(null);
        if (null == contract) {
            throw new BusinessException(ResponseCodeEnum.CONTRACT_NOT_EXIST);
        }

        // 获取合同基本信息
        ContractExecuteBasicsResp basics = new ContractExecuteBasicsResp();
        BeanUtils.copyProperties(contract,basics);
        basics.setId(contractId);
        basics.setOuterStatusName(ContractOuterStatusEnum.getMessage(contract.getOuterStatus()));
        basics.setSourceTypeName(ContractSourceTypeEnum.getNameByCode(contract.getSourceType()));
        basics.setStartTime(DateUtil.format(DateUtil.date(contract.getStartTime()), "yyyy-MM-dd"));
        basics.setEndTime(DateUtil.format(DateUtil.date(contract.getEndTime()), "yyyy-MM-dd"));
        basics.setCurrencyTypeName(CurrencyTypeEnum.getMessage(contract.getCurrencyType()));
        result.setBasics(basics);
        basics.setId(contractId);

        // 获取支付计划
        List<ContractPayPlanResp> payPlanList = payPlanService.getPayPlanList(contractId);
        if (CollectionUtil.isEmpty(payPlanList)) {
            throw new BusinessException(ResponseCodeEnum.CONTRACT_GET_PAY_PLAN_ERROR);
        }
        result.setPayPlanList(payPlanList);
        // 递归查询所有合同版本
        List<ContractVersionResp> contractVersionRespList = new ArrayList<>();
        // 先查出最下级合同
        while(Objects.nonNull(contract) && Objects.nonNull(contract.getSubContractId()) && contract.getSubContractId() != 0){
            // 查出下一版合同
            contract = repository.findById(contract.getSubContractId()).orElse(null);
        }
        // 构建合同版本
        if(Objects.nonNull(contract) ) {
            contractVersionRespList.add(
                    ContractVersionResp.builder().contractId(contract.getId())
                            .contractNo(contract.getContractNo()).oldContractId(contract.getOldContractId()).subContractId(contract.getSubContractId())
                            .outerStatus(contract.getOuterStatus()).outerStatusName(ContractOuterStatusEnum.getMessage(contract.getOuterStatus()))
                            .createTime(DateUtil.format(DateUtil.date(contract.getCreateTime()), "yyyy-MM-dd HH:mm")).build()
            );
        }
        // 再从最新的合同往上查
        while(Objects.nonNull(contract) && Objects.nonNull(contract.getOldContractId()) && contract.getOldContractId() != 0){
            // 查出上一版合同
            contract = repository.findById(contract.getOldContractId()).orElse(null);
            if(Objects.nonNull(contract)){
                // 构建合同版本
                contractVersionRespList.add(
                        ContractVersionResp.builder().contractId(contract.getId())
                                .contractNo(contract.getContractNo()).oldContractId(contract.getOldContractId()).subContractId(contract.getSubContractId())
                                .outerStatus(contract.getOuterStatus()).outerStatusName(ContractOuterStatusEnum.getMessage(contract.getOuterStatus()))
                                .createTime(DateUtil.format(DateUtil.date(contract.getCreateTime()), "yyyy-MM-dd HH:mm")).build()
                );
            }

        }
        result.setContractVersionResp(contractVersionRespList);

        return result;
    }
}
