package com.ssy.lingxi.contract.serviceImpl;


import com.ssy.lingxi.common.constant.Constant;
import com.ssy.lingxi.component.esign.common.enums.SignFlowStatusEnum;
import com.ssy.lingxi.component.esign.exception.EsignException;
import com.ssy.lingxi.component.esign.model.resp.standard.SignFlowDetailResp;
import com.ssy.lingxi.component.esign.util.EsignApiUtil;
import com.ssy.lingxi.contract.entity.do_.SignatureSignLogDO;
import com.ssy.lingxi.contract.enums.sign.SignatureLogStateEnum;
import com.ssy.lingxi.contract.model.dto.SignFlowNotifyDTO;
import com.ssy.lingxi.contract.repository.SignatureLogRepository;
import com.ssy.lingxi.contract.service.ISignFlowCheckJobService;
import com.ssy.lingxi.contract.service.ISignatureService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 合同签署流程任务
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/2/23
 */
@Slf4j
@Component
public class SignFlowCheckJobServiceImpl implements ISignFlowCheckJobService {

    @Resource
    private SignatureLogRepository signatureLogRepository;
    @Resource
    private ISignatureService signatureService;
    @Resource
    private EsignApiUtil esignApiUtil;

    /**
     * 一周的时间(毫秒)
     */
    private static final long WEEK_TIME = 7 * Constant.DAY_TO_MILLISECONDS;

    public void checkSignFlow() {
        List<SignatureSignLogDO> signatureSignLogDOList = signatureLogRepository.findAll((Specification<SignatureSignLogDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            predicateList.add(cb.equal(root.get("state"), SignatureLogStateEnum.SIGNING.getCode()));
            predicateList.add(cb.greaterThan(root.get("createTime"), System.currentTimeMillis() - WEEK_TIME));

            return query.where(predicateList.toArray(new Predicate[predicateList.size()])).getRestriction();
        });
        log.info("签署流程查询: 当前有{}条数据在校验, ids:[{}]", signatureSignLogDOList.size(), signatureSignLogDOList.stream().map(e -> e.getId().toString()).collect(Collectors.joining(",")));
        for (SignatureSignLogDO signatureSignLogDO : signatureSignLogDOList) {
            String flowId = signatureSignLogDO.getFlowId();
            try {
                SignFlowDetailResp response = esignApiUtil.signFlow().signFlowDetail(flowId);

                SignFlowNotifyDTO signFlowNotifyDTO = new SignFlowNotifyDTO();
                signFlowNotifyDTO.setFlowId(response.getFlowId());
                signFlowNotifyDTO.setStatusDescription(response.getFlowDesc());

                if (SignFlowStatusEnum.FINISH.getType().equals(response.getFlowStatus())) {
                    // 流程状态为完成,即签署完成
                    signatureService.updateNotifySuccess(signFlowNotifyDTO);
                } else if (
                        SignFlowStatusEnum.CANCEL.getType().equals(response.getFlowStatus())
                                || SignFlowStatusEnum.PAST_DUE.getType().equals(response.getFlowStatus())
                                || SignFlowStatusEnum.REJECT.getType().equals(response.getFlowStatus())
                ) {
                    // 流程状态为撤销,过期,拒签,即签署失败
                    signatureService.updateNotifyFail(signFlowNotifyDTO);
                }
            } catch (EsignException e) {
                log.error("合同签署流程查询失败", e);
            }
        }
    }
}
